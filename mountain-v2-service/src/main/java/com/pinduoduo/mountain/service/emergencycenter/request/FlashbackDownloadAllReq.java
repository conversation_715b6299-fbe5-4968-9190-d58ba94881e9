/**
 * <AUTHOR>
 * @date 2025/4/20
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FlashbackDownloadAllReq {
    @ApiModelProperty(value ="cate, origin/rollback")
    private String cate;

    @ApiModelProperty(value = "task id list")
    private List<Long> taskIds;
}
