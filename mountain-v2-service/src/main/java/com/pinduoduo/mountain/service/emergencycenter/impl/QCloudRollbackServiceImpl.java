/**
 * <AUTHOR>
 * @date 2025/5/20
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pinduoduo.mountain.common.thirdparty.xcloud.XcloudProxyService;
import com.pinduoduo.mountain.integration.rollback.IRollbackService;
import com.pinduoduo.mountain.integration.rollback.bo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class QCloudRollbackServiceImpl implements IRollbackService {
    private final String cloud = "QCloud";

    private final XcloudProxyService xcloudProxyService;

    public QCloudRollbackServiceImpl(XcloudProxyService xcloudProxyService) {
        this.xcloudProxyService = xcloudProxyService;
    }

    /**
     * POST / HTTP/1.1
     * Host: cdb.tencentcloudapi.com
     * Content-Type: application/json
     * X-TC-Action: DescribeRollbackRangeTime
     *
     * @param instanceIdList
     * @return
     */
    @Override
    public List<RollbackRangeTime> describeRollbackRangeTime(List<RollbackInstanceInfo> instanceIdList) {
        List<RollbackRangeTime> rollbackRangeTimeList = new ArrayList<>();
        Map<String, List<RollbackInstanceInfo>> map = new HashMap<>(16);
        for (RollbackInstanceInfo r : instanceIdList) {
            String region = r.getRegion();
            List<RollbackInstanceInfo> list = map.computeIfAbsent(region, k -> new ArrayList<>());
            list.add(r);
        }
        map.forEach((k, v) -> {
            try {

                JSONObject requestParams = new JSONObject();
                requestParams.put("Action", "DescribeRollbackRangeTime");
                requestParams.put("Version", "2017-03-20");
                requestParams.put("Region", k);
                List<String> instanceIds = new ArrayList<>();
                for (RollbackInstanceInfo rangeTimeReq : v) {
                    instanceIds.add(rangeTimeReq.getInstanceId());
                }
                requestParams.put("InstanceIds", instanceIds);

                JSONObject response = xcloudProxyService.withParams(cloud, "V3", "cdb", k).request(requestParams);
                JSONObject responseResp = response.getJSONObject("Response");

                JSONArray items = responseResp.getJSONArray("Items");
                if (items == null){
                    //
                    JSONObject errorObject = responseResp.getJSONObject("Error");
                    if (errorObject != null){
                        String code = errorObject.getString("Code");
                        String message = errorObject.getString("Message");
                        for (RollbackInstanceInfo rollbackInstanceInfo : v) {
                            rollbackRangeTimeList.add(new RollbackRangeTime(Boolean.FALSE, "error:" + code + ": "+ message, rollbackInstanceInfo.getInstanceId(), null));
                        }
                    }
                }else{
                    for (int i = 0; i < items.size(); i++) {
                    JSONObject jo = items.getJSONObject(i);
                    String instanceId = jo.getString("InstanceId");
                    String message = jo.getString("Message");
                    Integer code = jo.getInteger("Code");
                    JSONArray times = jo.getJSONArray("Times");
                    RollbackRangeTime rangeTime = new RollbackRangeTime();
                    rangeTime.setInstanceId(instanceId);
                    rangeTime.setMessage(message);
                    rangeTime.setResult(code == 0);
                        rangeTime.setRangeTimeList(new ArrayList<>());
                        for (int j = 0; j < times.size(); j++) {
                            RollbackRangeTime.RangeTime time = new RollbackRangeTime.RangeTime();
                            JSONObject timesJSONObject = times.getJSONObject(j);
                            String begin = timesJSONObject.getString("Begin");
                            String end = timesJSONObject.getString("End");
                            time.setStartTime(begin);
                            time.setEndTime(end);
                            rangeTime.getRangeTimeList().add(time);
                        }
                        rollbackRangeTimeList.add(rangeTime);
                    }
                }
            } catch (Exception e) {
                log.error("CdbRollbackServiceImpl describeRollbackRangeTime error: " + e.getMessage());
                for (RollbackInstanceInfo rollbackInstanceInfo : v) {
                     rollbackRangeTimeList.add(new RollbackRangeTime(Boolean.FALSE, "error:"+e.getMessage(), rollbackInstanceInfo.getInstanceId(), null));
                }
            }
        });

        return rollbackRangeTimeList;
    }

    @Override
    public List<RollbackTaskDetail> describeRollbackTaskDetail(List<RollbackInstanceInfo> instanceIdList) {
        List<RollbackTaskDetail> rollbackTaskDetails = new ArrayList<>();
        for (RollbackInstanceInfo instanceInfo : instanceIdList) {
            try {
                JSONObject requestParams = new JSONObject();
                requestParams.put("Action", "DescribeRollbackTaskDetail");
                requestParams.put("Version", "2017-03-20");
                requestParams.put("Region", instanceInfo.getRegion());
                requestParams.put("InstanceId", instanceInfo.getInstanceId());
                requestParams.put("Limit", 100);
                JSONObject retryResponse = xcloudProxyService.withParams(cloud, "V3", "cdb", instanceInfo.getRegion()).request(requestParams);
                JSONArray items = retryResponse.getJSONObject("Response").getJSONArray("Items");

                for (int i = 0; i < items.size(); i++) {
                    JSONObject jo = items.getJSONObject(i);
                    String info = jo.getString("Info");
                    String status = jo.getString("Status");
                    Integer progress = jo.getInteger("Progress");
                    String startTime = jo.getString("StartTime");
                    String endTime = jo.getString("EndTime");
                    JSONArray detail = jo.getJSONArray("Detail");
                    RollbackTaskDetail rollbackTaskDetail = new RollbackTaskDetail(true,
                            info, startTime, endTime, status, progress, new ArrayList<>(), instanceInfo.getInstanceId());
                    for (int j = 0; j < detail.size(); j++) {
                        JSONObject joo = detail.getJSONObject(j);
                        String instanceId = joo.getString("InstanceId");
                        String strategy = joo.getString("Strategy");
                        String rollbackTime = joo.getString("RollbackTime");
                        JSONArray ddatabases = joo.getJSONArray("Databases");
                        ArrayList<RollbackTaskDetail.RollbackInfo> databases = new ArrayList<>();
                        for (int i1 = 0; i1 < ddatabases.size(); i1++) {
                            JSONObject ji = ddatabases.getJSONObject(i1);
                            databases.add(new RollbackTaskDetail.RollbackInfo(ji.getString("DatabaseName"), ji.getString("NewDatabaseName")));
                        }
                        RollbackTaskDetail.RollbackTask rollbackTask = new RollbackTaskDetail.RollbackTask(strategy, rollbackTime, databases, new ArrayList<>());

                        JSONArray tables = joo.getJSONArray("Tables");
                        for (int k = 0; k < tables.size(); k++) {
                            JSONObject jo2 = tables.getJSONObject(k);
                            String database = jo2.getString("Database");
                            JSONArray tabless = jo2.getJSONArray("Table");
                            List<RollbackTaskDetail.RollbackInfo> _tables = new ArrayList<>();
                            for (int l = 0; l < tabless.size(); l++) {
                                JSONObject jo3 = tabless.getJSONObject(l);
                                String tableName = jo3.getString("TableName");
                                String newTableName = jo3.getString("NewTableName");
                                _tables.add(new RollbackTaskDetail.RollbackInfo(tableName, newTableName));
                            }
                            rollbackTask.getTables().add(new RollbackTaskDetail.RollbackTable(new RollbackTaskDetail.RollbackInfo(database, database), _tables));
                        }
                        rollbackTaskDetail.getTaskList().add(rollbackTask);
                    }
                    rollbackTaskDetails.add(rollbackTaskDetail);
                }
            }catch (Exception e){
                rollbackTaskDetails.add(new RollbackTaskDetail(Boolean.FALSE, "error:"+ e.getMessage(), "","","",0,null, instanceInfo.getInstanceId()));
            }
        }
        return rollbackTaskDetails;
    }

    /**
     * params = {
     *             "InstanceIds.0": InstanceId,
     *             "Method":"POST",
     *             "Action":"DescribeDBInstances",
     *             "Region":"ap-shanghai",
     *             "Version": "2017-03-20"
     *         }
     * @param rollbackReqList
     * @return
     */
    @Override
    public List<RollbackResp> batchRollback(List<RollbackBO> rollbackReqList) {
        List<RollbackResp> rollbackRespList = new ArrayList<>();
        for (RollbackBO rollbackReq : rollbackReqList) {
            try {
                // 以实例纬度提交任务
                JSONObject requestParams = new JSONObject();
                requestParams.put("Action", "StartBatchRollback");
                requestParams.put("Version", "2017-03-20");
                requestParams.put("Region", rollbackReq.getRegion());
                requestParams.put("Method", "GET");

                Map<String, String> rollbackParams = this.resolveRollbackParams(rollbackReq);
                requestParams.putAll(rollbackParams);
//                requestParams.put("Instances", rollbackReq);
                JSONObject retryResponse = xcloudProxyService.withParams(cloud, "V3", "cdb",rollbackReq.getRegion()).request(requestParams);
                JSONObject jsonObject = retryResponse.getJSONObject("Response");
                String requestId = jsonObject.getString("RequestId");
                String asyncRequestId = jsonObject.getString("AsyncRequestId");
                rollbackRespList.add(new RollbackResp(Boolean.TRUE, "success", cloud, rollbackReq.getInstanceId(), requestId, asyncRequestId));
            } catch (Exception e) {
                rollbackRespList.add(new RollbackResp(Boolean.FALSE, "error:"+ e.getMessage(), cloud, rollbackReq.getInstanceId(),"", ""));
            }
        }

        return rollbackRespList;
    }

    private Map<String, String> resolveRollbackParams(RollbackBO data) {
        Map<String, String> params = new HashMap<>(16);
        String instanceId = data.getInstanceId();
        params.put("Instances.0.InstanceId", instanceId);
        String strategy = data.getStrategy();
        params.put("Instances.0.Strategy", strategy);
        String rollbackTime = data.getRollbackTime();
        params.put("Instances.0.RollbackTime", rollbackTime);
        List<RollbackTaskDetail.RollbackTable> rollbackTables = data.getData();

        List<RollbackTaskDetail.RollbackTable> databasesRollback = new ArrayList<>();
        List<RollbackTaskDetail.RollbackTable> tablesRollback = new ArrayList<>();
        for(int i=0;i<rollbackTables.size();i++){
            RollbackTaskDetail.RollbackTable rtb = rollbackTables.get(i);
            RollbackTaskDetail.RollbackInfo database = rtb.getDatabase();
            List<RollbackTaskDetail.RollbackInfo> tables = rtb.getTables();
            if (tables ==null || tables.isEmpty()){
                databasesRollback.add(rtb);
            }else {
                tablesRollback.add(rtb);
            }
        }

        for (int i = 0; i < databasesRollback.size(); i++) {
            // 按库回档
            RollbackTaskDetail.RollbackTable rtb = databasesRollback.get(i);
            RollbackTaskDetail.RollbackInfo database = rtb.getDatabase();
//            List<RollbackTaskDetail.RollbackInfo> tables = rtb.getTables();
            params.put("Instances.0.Databases." + i + ".DatabaseName", database.getSource());
            String t = this.getTargetRollbackName(database);
            params.put("Instances.0.Databases." + i + ".NewDatabaseName", t);
        }

        for (int i = 0; i < tablesRollback.size(); i++) {
            RollbackTaskDetail.RollbackTable rtb = tablesRollback.get(i);
//            RollbackTaskDetail.RollbackInfo database = rtb.getDatabase();
            List<RollbackTaskDetail.RollbackInfo> tables = rtb.getTables();
            params.put("Instances.0.Tables." + i + ".Database", rtb.getDatabase().getSource());
            for (int i1 = 0; i1 < tables.size(); i1++) {
                RollbackTaskDetail.RollbackInfo rollbackInfo = tables.get(i1);
                params.put("Instances.0.Tables." + i + ".Table." + i1 + ".TableName", rollbackInfo.getSource());
                String t = this.getTargetRollbackName(rollbackInfo);
                params.put("Instances.0.Tables." + i + ".Table." + i1 + ".NewTableName", t);
            }

        }
        return params;
    }

    public String getTargetRollbackName(RollbackTaskDetail.RollbackInfo rollbackInfo) {
        if (StringUtils.isNotEmpty(rollbackInfo.getTarget())) {
            return rollbackInfo.getTarget();
        }
        //        表的命名我们要不给个默认值，如
        //源库为 "ies"，回档后库 "LEFT(ies,10)_rolback_${timestamp}"
        //源表位 "es_index"，回档后表位 "LEFT(es_index, 10)_rollback_${timestamp}"
        String source = rollbackInfo.getSource();
        long millis = System.currentTimeMillis();
        return  source.substring(0, 3) +"_rollback_"+ millis;
    }

    @Override
    public List<StopRollbackResp> stopRollback(List<RollbackInstanceInfo> stopRollbackReqList) {
        Map<String, List<RollbackInstanceInfo>> map = new HashMap<>(16);
        for (RollbackInstanceInfo rollbackReq : stopRollbackReqList) {
            String region = rollbackReq.getRegion();
            map.computeIfAbsent(region, k -> new ArrayList<>()).add(rollbackReq);
        }
        List<StopRollbackResp> stopRollbackRespList = new ArrayList<>();

        map.forEach((k, v) -> {
            for (RollbackInstanceInfo rollbackInstanceInfo : v) {
                JSONObject requestParams = new JSONObject();
                requestParams.put("Action", "StopRollback");
                requestParams.put("Version", "2017-03-20");
                requestParams.put("Region", k);
                requestParams.put("Method", "POST");
                requestParams.put("InstanceId", rollbackInstanceInfo.getInstanceId());
                StopRollbackResp rollbackResp = new StopRollbackResp();
                rollbackResp.setResult(Boolean.FALSE);
                try {
                    JSONObject resp = xcloudProxyService.withParams(cloud, "V3", "cdb", k).request(requestParams);
                    JSONObject jsonObject = resp.getJSONObject("Response");
                    String requestId = jsonObject.getString("RequestId");
                    String asyncRequestId = jsonObject.getString("AsyncRequestId");
                    JSONObject err = jsonObject.getJSONObject("Error");
                    if (err!=null){
                        rollbackResp.setMessage(JSONObject.toJSONString(err));
                    }
                    rollbackResp.setResult(Boolean.TRUE);
                    rollbackResp.setRequestId(requestId);
                    rollbackResp.setAsyncRequestId(asyncRequestId);
                    rollbackResp.setInstanceId(rollbackResp.getInstanceId());
                    if (asyncRequestId == null){
                        rollbackResp.setResult(Boolean.FALSE);
                    }
                } catch (Exception e) {
                    log.error("stopRollback error: " + e.getMessage());
                    rollbackResp.setMessage(e.getMessage());
                }
                stopRollbackRespList.add(rollbackResp);
            }
        });
        return stopRollbackRespList;
    }
}
