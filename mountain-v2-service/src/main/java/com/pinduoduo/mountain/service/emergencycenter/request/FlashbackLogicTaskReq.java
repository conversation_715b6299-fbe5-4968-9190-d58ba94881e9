/**
 * <AUTHOR>
 * @date 2025/3/26
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FlashbackLogicTaskReq {

    @ApiModelProperty(value = "sql type, should be in ['insert','update','delete']")
    @NotEmpty
    private List<String> sqlType;
    @ApiModelProperty(value = "logic table id list")
    @NotEmpty
    private List<Long> logicTables;
    @ApiModelProperty(value = "flashback start time yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    @NotEmpty
    private String startTime;
    @ApiModelProperty(value = "flashback end time yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull
    @NotEmpty
    private String endTime;

    private String sessionId;

    private String matchSql;

    private String executeHost;

    private List<Long> physicalClusterIds;

    private List<Long> physicalTableIds;


}
