/**
 * <AUTHOR>
 * @date 2025/3/31
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.impl;

import com.pinduoduo.mountain.common.thirdparty.panda.PandaService;
import com.pinduoduo.mountain.integration.commandexecutor.CommandExecutor;
import com.pinduoduo.mountain.integration.systemconfig.bo.ProcessResultBO;
import ddns.org.apache.commons.lang3.tuple.Triple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pdd.tiger.kafka.common.protocol.types.Field;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class PandaCommandExecutor implements CommandExecutor {

    @Autowired
    PandaService pandaService;

    @Override
    public ProcessResultBO<String> process(String traceId, String host, String command, List<String> args) {
        ProcessResultBO processResultBO = new ProcessResultBO();
        try {
            if (StringUtils.isEmpty(host)) {
                String msg = "Using pandaService to execute flash backtask should set execute_host";
                processResultBO.setCode(1);
                processResultBO.setMsg(msg);
                log.error(msg);
                return processResultBO;
            }
            Map<String, Object> params = new HashMap<>(16);
            Boolean prefix = true;
            StringBuilder commandBuilder = new StringBuilder(command);
            for (String arg : args) {
                String[] ps = arg.split("=", 2);
                String dashPrefix = ps[0].substring(0,2);
                if (ps[0].length() > 2 && StringUtils.equals(dashPrefix, "--")){
                    ps[0] = ps[0].substring(2);
                }
                if (prefix && ps.length == 1) {
                    prefix = false;
                    commandBuilder.append(" ").append(ps[0]);
                }else {
                    params.put(ps[0],"'"+ps[1]+"'");
                }
            }
            command = commandBuilder.toString();
            // flashback result file should store in `/data0/rollback-sqls/${任务id}/xxx.sql`
            // then we will encrypt these result sql into `/data0/rollback-sqls/${任务id}/.encrypted/xxx.sql`

            // As when download using panda, it will upload our file into POS, so we need to encryt it manually
            // when download, we will download the /data0/rollback-sqls/${任务id}/.encrypted/xxx.sql
            // then we will decrypt these files and transfer to user
            Triple<Integer, String, String> integerStringStringTriple = pandaService.pandaExecute(host, command, params, false);
            if (integerStringStringTriple.getLeft() != null) {
                Integer code = integerStringStringTriple.getLeft();
                processResultBO.setCode(code);

                String r = integerStringStringTriple.getRight();
                if (StringUtils.isEmpty(r)){
                     processResultBO.setMsg(integerStringStringTriple.getMiddle());
                }else {
                    processResultBO.setMsg(integerStringStringTriple.getRight());
                }

                return processResultBO;
            }
        return null;
        }catch (Exception e){
            String msg = String.format("PandaCommandExecutor.process err %s", e.getMessage());
            log.error(msg);
            processResultBO.setMsg(msg);
            processResultBO.setCode(1);
            return processResultBO;
        }

    }
}
