package com.pinduoduo.mountain.service.operationmanage.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("实例的参数名对应的值")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class InstanceVariableValueDTO {
    @ApiModelProperty(value = "实例id", required = true)
    private String instanceId;

    @ApiModelProperty(value = "当前值", required = true)
    private Object currentValue;

    @ApiModelProperty(value = "参数名", required = true)
    private String configName;

    @ApiModelProperty(value = "db版本", required = true)
    private String dbVersion;

    @ApiModelProperty(value = "ip", required = true)
    private String instanceIp;

    @ApiModelProperty(value = "端口", required = true)
    private Integer port;

}
