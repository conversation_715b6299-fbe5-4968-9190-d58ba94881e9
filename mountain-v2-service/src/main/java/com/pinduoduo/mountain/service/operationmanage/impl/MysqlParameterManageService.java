package com.pinduoduo.mountain.service.operationmanage.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pinduoduo.mountain.common.config.PlatformConfig;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.common.thirdparty.inform.InformService;
import com.pinduoduo.mountain.common.thirdparty.inform.response.InformSendMessageRespV2;
import com.pinduoduo.mountain.common.thirdparty.panda.PandaService;
import com.pinduoduo.mountain.common.thirdparty.xcloud.XcloudProxyService;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.*;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.*;
import com.pinduoduo.mountain.repository.util.InformationSchemaUtil;
import com.pinduoduo.mountain.service.metadata.dto.InstanceNewBaseInfoDTO;
import com.pinduoduo.mountain.service.metadata.impl.LogicDatabaseService;
import com.pinduoduo.mountain.service.operationmanage.dto.InstanceVariableValueDTO;
import com.pinduoduo.mountain.service.operationmanage.dto.LogicDatabaseInstanceInfoDTO;
import com.pinduoduo.mountain.service.operationmanage.dto.MysqlVariablesModifyDTO;
import ddns.org.apache.commons.lang3.tuple.Triple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MysqlParameterManageService {


    private final ClusterMapper clusterMapper;
    private final InstanceMapper instanceMapper;
    private final LogicDatabaseMapper logicDatabaseMapper;
    private final PhysicalClusterMapper physicalClusterMapper;
    private final PhyClusterInstanceRelationMapper phyClusterInstanceRelationMapper;
    private final InstanceDbInfoMapper instanceDbInfoMapper;
    private final MysqldParameterConfigMapper mysqldParameterConfigMapper;
    private final ParameterModifyLogMapper parameterModifyLogMapper;

    private final LogicDatabaseService logicDatabaseService;
    private final XcloudProxyService xcloudProxyService;
    private final PandaService pandaService;
    private final InformService informService;



    public MysqlParameterManageService(ClusterMapper clusterMapper, InstanceMapper instanceMapper, LogicDatabaseMapper logicDatabaseMapper, PhysicalClusterMapper physicalClusterMapper, PhyClusterInstanceRelationMapper phyClusterInstanceRelationMapper, InstanceDbInfoMapper instanceDbInfoMapper, MysqldParameterConfigMapper mysqldParameterConfigMapper, ParameterModifyLogMapper parameterModifyLogMapper, LogicDatabaseService logicDatabaseService, XcloudProxyService xcloudProxyService, PandaService pandaService, InformService informService) {
        this.clusterMapper = clusterMapper;
        this.instanceMapper = instanceMapper;
        this.logicDatabaseMapper = logicDatabaseMapper;
        this.physicalClusterMapper = physicalClusterMapper;
        this.phyClusterInstanceRelationMapper = phyClusterInstanceRelationMapper;
        this.instanceDbInfoMapper = instanceDbInfoMapper;
        this.mysqldParameterConfigMapper = mysqldParameterConfigMapper;
        this.parameterModifyLogMapper = parameterModifyLogMapper;
        this.logicDatabaseService = logicDatabaseService;
        this.xcloudProxyService = xcloudProxyService;
        this.pandaService = pandaService;
        this.informService = informService;
    }

    // 这里更像是一个通用接口，通过搜索某个关键词，找到所有关于这个关键词的所有实例。先写到这里，后续看需求移位置
    public PageResult<LogicDatabaseInstanceInfoDTO> getLogicDatabaseInstanceList(String username, String env, int pageNum, int pageSize,
                                                                                 String business, String service, String searchVal) {

        List<LogicDatabaseInstanceInfoDTO> result = new ArrayList<>();
        Pair<List<LogicDatabase>, Integer> pair = logicDatabaseService.pageSearch(username, env, searchVal, business, service, pageNum, pageSize);
        log.info(String.format("getLogicDatabaseInstanceList pageSearch对应的结果为：%s", pair));
        for (LogicDatabase logicDatabase : pair.getLeft()) {
            LogicDatabaseInstanceInfoDTO logicDatabaseInstanceInfoDTO = new LogicDatabaseInstanceInfoDTO();
            // 这个逻辑库的集群
            List<Cluster> clusters = new ArrayList<>(1);
            Cluster cluster = clusterMapper.selectOneByClusterId(logicDatabase.getClusterId());
            clusters.add(cluster);

            logicDatabaseInstanceInfoDTO.setLogicDbId(logicDatabase.getLogicDbId());
            logicDatabaseInstanceInfoDTO.setLogicDbName(logicDatabase.getLogicDbName());
            logicDatabaseInstanceInfoDTO.setUniqueDbName(logicDatabase.getUniqueDbName());
            logicDatabaseInstanceInfoDTO.setBusinessName(logicDatabase.getBusinessName());
            logicDatabaseInstanceInfoDTO.setServiceName(logicDatabase.getServiceName());

            List<LogicDatabaseInstanceInfoDTO.ClusterSummary> clusterSummaryList = new ArrayList<>();

            // 这个逻辑库的物理集群
            for (Cluster cluster_ : clusters) {
                LogicDatabaseInstanceInfoDTO.ClusterSummary clusterSummary = new LogicDatabaseInstanceInfoDTO.ClusterSummary();
                clusterSummary.setClusterId(cluster_.getClusterId());
                clusterSummary.setClusterName(cluster_.getClusterName());

                List<PhysicalCluster> physicalClusters = physicalClusterMapper.selectByClusterId(cluster_.getClusterId());
                List<LogicDatabaseInstanceInfoDTO.PhysicalClusterSummary> physicalClusterSummaryList = new ArrayList<>();
                for (PhysicalCluster physicalCluster : physicalClusters) {
                    LogicDatabaseInstanceInfoDTO.PhysicalClusterSummary physicalClusterSummary = new LogicDatabaseInstanceInfoDTO.PhysicalClusterSummary();
                    List<InstanceNewBaseInfoDTO> allRoleInstanceList = new ArrayList<>();
                    List<String> instanceIds = phyClusterInstanceRelationMapper.selectInstanceIdsByPhysicalClusterId(physicalCluster.getPhysicalClusterId());
                    String instanceIdsInSql = String.format("'%s'", String.join("', '", instanceIds));
                    List<Instance> instancesByInstanceIds = instanceMapper.selectAllRoleInstanceByInstanceIdsInSqlExceptBackup(instanceIdsInSql);
                    for(Instance masterInstance:instancesByInstanceIds){
                        InstanceNewBaseInfoDTO InstanceNewBaseInfoDTO = new InstanceNewBaseInfoDTO(masterInstance);
                        String dbVersion = String.join(".", Arrays.copyOfRange(masterInstance.getVersion().split("\\."), 0, 2));
                        InstanceNewBaseInfoDTO.setVersion(dbVersion);
                        allRoleInstanceList.add(InstanceNewBaseInfoDTO);
                    }
                    physicalClusterSummary.setPhysicalClusterId(physicalCluster.getPhysicalClusterId());
                    physicalClusterSummary.setPhysicalClusterName(physicalCluster.getPhysicalClusterName());
                    physicalClusterSummary.setInstanceNewBaseInfoList(allRoleInstanceList);
                    physicalClusterSummaryList.add(physicalClusterSummary);
                }
                clusterSummary.setPhysicalClusterSummaryList(physicalClusterSummaryList);

                clusterSummaryList.add(clusterSummary);
            }
            logicDatabaseInstanceInfoDTO.setClusterSummaryList(clusterSummaryList);
            result.add(logicDatabaseInstanceInfoDTO);
        }
        return PageResult.of(result, pair.getRight(), pageNum, pageSize);
    }

    // 这个接口支持从instance表快速查找到某个实例
    public PageResult<InstanceNewBaseInfoDTO> getSearchInstances(String searchValue, String env, String business, String serviceName, String cloud, String region, Integer pageNumber, Integer pageSize){
        log.info(String.format("getSearchInstances的入参：searchValue=%s,env=%s,business=%s, serviceName=%s, cloud=%s, region=%s, pageNumber=%s, pageSize=%s", searchValue,env,business,serviceName,cloud,region,pageNumber,pageSize));
        List<InstanceNewBaseInfoDTO> instanceInfoList = new ArrayList<>();
        int offset = (pageNumber - 1) * pageSize;
        String rolesInSql = "'master','bg','ro','disaster'";
        List<Instance> instanceList = instanceMapper.selectBySearchValueAndBaseinfo(searchValue, env, business, serviceName, cloud,region, rolesInSql, offset, pageSize);
        Integer instanceCount = instanceMapper.selectCountBySearchValueAndBaseinfo(searchValue, env, business, serviceName, cloud,region,rolesInSql);
        for(Instance instance:instanceList){
            InstanceNewBaseInfoDTO InstanceNewBaseInfoDTO = new InstanceNewBaseInfoDTO(instance);
            String dbVersion = String.join(".", Arrays.copyOfRange(instance.getVersion().split("\\."), 0, 2));
            InstanceNewBaseInfoDTO.setVersion(dbVersion);

            instanceInfoList.add(InstanceNewBaseInfoDTO);
        }
        return PageResult.of(instanceInfoList, instanceCount, pageNumber, pageSize);
    }

    // 获取某个实例的实例参数
    public List<MysqldParameterConfig> getInstanceParameter(String instanceId){

        List<MysqldParameterConfig> result = new ArrayList<>();
        Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
        List<HashMap<String, Object>> instanceAllGlobalVariables = InformationSchemaUtil.getInstanceAllGlobalVariables(instance.getIp(), instance.getPort());
        String dbVersion = String.join(".", Arrays.copyOfRange(instance.getVersion().split("\\."), 0, 2)); //5.7.25的版本取5.7
        List<MysqldParameterConfig> mysqldParameterConfigList = mysqldParameterConfigMapper.selectByDbVersion(dbVersion);

        for (MysqldParameterConfig mysqldParameterConfig : mysqldParameterConfigList) {
            for (HashMap<String, Object> instanceAllGlobalVariable : instanceAllGlobalVariables) {
                String variableName = (String) instanceAllGlobalVariable.get("Variable_name");
                Object variableValue = instanceAllGlobalVariable.get("Value");
                if(mysqldParameterConfig.getConfigName().equals(variableName)){
                    mysqldParameterConfig.setCurrentValue(variableValue);
                }
            }
            result.add(mysqldParameterConfig);
        }
        return result;
    }

    // 获取某一批实例的某个参数的当前值
    public List<InstanceVariableValueDTO> getInstanceListVariableValue(String instanceIdString,String parameterName){
        if(instanceIdString.isEmpty()){
            return null;
        }
        List<InstanceVariableValueDTO> instanceVariableValueDTOList = new ArrayList<>();
        List<String> instanceIdList = Arrays.asList(instanceIdString.split(","));
        for(String instanceId:instanceIdList){
            Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
            List<HashMap<String, Object>> instanceAllGlobalVariables = InformationSchemaUtil.getInstanceAllGlobalVariables(instance.getIp(), instance.getPort());
            Object variableNowValue = null;
            for (HashMap<String, Object> instanceAllGlobalVariable : instanceAllGlobalVariables) {
                String variableName = (String) instanceAllGlobalVariable.get("Variable_name");
                Object variableValue = instanceAllGlobalVariable.get("Value");
                if (variableName.equals(parameterName)){
                    variableNowValue = variableValue;
                    break;
                }

            }
            InstanceVariableValueDTO instanceVariableValueDTO = new InstanceVariableValueDTO();
            instanceVariableValueDTO.setInstanceId(instanceId);
            instanceVariableValueDTO.setConfigName(parameterName);
            instanceVariableValueDTO.setCurrentValue(variableNowValue);
            instanceVariableValueDTO.setDbVersion(String.join(".", Arrays.copyOfRange(instance.getVersion().split("\\."), 0, 2)));
            instanceVariableValueDTO.setInstanceIp(instance.getIp());
            instanceVariableValueDTO.setPort(instance.getPort());

            instanceVariableValueDTOList.add(instanceVariableValueDTO);
        }
        return instanceVariableValueDTOList;

    }

    // 根据实例id查找某个实例的参数修改记录
    public List<ParameterModifyLog> getModifyHistory(List<String> instanceIdList){
        List<ParameterModifyLog> parameterModifyLogs = parameterModifyLogMapper.selectByInstanceId(String.format("'%s'", String.join("', '", instanceIdList)));
        log.info(String.format("getModifyHistory Result: %s", parameterModifyLogs));
        return parameterModifyLogs;
    }

    // 获取某个版本的MySQL实例的参数列表有哪些，及参数的详细信息
    public List<MysqldParameterConfig> getParameterListByVersionAndReboot(String dbVersion,String needReboot){
        List<MysqldParameterConfig> mysqldParameterConfigList = mysqldParameterConfigMapper.selectByDbVersionAndReboot(dbVersion,needReboot);
        log.info(String.format("getParameterListByVersionAndReboot Result: %s", mysqldParameterConfigList));
        return mysqldParameterConfigList;
    }


    // 同步修改一批同云环境、同region实例的某个参数
    public Pair<Boolean, String> modifyInstanceListVariables(MysqlVariablesModifyDTO mysqlVariablesModifyDTO,String username){

        List<String> instanceIdList = mysqlVariablesModifyDTO.getInstanceIdList();
        log.info(String.format("modifyInstanceVariables 待修改参数的实例列表为：%s", instanceIdList));
        if (instanceIdList.size()==0 || instanceIdList.size()>256){
            return Pair.of(Boolean.FALSE,"实例列表为空或者实例列表超过256个，请分批修改！");
        }
        MysqlVariablesModifyDTO.ModifyData modifyData = mysqlVariablesModifyDTO.getModifyData();

        // 判断是不是同云实例、同region实例，不是的话直接返回不允许修改
        List<String> sameCloudList = new ArrayList<>();
        List<String> sameRegion = new ArrayList<>();
        for (String instanceId : instanceIdList) {
            Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
            if(!sameCloudList.contains(instance.getType())){
                sameCloudList.add(instance.getType());
            }
            if(!sameRegion.contains(instance.getRegion())){
                sameRegion.add(instance.getRegion());
            }
        }
        if (sameCloudList.size()>1 || sameRegion.size()>1){
            return Pair.of(Boolean.FALSE,"实例云环境不统一或者region不统一，不允许批量修改！");
        }

        Instance instanceSample = instanceMapper.selectOneByInstanceId(instanceIdList.get(0));
        String cloud = instanceSample.getType();
        log.info(String.format("实例%s的cloud为：%s", instanceSample,cloud));

        Pair<Boolean, String> modifyResult = Pair.of(Boolean.FALSE,"");
        if (cloud.equals("cdb") || cloud.equals("fft") || cloud.equals("isv")) {

            ExecutorService executorService = Executors.newFixedThreadPool(11); // 限制最大线程数
            List<Future<Pair<Boolean, String>>> futures = new ArrayList<>();

            int totalSize = instanceIdList.size();
            // 腾讯云接口一次只能修改100个实例，跟王小林、腾讯云开发沟通后建议一次传20个实例过去
            for (int start = 0; start < totalSize; start += 20) {
                // 获取当前批次的子列表
                List<String> instanceSubList = instanceIdList.subList(start, Math.min(start + 20, totalSize));
                futures.add(executorService.submit(() -> modifyCdbInstanceVariable(instanceSubList, modifyData)));
            }

            for (Future<Pair<Boolean, String>> future : futures) {
                try {
                    Pair<Boolean, String> result = future.get(); // 等待线程完成并获取结果
                    log.info("modifyCdbInstanceVariable 的结果：" + result);
                    // 如果遇到随便哪一批修改失败，就返回
                    if(result.getLeft()==Boolean.FALSE){
                        modifyResult = result;
                        break;
                    }
                    modifyResult = result;
                } catch (ExecutionException | InterruptedException e) {
                    log.error("线程执行失败", e);
                }
            }
            executorService.shutdown(); // 关闭线程池
        }

        else if (cloud.equals("bdb")) {
            // 百度云的接口一次支持修改512个实例，我们一次最多修改256个实例
            modifyResult = modifyBdbInstanceVariable(instanceIdList, modifyData);
        }

        else if (cloud.equals("pdb")) {
            modifyResult = modifyPdbInstanceVariables(instanceIdList, modifyData);
        }

        else{
            modifyResult = Pair.of(Boolean.FALSE, "未知实例类型，不允许切换！");
        }

        log.info(String.format("修改结果为：%s", modifyResult));
        // 修改成功后，逐一检查是否真的修改成功
        if(modifyResult.getLeft()){
            modifyResult = modifyConfirm(instanceIdList, modifyData);
        }
        log.info(String.format("确认结果为：%s", modifyResult));


        // 插入修改记录

        for (String instanceId : instanceIdList) {
            try {
                Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
                Integer logStatus = modifyResult.getLeft() ? 1 : 2;
                parameterModifyLogMapper.insertModifyRecord(instance.getBusinessName(),modifyData.getDbVersion(),modifyData.getParameterName(),modifyData.getOldValue().toString(),modifyData.getNewValue().toString(),instanceId,instance.getInstanceName(),instance.getIp(),instance.getPort(),username,logStatus, mysqlVariablesModifyDTO.getModifyComment());
                modifyResult = Pair.of(Boolean.TRUE, "插入参数修改记录成功");
            }catch (Exception e){
                log.info(String.format("更新成功，但是插入修改记录表出错，错误信息为：%s", e));
                modifyResult = Pair.of(Boolean.TRUE, String.format("更新成功，但是插入修改记录表出错，错误信息为：%s", e));
            }
        }
        log.info(String.format("插入参数修改修改记录成功！%s", LocalDateTime.now()));

        // 发送Knock通知，每50个实例发送一个通知
        int batchSize = 50; // 每批的大小
        int totalSize = instanceIdList.size();
        int numBatches = (int) Math.ceil((double) totalSize / batchSize);

        for (int i = 0; i < numBatches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, totalSize);
            List<String> batch = instanceIdList.subList(start, end);

            try{
                String title = "[Mountain V2] MySQL配置变更";
                String content = String.format(
                        "业务线：%s\n" +
                        "实例: %s\n" +
                        "数据库版本: %s\n" +
                        "操作人: %s\n" +
                        "操作时间：%s\n\n" +
                        "配置名：%s\n" +
                        "变更前: %s\n" +
                        "变更后: %s\n" +
                        "变更原因：%s\n" +
                        "变更结果: %s",
                        instanceSample.getBusinessName(), batch, modifyData.getDbVersion(), username, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),modifyData.getParameterName(),modifyData.getOldValue(),modifyData.getNewValue(),mysqlVariablesModifyDTO.getModifyComment(),modifyResult.getLeft()+" | "+modifyResult.getRight()
                );
                InformSendMessageRespV2 informSendMessageRespV2 = informService.sendMsgToDbaInform(new ArrayList<>(PlatformConfig.ADMIN_MEMBERS_SET.keySet()),title,content);
                log.info(String.format("修改参数发送通知结果为：%s", informSendMessageRespV2));

            }catch (Exception e){
                log.info(String.format("发通知出错：%s", e));
                modifyResult = Pair.of(Boolean.TRUE, String.format("更新成功，发通知出错，错误信息为：%s", e));
            }
        }

        return modifyResult;
    }


    public Pair<Boolean, String> modifyCdbInstanceVariable(List<String> instanceSubList, MysqlVariablesModifyDTO.ModifyData modifyData){
        try{
            List<HashMap<String,Object>> paramList = new ArrayList<>();
            HashMap<String,Object> param = new HashMap<String,Object>(){
                {
                    put("Name",modifyData.getParameterName());
                    put("CurrentValue",modifyData.getNewValue().toString()); //腾讯云接口要求所有修改的值都转为String类型
                }
            };
            paramList.add(param);

            Instance instanceSample = instanceMapper.selectOneByInstanceId(instanceSubList.get(0));
            String region = instanceSample.getRegion();
            String cloudType = instanceSample.getCloudName().replace("qcloud","QCloud");

            JSONObject requestParams = new JSONObject();
            requestParams.put("InstanceIds", instanceSubList);
            requestParams.put("ParamList", paramList);
            requestParams.put("Method", "POST");
            requestParams.put("Action", "ModifyInstanceParam");
            requestParams.put("Region", region);
            requestParams.put("Version", "2017-03-20");
            log.info(String.format("调腾讯云 ModifyInstanceParam 的入参为：cloudType %s,region %s,requestParams %s", cloudType,region,requestParams));
            XcloudProxyService x = xcloudProxyService.withParams(cloudType, "V3", "cdb", region);
            JSONObject response = x.request(requestParams);

            /**接口返回错误有两种json情况，都需要判断
             * ①{
             *         "Response": {
             *             "Error": {
             *                 "Code": "AuthFailure.SignatureFailure",
             *                 "Message": "The provided credentials could not be validated. Please check your signature is correct."
             *             },
             *             "RequestId": "ed93f3cb-f35e-473f-b9f3-0d451b8b79c6"
             *         }
             *     }
             *
             *  ②{"error_code":40003,"error_msg":"非法的云类型"}
             */
            if(response.getIntValue("error_code")!=0){ //获取一个不存在的键返回0
                return Pair.of(Boolean.FALSE, response.toString());
            }
            JSONObject resp = response.getJSONObject("Response");
            log.info(String.format("%s请求modifyCdbInstanceVariable返回的结果为：%s", instanceSubList, response));
            if (resp != null) {
                if (resp.get("Error") != null) {
                    return Pair.of(Boolean.FALSE, resp.get("Error").toString());
                }else{
                    String asyncRequestId = resp.getString("AsyncRequestId");
                    Thread.sleep(1000);

                    try{
                        JSONObject asyncRequestParams = new JSONObject();
                        asyncRequestParams.put("AsyncRequestId", asyncRequestId);
                        asyncRequestParams.put("Method", "POST");
                        asyncRequestParams.put("Action", "DescribeAsyncRequestInfo");
                        asyncRequestParams.put("Region", region);
                        asyncRequestParams.put("Version", "2017-03-20");
                        JSONObject asyncResponse = x.request(asyncRequestParams).getJSONObject("Response");
                        log.info(String.format("腾讯云DescribeAsyncRequestInfo的返回结果是：%s", asyncResponse));

                        /**
                         * {
                         *     "Response": {
                         *         "Info": "修改成功！",
                         *         "Status": "SUCCESS", //任务执行结果。可能的取值：INITIAL - 初始化，RUNNING - 运行中，SUCCESS - 执行成功，FAILED - 执行失败，KILLED - 已终止，REMOVED - 已删除，PAUSED - 终止中。
                         * 注意：此字段可能返回 null，表示取不到有效值。
                         *         "RequestId": "faae8d6a-38fb-44de-988e-5a0e78aba4a7"
                         *     }
                         * }
                         */

                        String asyncResponseStatus = asyncResponse.getString("Status");
                        String asyncResponseInfo = asyncResponse.getString("Info");

                        if (asyncResponseStatus!=null && !asyncResponseStatus.isEmpty()){
                            while (asyncResponseStatus.equals("INITIAL") || asyncResponseStatus.equals("RUNNING")) {
                                Thread.sleep(1000);
                                asyncResponse = x.request(asyncRequestParams).getJSONObject("Response");
                                log.info(String.format("腾讯云DescribeAsyncRequestInfo的返回结果是：%s", asyncResponse));
                                asyncResponseStatus = asyncResponse.getString("Status");
                                asyncResponseInfo = asyncResponse.getString("Info");
                            }

                            if (asyncResponseStatus.equals("SUCCESS")) {
                                log.info(String.format("%s更新成功！%s", "SUCCESS", asyncResponseInfo));
                                return Pair.of(Boolean.TRUE, "更新成功！" + asyncResponseInfo);
                            }

                        }
                        return Pair.of(Boolean.FALSE, "查询腾讯云任务失败！" + asyncResponseInfo);
                    }catch (Exception e){
                        log.error(String.format(" DescribeAsyncRequestInfo FAILD! %s", e));
                        return Pair.of(Boolean.FALSE, "异步查询腾讯云修改任务结果为：" + e.toString());
                    }
                }

            }
            return Pair.of(Boolean.FALSE,"调用腾讯云接口ModifyInstanceParam返回结果为空！");

        }catch (Exception e){
            return Pair.of(Boolean.FALSE, String.format("modifyCdbInstanceVariable 接口出错！%s", e.toString()));
        }

    }

    public Pair<Boolean, String> modifyBdbInstanceVariable(List<String> instanceSubList, MysqlVariablesModifyDTO.ModifyData modifyData) {
        List<HashMap<String,Object>> paramList = new ArrayList<>();
        HashMap<String,Object> param = new HashMap<String,Object>(){
            {
                put("name",modifyData.getParameterName());
                put("value",modifyData.getNewValue());
            }
        };
        paramList.add(param);

        Instance instanceSample = instanceMapper.selectOneByInstanceId(instanceSubList.get(0));
        String region = instanceSample.getRegion();
        String cloudType = instanceSample.getCloudName().replace("qcloud","Qcloud");

        JSONObject requestParams = new JSONObject();
        requestParams.put("instanceIds", instanceSubList);
        requestParams.put("parameters", paramList);
        requestParams.put("Method", "PUT");
        requestParams.put("uri", "/v1/ddc/instance/parameter/modify/batch");
        requestParams.put("waitSwitch", 0); //执行时机。0：立即执行；1：维护时间内执行；2、用户控制触发时机
        XcloudProxyService x = xcloudProxyService.withParams(cloudType, "V1" ,"ddc", region);
        JSONObject response = x.request(requestParams);
        /* 百度云接口返回接口
         * {
         *     "success": true,
         *     "message": {},
         *     "result": {
         *         "ddc-mruqqqb1": {
         *             "success": 1,
         *             "taskId": 1512816,
         *             "code": "",
         *             "message": ""
         *         },
         *         "ddc-xxx": {
         *             "success": 0,
         *             "taskId": 0,
         *             "code": "",
         *             "message": "失败"
         *         }
         *     }
         * }
         */

        log.info(String.format("%s请求 modify/batch 返回的结果为：%s", instanceSubList, response));
        if (response != null) {
            boolean successResult = response.getBooleanValue("success");
            if (!successResult) {
                return Pair.of(Boolean.FALSE, response.getString("message"));
            }else{
                // 任务成功发起，记录一下每个实例的异步修改任务id
                List<Integer> modifyTaskList = new ArrayList<>();
                JSONObject asyncResult = response.getJSONObject("result");
                for (String insId : asyncResult.keySet()) {
                    JSONObject insResult = asyncResult.getJSONObject(insId);
                    Integer asyncRequestId = insResult.getInteger("taskId");
                    modifyTaskList.add(asyncRequestId);
                }

                try{
                    for (Integer taskId : modifyTaskList) {
                        JSONObject asyncRequestParams = new JSONObject();
                        asyncRequestParams.put("Method", "GET");
                        asyncRequestParams.put("uri", "/v1/ddc/task/detail");
                        asyncRequestParams.put("taskId", taskId);
                        log.info(String.format("请求百度云 /v1/ddc/task/detail 的入参为：%s",asyncRequestParams ));
                        JSONArray asyncTasksResponse = x.request(asyncRequestParams).getJSONArray("tasks");
                        log.info(String.format("百度云 /v1/ddc/task/detail 的返回结果是：%s", asyncTasksResponse));
                        /**
                         * {
                         *   "tasks" : [ {
                         *     "taskType" : "/queue/xflow_cluster_update",
                         *     "pickupTime" : "2021-11-17 05:48:51",
                         *     "instanceId" : "ddc-xdakndwn",
                         *     "instanceName" : "ddc-56",
                         *     "createTime" : "2021-11-17 04:48:51",
                         *     "taskSpecialAction" : "taskSpecialAction",
                         *     "appId" : "appId",
                         *     "errNu" : 0,
                         *     "taskName" : "重启",
                         *     "taskSpecialActionTime" : "taskSpecialActionTime",
                         *     "taskId" : 117821,
                         *     "taskStatus" : "success"
                         *   }, {
                         *     "taskType" : "/queue/xflow_cluster_update",
                         *     "pickupTime" : "2021-11-17 05:48:51",
                         *     "instanceId" : "ddc-xdakndwn",
                         *     "instanceName" : "ddc-56",
                         *     "createTime" : "2021-11-17 04:48:51",
                         *     "taskSpecialAction" : "taskSpecialAction",
                         *     "appId" : "appId",
                         *     "errNu" : 0,
                         *     "taskName" : "重启",
                         *     "taskSpecialActionTime" : "taskSpecialActionTime",
                         *     "taskId" : 117821,
                         *     "taskStatus" : "success"
                         *   } ]
                         * }
                         */
                        if(asyncTasksResponse.isEmpty()){
                            return Pair.of(Boolean.FALSE,"未查询到修改任务结果");
                        }
                        JSONObject taskDetail = (JSONObject) asyncTasksResponse.get(0);
                        String asyncResponseStatus = taskDetail.getString("taskStatus").toUpperCase();

                        while(!asyncResponseStatus.isEmpty()){
                            if (asyncResponseStatus.equals("SUCCESS")) {
                                log.info(String.format("%s更新成功！%s", "SUCCESS", asyncResponseStatus));
                                return Pair.of(Boolean.TRUE, "更新成功！" + asyncResponseStatus);
                            }
                            else if(asyncResponseStatus.equals("FAILED") || asyncResponseStatus.equals("CANCELED")){
                                return Pair.of(Boolean.FALSE, "异步查询百度云修改任务结果为：" + asyncResponseStatus);
                            }
                            else{
                                Thread.sleep(1000);
                                asyncResponseStatus  = ((JSONObject)x.request(asyncRequestParams).getJSONArray("tasks").get(0)).getString("taskStatus").toUpperCase();
                            }
                        }

                    }


                }catch (Exception e){
                    log.error(String.format(" 百度云异步查询任务状态FAILD! %s", e));
                    return Pair.of(Boolean.FALSE, "异步查询百度云修改任务结果为：" + e);
                }
            }

        }
        return Pair.of(Boolean.FALSE,"调用百度云接口 modifyBdbInstanceVariable 返回结果为空！");

    }

    public Pair<Boolean,String> modifyPdbInstanceVariables(List<String> instanceIdList, MysqlVariablesModifyDTO.ModifyData modifyData){
        List<Instance> allRoleInstanceList = instanceMapper.selectAllRoleInstanceByInstanceIdsInSql(String.format("'%s'", String.join("','",instanceIdList)));
        String command = "modify_parameter";
        log.info(String.format("modifyPdbInstanceVariables allRoleInstanceList列表为：%s", allRoleInstanceList));
        // 创建线程池
        int threadCount = allRoleInstanceList.size(); // 每个任务一个线程
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        // 存储任务的 Future 对象
        List<Future<Triple<Integer, String, String>>> futures = new ArrayList<>();

        for (Instance instance : allRoleInstanceList) {

            // 提交任务
            futures.add(executorService.submit(() -> {
                HashMap<String,Object> param = new HashMap<String,Object>(){
                    {
                        put(modifyData.getParameterName(),modifyData.getNewValue());
                    }
                };
                HashMap<String,Object> requestParams = new HashMap<>();
                requestParams.put("port", instance.getPort());
                requestParams.put("local_user", "xtraback");
                requestParams.put("local_user_pwd", "j0MlZ8fxMxVMuE6f");
                requestParams.put("args_str", param);
                log.info(String.format("pdb修改参数pandaExecute的入参为：machineIp：%s,command:%s, requestParams:%s", instance.getMachineIp(),command,requestParams));
                Triple<Integer, String, String> pandaResult = pandaService.pandaExecute(instance.getMachineIp(),command,requestParams,false);
                return pandaResult;

            }));
        }
        // 处理线程返回结果
        for (Future<Triple<Integer, String, String>> future : futures) {
            try {
                Triple<Integer, String, String> pandaResult = future.get(); // 等待线程完成并获取结果
                if (pandaResult.getLeft()!=0 && !pandaResult.getRight().equals("")){
                    return Pair.of(Boolean.FALSE,pandaResult.getRight());
                }
                System.out.println("Result: " + pandaResult);
            } catch (ExecutionException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        // 关闭线程池
        executorService.shutdown();

        return Pair.of(Boolean.TRUE,"SUCCESS");
    }

    public Pair<Boolean,String> modifyConfirm(List<String> instanceIdList, MysqlVariablesModifyDTO.ModifyData modifyData){

        String parameterName = modifyData.getParameterName();
        Object parameterNewValue = modifyData.getNewValue();

        for(String instanceId:instanceIdList){
            Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
            List<HashMap<String, Object>> instanceAllGlobalVariables = InformationSchemaUtil.getInstanceAllGlobalVariables(instance.getIp(), instance.getPort());
            for (HashMap<String, Object> instanceAllGlobalVariable : instanceAllGlobalVariables) {
                String variableName = (String) instanceAllGlobalVariable.get("Variable_name");
                Object variableNowValue = instanceAllGlobalVariable.get("Value");
                if (variableName.equals(parameterName)){
                    if(variableName.equals("long_query_time")){
                        if (Double.parseDouble(variableNowValue.toString()) == Double.parseDouble(parameterNewValue.toString())) {
                            break;
                        }else{
                            return Pair.of(Boolean.FALSE, String.format("确认异常！实例%s实时查询%s的结果为：%s，预期为：%s", instanceId,variableName,variableNowValue,parameterNewValue));
                        }
                    }
                    else if(variableName.equals("sql_mode")){
                        List<String> variableNowValueList = Arrays.asList(variableNowValue.toString().split(","));
                        List<String> parameterNewValueList = Arrays.asList(parameterNewValue.toString().split(","));
                        if (new HashSet<>(variableNowValueList).equals(new HashSet<>(parameterNewValueList))) {
                            break;
                        }else{
                            return Pair.of(Boolean.FALSE, String.format("确认异常！实例%s实时查询%s的结果为：%s，预期为：%s", instanceId,variableName,variableNowValue,parameterNewValue));
                        }
                    }

                    else if(variableNowValue.toString().equalsIgnoreCase(parameterNewValue.toString())){
                        break;
                    }
                    else{
                        return Pair.of(Boolean.FALSE, String.format("确认异常！实例%s实时查询%s的结果为：%s，预期为：%s", instanceId,variableName,variableNowValue,parameterNewValue));
                    }
                }
            }
        }
        return Pair.of(Boolean.TRUE,"SUCCESS");

    }



}
