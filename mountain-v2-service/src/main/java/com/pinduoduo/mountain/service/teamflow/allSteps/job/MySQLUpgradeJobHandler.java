package com.pinduoduo.mountain.service.teamflow.allSteps.job;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.pinduoduo.arch.leo.AppEnvUtil;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.CmdbServiceMapper;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.PddInstanceMapper;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFOrderStatusEnum;
import com.pinduoduo.mountain.service.teamflow.order.TFOrderService;
import com.pinduoduo.mountain.service.teamflow.order.entity.TFOrderBasic;
import com.yiran.arch.leo.util.LeoUtils;
import com.pinduoduo.mountain.common.config.Omega;
import com.pinduoduo.mountain.common.dto.pangu.PanguJobDetail;
import com.pinduoduo.mountain.common.dto.pangu.PanguMysqlJobResult;
import com.pinduoduo.mountain.common.dto.pangu.PanguPlanDetail;
import com.pinduoduo.mountain.common.model.cmdb.CmdbBusinessDTO;
import com.pinduoduo.mountain.common.model.platform.BaseRetDTO;
import com.pinduoduo.mountain.common.thirdparty.cmdb.CmdbService;
import com.pinduoduo.mountain.common.thirdparty.cmdb.WoodsService;
import com.pinduoduo.mountain.common.thirdparty.cmdb.model.request.WoodsAddResourceGroupRequest;
import com.pinduoduo.mountain.common.thirdparty.cmdb.model.response.WoodsAddResourceGroupResult;
import com.pinduoduo.mountain.common.thirdparty.inform.InformService;
import com.pinduoduo.mountain.common.thirdparty.omega.reponse.OmegaCreateTicketResponse;
import com.pinduoduo.mountain.common.thirdparty.pangu.PanguService;
import com.pinduoduo.mountain.common.thirdparty.pangu.request.MySQLChangeRequest;
import com.pinduoduo.mountain.common.thirdparty.pangu.response.MySQLResourceOperationResp;
import com.pinduoduo.mountain.common.util.CollectionUtil;
import com.pinduoduo.mountain.common.util.JSONUtils;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.InstanceMapper;
import com.pinduoduo.mountain.service.order.mysqlcreate.MySQLCreateService;
import com.pinduoduo.mountain.service.teamflow.entity.content.MySQLUpgradeOrderParam;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFContext;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFJob;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFJobStatusEnum;
import com.pinduoduo.mountain.service.teamflow.execute.TFJobService;
import com.pinduoduo.mountain.service.teamflow.step.TFStepDesc;
import com.pinduoduo.mountain.service.teamflow.step.TFStepExecution;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Validator;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.pinduoduo.mountain.common.constant.OrderType.MYSQL_UPGRADE;

/**
 * MySQL Scale-Up/Down Ticket Processor
 *
 * <p>
 * date: 2024-10-10
 */
@Service
@Slf4j
@SuppressWarnings({"DuplicatedCode", "FieldCanBeLocal"})
public class MySQLUpgradeJobHandler implements GeneralTFOrderJobHandler, TFStepExecution {
	private final Class<MySQLUpgradeOrderParam> jobParamType = MySQLUpgradeOrderParam.class;

	private final String PRECHECK_RESULT = "preCheckResult";

	private final String REQUIREMENT_BODY = "requirementBody"; // Procurement plan pangu procurement request body

	// 关于ServerBody
	private final String TOTAL_INSTANCE_IDS = "totalInstanceIds"; // all instance ids
	private final String PLAN_ID = "planId"; // Purchasing plan id
	private final String OMEGA_ID = "omegaId"; // omega ticket id
	private final String JOB_RESULT = "jobResult"; // CMP work order result
	private final String SHOW_INFOS = "showInfos"; // Display information



	public static final Map<String, String> ZONE_TO_IDC_DICT;

	// 第二备机，不同区，目前仅支持腾讯云二五区、百度云可用区A和H
	public static final Map<String, String> BACKUP_ZONE_DICT;
	public static final Map<String, String> BACKUP_SET_DICT;


	// ## 第二备机，idc转单元，目前仅支持腾讯云二五区、百度云可用区A和H
	public static final Map<String, String> IDC_TO_SET_MAP;

	static {

		// 初始化ZONE_TO_IDC_DICT
		Map<String, String> zoneToIdcMap = new HashMap<>();
		zoneToIdcMap.put("ap-shanghai-pdd1", "st1");
		zoneToIdcMap.put("ap-shanghai-pdd2", "st2");
		zoneToIdcMap.put("ap-shanghai-pdd3", "st3");
		zoneToIdcMap.put("ap-shanghai-pdd4", "st4");
		zoneToIdcMap.put("ap-shanghai-pdd5", "st5");
		zoneToIdcMap.put("ap-shanghai-pdd8", "st8");
		zoneToIdcMap.put("ap-shanghai-1", "st1");
		zoneToIdcMap.put("ap-shanghai-3", "st3");
		zoneToIdcMap.put("ap-shanghai-4", "st4");
		zoneToIdcMap.put("ap-shanghai-5", "st5");
		zoneToIdcMap.put("ap-guangzhou-3", "gz3");
		zoneToIdcMap.put("ap-guangzhou-4", "gz4");
		zoneToIdcMap.put("ap-beijing-3", "bt3");
		zoneToIdcMap.put("ap-beijing-5", "bt5");
		zoneToIdcMap.put("cn-su-c", "sbd1");
		zoneToIdcMap.put("cn-su-a", "sbd2");
		zoneToIdcMap.put("cn-su-h", "sbd3");
		zoneToIdcMap.put("cn-bj-f", "bbd1");
		zoneToIdcMap.put("pap-shanghai-1", "sp1");
		zoneToIdcMap.put("pap-shanghai-2", "sp2");
		zoneToIdcMap.put("pap-shanghai-3", "sp3");
		zoneToIdcMap.put("pap-shanghai-4", "sp4");
		zoneToIdcMap.put("pap-shanghai-5", "sp5");
		zoneToIdcMap.put("ap-seoul-1", "se1");
		zoneToIdcMap.put("ap-hongkong-1", "hk1");
		zoneToIdcMap.put("ap-shanghai-bls-10", "bst10");
		zoneToIdcMap.put("ap-xian-ec-1", "xat1");
		zoneToIdcMap.put("ap-wuhan-ec-1", "wht1");
		zoneToIdcMap.put("ap-nanjing-1", "nt1");
		zoneToIdcMap.put("ap-nanjing-2", "nt2");
		zoneToIdcMap.put("ap-chengdu-1", "ct1");
		zoneToIdcMap.put("cn-bd-b", "hbd1");
		zoneToIdcMap.put("cnt1-ap-shanghai-5", "sht5");
		zoneToIdcMap.put("tcnt1-ap-shanghai-5", "sht5");
		zoneToIdcMap.put("usm1-0", "vam0");
		zoneToIdcMap.put("usm1-1", "vam1");
		zoneToIdcMap.put("usm1-2", "vam2");
		zoneToIdcMap.put("usm1-3", "vam3");
		zoneToIdcMap.put("eam1-0", "hkm0");
		zoneToIdcMap.put("eam1-1", "hkm1");
		zoneToIdcMap.put("eam1-2", "hkm2");
		zoneToIdcMap.put("eam1-3", "hkm3");
		zoneToIdcMap.put("tusm1-0", "vam0");
		zoneToIdcMap.put("tusm1-1", "vam1");
		zoneToIdcMap.put("tusm1-2", "vam2");
		zoneToIdcMap.put("tusm1-3", "vam3");

		ZONE_TO_IDC_DICT = Collections.unmodifiableMap(zoneToIdcMap);

		// 初始化 backupZoneDict  第二备机，不同区，目前仅支持腾讯云二五区、百度云可用区A和H
		Map<String, String> backupZoneMap = new HashMap<>();
		backupZoneMap.put("ap-shanghai-pdd2", "ap-shanghai-pdd5");
		backupZoneMap.put("ap-shanghai-pdd5", "ap-shanghai-pdd2");
		backupZoneMap.put("ap-shanghai-2", "ap-shanghai-5");
		backupZoneMap.put("ap-shanghai-5", "ap-shanghai-2");
		backupZoneMap.put("cn-su-h", "cn-su-a");
		backupZoneMap.put("cn-su-a", "cn-su-h");
		BACKUP_ZONE_DICT = Collections.unmodifiableMap(backupZoneMap);

		// 初始化 backupSetDict
		Map<String, String> backupSetMap = new HashMap<>();
		backupSetMap.put("sh2", "sh5");
		backupSetMap.put("sh5", "sh2");
		backupSetMap.put("shb1", "shb1");
		BACKUP_SET_DICT = Collections.unmodifiableMap(backupSetMap);

		Map<String, String> idcToSetMap = new HashMap<>();
		idcToSetMap.put("st2", "sh2");
		idcToSetMap.put("st5", "sh5");
		idcToSetMap.put("st8", "sh2");
		idcToSetMap.put("sbd2", "shb1");
		idcToSetMap.put("sbd3", "shb1");

		IDC_TO_SET_MAP = Collections.unmodifiableMap(idcToSetMap);
	}

	@Resource
	private MySQLCreateService mysqlCreateService;
	@Resource
	private PanguService panguService;
	@Resource
	private CmdbService cmdbService;
	@Resource
	private WoodsService woodsService;
	@Resource
	private InstanceMapper instanceMapper;
	@Resource
	private PddInstanceMapper pddInstanceMapper;
	@Resource
	private CmdbServiceMapper cmdbServiceMapper;
	@Resource
	private Validator validator;
	@Resource
	private InformService informService;
	@Resource
	private TFJobService tfJobService;
	@Resource
	private TFOrderService tfOrderService;

	private List<String> DBA;

	@Value("${mountain-v2-api.cmp_home_url}")
	private String cmpHomeUrl;

	@Value("${mountain-v2-api.web_domain}")
	private String webDomain;


	@PostConstruct
	public void init() {
		DBA = Arrays.stream(LeoUtils.getProperty("mountain-backend.dba_apply").split(",")).collect(Collectors.toList());
	}

	@Override
	public String getType() {
		return MYSQL_UPGRADE.getTypeName();
	}

	@Override
	public BaseRetDTO<OmegaCreateTicketResponse> createOmegaFlow(TFOrderBasic tfOrderBasic) {
		// Todo Determine if upgrade/downgrade orders can bypass Omega approval?
		return BaseRetDTO.success(new OmegaCreateTicketResponse(0, "0"));
	}

	// Parameter Preprocessing
	private Pair<Boolean, Object> preHandleParam(TFOrderBasic tfOrderBasic) {
		MySQLUpgradeOrderParam param = this.parseJobParam(tfOrderBasic, jobParamType);


		List<Instance> instanceList = new ArrayList<>();
		for (Long phyClusterId : param.getPhysicalClusterIdList()) {
			instanceList.addAll(instanceMapper.selectByPhyClusterId(phyClusterId));
		}

		if (CollectionUtil.isEmpty(instanceList)) {
			return Pair.of(false, String.format("根据物理集群 %s 没有找到实例信息，请联系DBA。", param.getPhysicalClusterIdList()));
		}

		param.setInstanceIdList(instanceList.stream().map(Instance::getInstanceId).collect(Collectors.toList()));

		JsonNode jsonNode = JSONUtils.getObjectMapper().valueToTree(param);
		tfOrderBasic.setContent(jsonNode);
		return Pair.of(true, "valid");
	}

	@Override
	public Pair<Boolean, Object> validParam(TFOrderBasic tfOrderBasic) {
		// Parameter Preprocessing
		Pair<Boolean, Object> preHandleParamResult = preHandleParam(tfOrderBasic);
		if (!preHandleParamResult.getLeft()) {
			return preHandleParamResult;
		}

		MySQLUpgradeOrderParam param = this.parseJobParam(tfOrderBasic, jobParamType);

		// Set parameter value
		// BeanPropertyBindingResult 是 Spring 用来封装 Java Bean 校验和绑定错误的核心对象，在表单校验、自定义 Validator、@Valid 等机制下自动或手动使用。
		BindingResult result = new BeanPropertyBindingResult(param, "param");

		// Basic Form Parameter Validation
		validator.validate(param, result);

		if (result.hasErrors()) {
			return Pair.of(false, Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
		}

		if (Strings.isBlank(param.getAfterConfig())
				&& !(param.getUpgradeType().equals("double_backup_sameZone") && param.getSameZone())) {
			return Pair.of(false, "升级规格参数不能为空。");
		}

		return Pair.of(true, "参数验证成功！");
	}

	@TFStepDesc(name = "mysqlUpgradePreCheck",desc = "mysqlUpgrade工单的前置校验")
	public void mysqlUpgradePreCheck(TFContext context){
		MySQLUpgradeOrderParam content = context.getJobParam(jobParamType);
		log.info("开始进行mysqlUpgradePreCheck步骤..");
		context.info("开始进行mysqlUpgradePreCheck步骤..");
		if(!content.getUpgradeType().equals("cpu_memory_disk")){
			context.info("非实例规格变配，不需要校验配额是否满足，跳过本步骤，进入下一步-生产CMP工单请求体");
			context.putOutput(PRECHECK_RESULT, "Done");
			return;
		}
		log.info(String.format("升降配工单，Content为：%s", content));

		String afterConfig = content.getAfterConfig();
		final List<String> instanceIdList = content.getInstanceIdList();
		final Triple<Boolean, List<Map<String, Object>>, String> resourceGroupRt = mysqlCreateService.getResourceNeedGroupBySetIdc(instanceIdList, afterConfig);
		if(!resourceGroupRt.getLeft()){
			throw new RuntimeException(String.format("按单元&IDC进行资源需求聚合处理时出现异常:%s", resourceGroupRt.getRight()));
		}

		List<Map<String, Object>> resourceNeedGroupBySetIdc = resourceGroupRt.getMiddle();
		for (Map<String, Object> item : resourceNeedGroupBySetIdc) {
			String setName = (String) item.get("set");
			String idc = (String) item.get("idc");
			Integer cpuNeed = (Integer) item.get("cpuNeed");
			Integer memoryNeed = (Integer) item.get("memoryNeed");
			Integer volumeNeed = (Integer) item.get("volumeNeed");

			Map<String, Integer> quotaCheckMap = new HashMap<>();
			quotaCheckMap.put("cpu", cpuNeed);
			quotaCheckMap.put("mem", memoryNeed);
			quotaCheckMap.put("volume", volumeNeed);

			for (Map.Entry<String, Integer> entry : quotaCheckMap.entrySet()) {
				String quotaType = entry.getKey();
				Integer resourceNeed = entry.getValue();

				JsonNode cmpQuotaData = panguService.getCmpQuota(setName,quotaType);
				String msg  = String.format("DBA组单元%s下的%s类型配额情况为: %s", setName,quotaType,cmpQuotaData);
				context.info(msg);
				String needScaleOutMsg = "";
				for (JsonNode cmpItem : cmpQuotaData) {
					if(cmpItem.get("idc").asText().equals(idc)&&cmpItem.get("quota_type").asText().equals(quotaType)&&cmpItem.get("balance").asInt()<resourceNeed){
						needScaleOutMsg = String.format("单元%s下机房%s的%s配额当前剩余: %s。本工单%s配额需求量: %s，已不满足本工单的资源需求，工单暂停，DBA处理中，请耐心等待DBA配额扩容，扩容完毕后工单会继续执行。",cmpItem.get("set").asText(),cmpItem.get("idc").asText(),quotaType,cmpItem.get("balance").asInt(),quotaType,resourceNeed);
						context.putOutput(PRECHECK_RESULT,needScaleOutMsg);
						if(!needScaleOutMsg.isEmpty()){
							throw new RuntimeException(needScaleOutMsg);
						}
					}
				}
			}

		}
		context.info("配额校验完毕.");
		context.info("前置校验验证完毕，进入下一步-生产CMP工单请求体。");
		context.putOutput(PRECHECK_RESULT, "Done");
	}

	@TFStepDesc(name = "createUpgradePostServerBody", desc = "Create pangu purchasing plan")
	public void createUpgradePostServerBody(TFContext context) {
		MySQLUpgradeOrderParam content = context.getJobParam(jobParamType);

		List<String> totalInstanceIds = new ArrayList<>(content.getInstanceIdList());
		List<String> additionalInstanceIds = new ArrayList<>();
		Map<String, List<String>> idcDict = new HashMap<>();
		boolean omegaBigdataCheck = false;

		context.info("准备创建MySQL配置变更CMP采买计划");
		context.info("变更实例列表: " + content.getInstanceIdList());
		context.info("变更类型: " + content.getUpgradeType());

		// 按idc将实例归类
		/**
		 * {
		 *     'st2': [pdd_id1, pdd_id2, ...],
		 *     'sh1': [pdd_id3, pdd_id4, ...],
		 *     ...
		 * }
		 */
		for (String instanceId : content.getInstanceIdList()) {
			Instance instance = instanceMapper.selectOneByInstanceId(instanceId);

			if (instance == null) {
				log.error("[MySQLUpgradeJobHandler] createPostServerBody: instance {} does not exist", instanceId);
				throw new RuntimeException(String.format("Instance %s does not exist", instanceId));
			}

			if (instance.getRole().equals("disaster")) {
				context.info("Instance{} is a disaster recovery instance, skip", instanceId);
				continue;
			}

			String idc = instance.getIdc();

			if (!idcDict.containsKey(idc)) {
				idcDict.put(idc, new ArrayList<>());
			}
			if (!idcDict.get(idc).contains(instanceId)) {
				idcDict.get(idc).add(instanceId);
			}

			// 如果是主库调整磁盘，则从库跟着调整，cvm自建MySQL除外
			// 如果是主库主库跨区迁移，则ro库、bg库也跟着调整，disaster库不动
			if (instance.getRole().equals("master") && (Arrays.asList("cpu_memory_disk","cross_idc_migrate").contains(content.getUpgradeType()))) {
				String[] configs = content.getAfterConfig().split(",");
				if(content.getUpgradeType().equals("cpu_memory_disk") && Integer.parseInt(configs[2]) == instance.getInstanceVolume()){
					continue;
				}

				List<Instance> slaveInstances = instanceMapper.selectInstanceByMasterIdAndRoleNotIn(instanceId,"'master','backup'");
				if(content.getUpgradeType().equals("cross_idc_migrate")){
					slaveInstances = instanceMapper.selectInstanceByMasterIdAndRoleNotIn(instanceId,"'master','backup','disaster'");
				}

				context.info("获取实例 {} 的slave信息：{}", instanceId,
						slaveInstances.stream().map(Instance::getInstanceId).collect(Collectors.joining(",")));

				for (Instance slaveInstance : slaveInstances) {
					if (!content.getInstanceIdList().contains(slaveInstance.getInstanceId())) {
						String idcSlave = slaveInstance.getIdc();
						if (!idcDict.containsKey(idcSlave)) {
							idcDict.put(idcSlave, new ArrayList<>());
						}
						if (!idcDict.get(idcSlave).contains(slaveInstance.getInstanceId())) {
							idcDict.get(idcSlave).add(slaveInstance.getInstanceId());
							String slaveRole = slaveInstance.getRole();
							// 如果是bg实例提单扩缩容，加签大数据人员
							omegaBigdataCheck = slaveRole.equals("bg") || omegaBigdataCheck;

							additionalInstanceIds.add(slaveInstance.getInstanceId()+"##"+slaveInstance.getInstanceName()+" - "+slaveInstance.getIp()+":"+slaveInstance.getPort()+"##"+slaveInstance.getType()+"##"+slaveRole);
						}
					}
				}

			}

			// 如果是bg实例提单扩缩容，加签大数据人员
			omegaBigdataCheck = instance.getRole().equals("bg") || omegaBigdataCheck;
		}

		context.info("按IDC分类的实例:\n{}", JSONUtils.toJSONStringWithPretty(idcDict));

		totalInstanceIds.addAll(additionalInstanceIds);
		context.info("所有待变更的实例列表: " + String.join(",", totalInstanceIds));

		// 创建mysql资源组
		String serviceName = content.getService();
		List<MySQLChangeRequest.Requirement> plans = new ArrayList<>();

		for (String idc : idcDict.keySet()) {
			// 创建MySQL资源组
			String resourceGroupName = String.format("%s_%sMYSQL", serviceName, idc);
			context.info("开始创建MySQL资源组: {}", resourceGroupName);

			WoodsAddResourceGroupResult r = woodsService
					.addResourceGroup(new WoodsAddResourceGroupRequest(resourceGroupName, "MYSQL", serviceName));
			context.info(String.format("资源组创建成功: %s", r.getUid()));


			// 处理盘古plan
			if (content.getUpgradeType().equals("cpu_memory_disk")) {
				context.info("开始处理cpu_memory_disk场景.");
				Map<String, List<String>> roConfigDict = new HashMap<>();

				// 按CPU内存磁盘配置组合做分类，聚合同配置的RO实例，减少plan数量
				String[] configs = content.getAfterConfig().split(",");

				// 用户提单实例和额外添加的从实例，区分开
				List<String> originInstances = content.getInstanceIdList().stream()
						.filter(item -> idcDict.get(idc).contains(item)).collect(Collectors.toList());

				List<String> additionalInstances = idcDict.get(idc).stream().filter(x -> !originInstances.contains(x))
						.collect(Collectors.toList());

				if (!originInstances.isEmpty()) {
					Pair<String, String> woodsInfo = woodsService.getInstanceWoodsInfo(originInstances.get(0));
					String resourceGroup = woodsInfo.getLeft();
					String woodsServiceName = woodsInfo.getRight();

					MySQLChangeRequest.Requirement plan = MySQLChangeRequest.Requirement.builder()
							.service(woodsServiceName).resourceGroup(resourceGroup)
							.cpuCount(Integer.parseInt(configs[0])).memory(Integer.parseInt(configs[1]) / 1000)
							.volume(Integer.parseInt(configs[2])).instanceCount(originInstances.size())
							.instanceIds(originInstances).waitSwitch(2).build();
					context.info("Add user bill of lading instance plan:\n" + JSONUtils.toJSONStringWithPretty(plan));
					plans.add(plan);
				}

				for (String slaveInstanceId : additionalInstances) {
					Instance instanceInfo = instanceMapper.selectOneByInstanceId(slaveInstanceId);
					if (instanceInfo == null) {
						throw new RuntimeException(
								String.format("获取slave信息失败： %s", slaveInstanceId));
					}
					int cpuCount = instanceInfo.getInstanceCpu();
					int memory = instanceInfo.getInstanceMemory() / 1000;
					int volume = Integer.parseInt(configs[2]);
					String configKey = cpuCount + "C/" + memory + "G/" + volume + "G";

					if (roConfigDict.containsKey(configKey)) {
						roConfigDict.get(configKey).add(slaveInstanceId);
					} else {
						roConfigDict.put(configKey, new ArrayList<>(Collections.singletonList(slaveInstanceId)));
					}
				}

				for (Map.Entry<String, List<String>> roConfigEntry : roConfigDict.entrySet()) {
					Pair<String, String> woodsInfo = woodsService.getInstanceWoodsInfo(roConfigEntry.getValue().get(0));
					String resourceGroup = woodsInfo.getLeft();
					String woodsServiceName = woodsInfo.getRight();

					MySQLChangeRequest.Requirement plan = MySQLChangeRequest.Requirement.builder()
							.service(woodsServiceName).resourceGroup(resourceGroup)
							.cpuCount(Integer.parseInt(roConfigEntry.getKey().split("/")[0].replace("C", "")))
							.memory(Integer.parseInt(roConfigEntry.getKey().split("/")[1].replace("G", "")))
							.volume(Integer.parseInt(roConfigEntry.getKey().split("/")[2].replace("G", "")))
							.instanceCount(roConfigEntry.getValue().size()).instanceIds(roConfigEntry.getValue()).waitSwitch(2)
							.build();
					context.info("Add {} to configure ro instance plan:\n{}", roConfigEntry.getKey(),
							JSONUtils.toJSONStringWithPretty(plan));
					plans.add(plan);
				}
			} else {
				context.info("开始处理非升级规格场景.");

				// 非升级规格场景，规格参数仍然必传，每个实例单独生成一个plan，防止多实例规格不统一导致实例规格被误调整
				// 按CPU内存磁盘配置组合做分类，聚合同配置的RO实例，减少plan数量

				Map<String, List<String>> insConfigDict = new HashMap<>();

				Instance instance = null;

				for (String insId : idcDict.get(idc)) {
					instance = instanceMapper.selectOneByInstanceId(insId);
					if (instance == null) {
						throw new RuntimeException(String.format("get Instance %s information failed", insId));
					}

					int cpuCount = instance.getInstanceCpu();
					int memory = instance.getInstanceMemory() / 1000;
					int volume = instance.getInstanceVolume();
					String configKey = cpuCount + "C/" + memory + "G/" + volume + "G";

					if (insConfigDict.containsKey(configKey)) {
						insConfigDict.get(configKey).add(insId);
					} else {
						insConfigDict.put(configKey, new ArrayList<>(Collections.singletonList(insId)));
					}
				}

				for (Map.Entry<String, List<String>> roConfigEntry : insConfigDict.entrySet()) {
					Pair<String, String> woodsInfo = woodsService.getInstanceWoodsInfo(roConfigEntry.getValue().get(0));
					String resourceGroup = woodsInfo.getLeft();
					String woodsServiceName = woodsInfo.getRight();

					MySQLChangeRequest.Requirement plan = MySQLChangeRequest.Requirement.builder()
							.service(woodsServiceName).resourceGroup(resourceGroup)
							.cpuCount(Integer.parseInt(roConfigEntry.getKey().split("/")[0].replace("C", "")))
							.memory(Integer.parseInt(roConfigEntry.getKey().split("/")[1].replace("G", "")))
							.volume(Integer.parseInt(roConfigEntry.getKey().split("/")[2].replace("G", "")))
							.instanceCount(roConfigEntry.getValue().size()).instanceIds(roConfigEntry.getValue())
							.build();

					if (content.getUpgradeType().equals("double_backup") && !content.getSameZone()) {
						if(!BACKUP_ZONE_DICT.containsKey(instance.getZone())){
							throw new RuntimeException(String.format("工单不支持%s可用区的主实例创建跨区第二备库", instance.getZone()));
						}
						plan.setBackupZone(BACKUP_ZONE_DICT.get(instance.getZone()));
					} else if (content.getUpgradeType().equals("double_backup_sameZone") && content.getSameZone()) {
						plan.setBackupZone(instance.getZone());
					} else if (content.getUpgradeType().equals("engine_version")) {
						plan.setVersion(content.getAfterConfig().replaceAll("(?i)mysql", ""));
						plan.setEngineVersion(content.getAfterConfig().replaceAll("(?i)mysql", "")); //主站用的是engine_version字段
					} else if (content.getUpgradeType().equals("cross_idc_migrate")){

						// 主站单独逻辑：跨区迁移场景

						String [] sourceDestIdcList = content.getAfterConfig().split("_to_");
						if (sourceDestIdcList.length!=2){
							String msg = "跨区迁移的afterConfig参数格式错误，应该是类似st2_to_st8这样的格式，请检查参数是否正确!";
							throw new RuntimeException(msg);
						}
						String destIdc = sourceDestIdcList[1];
						if(!IDC_TO_SET_MAP.containsKey(destIdc)){
							throw new RuntimeException(String.format("跨区迁移的afterConfig参数错误，目标可用区 %s 不在idc_to_set_map映射组合支持范围，请检查参数是否正确", destIdc));
						}
						String descSet = IDC_TO_SET_MAP.get(destIdc);
						String descZone = "";
						for (Map.Entry<String, String> stringStringEntry : ZONE_TO_IDC_DICT.entrySet()) {
							if(stringStringEntry.getValue().equals(destIdc)){
								descZone = stringStringEntry.getKey();
							}
						}
						if(descZone.isEmpty()){
							throw new RuntimeException(String.format("跨区迁移的afterConfig参数错误，目标可用区 %s 不在zone_to_idc_dict映射组合支持范围，请检查参数是否正确", destIdc));
						}

						String finalDescZone = descZone;
						HashMap<String,String> migrateDestMap = new HashMap<String,String>(){{
							put("set",descSet);
							put("zone", finalDescZone);
							put("idc",destIdc);
						}};
						plan.setMigrateDest(migrateDestMap);
					}

					context.info("添加 configKey {} 的 instance plan:\n{}", roConfigEntry.getKey(),
							JSONUtils.toJSONStringWithPretty(plan));
					plans.add(plan);
				}
			}
		}

		String descriptionOmega = String.format("申请描述: %s\n预估说明: %s",
				context.getJobBean().getTitle(), context.getJobBean().getDescription());

		CmdbBusinessDTO businessInfo = cmdbService.getBusinessByChName(content.getBusiness());
		if (businessInfo == null) {
			throw new RuntimeException(String.format("查询业务线 %s 信息失败.", content.getBusiness()));
		}

		final com.pinduoduo.mountain.repository.mysql.pddmountain.entity.CmdbService cmdbService1 = cmdbServiceMapper.selectByServiceName("honeybee-api");
		String bigdataOwner = cmdbService1.getOwner();
		List<String> bigdataOwnerList = new ArrayList<>();
		try {
			bigdataOwnerList = Arrays.asList(bigdataOwner.split(","));
		}catch (Exception e){
			bigdataOwnerList =  Arrays.asList("manhui", "saisai","muyun");
		}

		MySQLChangeRequest serverBody = MySQLChangeRequest.builder()
				.traceId(context.getJobBean().getWorkId().toString()).businessLine(businessInfo.getEnglishName())
				.title(context.getJobBean().getTitle()).reason(descriptionOmega)
				.creatorId(context.getJobBean().getApplyUser()).ticketFollowers(DBA).requirements(plans)
				.extraApprover(omegaBigdataCheck ? bigdataOwnerList : new ArrayList<>())
				.idempotencyKey(context.getJobBean().getWorkId().toString())
				.pdbEnv(Arrays.asList("knock-prod", "knock-test", "knock-dev").contains(AppEnvUtil.getPddEnv()) ? "isolation":null)
				.build();

		context.putOutput(REQUIREMENT_BODY, serverBody);
		context.putOutput(TOTAL_INSTANCE_IDS, totalInstanceIds);
		context.putOutput(PLAN_ID, null);

		context.info("创建CMP采买任务成功, serverBody:\n{}",
				JSONUtils.toJSONStringWithPretty(serverBody));
		log.info(String.format("createUpgradePostServerBody 完成！context为：%s", context));
	}

	@TFStepDesc(name = "createUpgradeCDBPanguWork", desc = "Create Purchase Order")
	public void createUpgradeCDBPanguWork(TFContext context) {
		log.info(String.format("createUpgradeCDBPanguWork开始！context为：%s", context));
		MySQLUpgradeOrderParam content = context.getJobParam(jobParamType);

		String planId = context.getStringOutputs(PLAN_ID);

		if (planId != null) {
			// 已存在CMP任务: {}，不再请求CMP创建新的CMP工单
			context.info(String.format("已存在CMP任务: %s, 不再请求CMP创建新的CMP工单", planId));
			return;
		}

//		List<String> instanceIds = context.getClassObjectOutputs(TOTAL_INSTANCE_IDS, new TypeReference<List<String>>() {
//		});

		MySQLChangeRequest serverBody = context.getClassObjectOutputs(REQUIREMENT_BODY,
				new TypeReference<MySQLChangeRequest>() {});

		try {
			context.info("开始创建CMP任务, serverBodyJson:\n{}", JSONUtils.toJSONStringWithPretty(serverBody));

			MySQLResourceOperationResp resp = panguService.changeMySQL(serverBody);

			/*
			* resp样例： {'code': 0, 'msg': '', 'trace_id': '1744876802308-80', 'data': {'id': 12345, 'created_at': 1744876802, 'updated_at': 1744876802, 'guid': 'CMP-TIC-020-2025041712345', 'closed_at': 0, 'title': '数据库配置升级', 'reason': '业务活动流量调整，目前规格对数据库压力较大，需要升级配置', 'creator': '张三', 'creator_id': 'zhangsan', 'status': 1, 'business_line': 'testBusiness', 'business_line_cn': '业务线中文名', 'sponsor': 'zhangsan', 'type': 'MysqlChange', 'sponsor_cn': '张三', 'parent_uid': '', 'record_status': 'waiting', 'url': 'https://cmp.pdd.net/tickets/center/detail/mysql-change/CMP-TIC-020-20250417123456', 'type_cn': '数据库资源配置变更'}}
			* */
			planId = resp.getGuid();

			context.putOutput(PLAN_ID, planId);

			log.info("[MySQLUpgradeJobHandler] createUpgradeCDBPanguWork: CMP任务创建成功, planId: {}",
					planId);
			context.info(String.format("CMP任务创建成功, planId: %s", planId));

			// 主站单独逻辑： 实例配置变更切换管理功能优化：增加切换接管平台标记字段，切换执行根据标记来区分切换.

			final List<MySQLChangeRequest.Requirement> plans = serverBody.getRequirements();

			for (MySQLChangeRequest.Requirement planItem : plans) {
				List<String> instanceIds = planItem.getInstanceIds();
				String switch_platform = "cloud";
				if(planItem.getWaitSwitch()!=null && planItem.getWaitSwitch()==2){
					switch_platform = "mountain";
				}
				Map<String, Object> extInfo = new HashMap<>();
				extInfo.put("switch_platform",switch_platform);

				String operationType = "upgrade_cpu_memory_disk";

				switch (content.getUpgradeType()) {
					case "cpu_memory_disk" :
						String[] configs = content.getAfterConfig().split(",");
						extInfo.put("instance_cpu", Integer.parseInt(configs[0]));
						extInfo.put("instance_memory", Integer.parseInt(configs[1]));
						extInfo.put("instance_volume", Integer.parseInt(configs[2]));
						break;
					case "engine_version" :
						operationType = "upgrade_engine_version";
						break;
					case "double_backup" :
					case "double_backup_sameZone":
						operationType = "upgrade_double_backup";
						break;
				}
				for (String instanceId : instanceIds) {
					context.info("开始生成实例 {} 操作记录。", instanceId);

					Pair<Boolean, String> recordInfo = mysqlCreateService.instanceOperationRecord(instanceId, operationType,
							context.getJobBean().getApplyUser(), context.getJobBean().getWorkId(),
							context.getJobBean().getOmegaId(), extInfo);

					if (!recordInfo.getLeft()) {
						Exception e = new RuntimeException(recordInfo.getRight());
						log.error("[MySQLUpgradeJobHandler] createUpgradeCDBPanguWork, instanceOperationRecord error", e);
					} else {
						log.info(
								"[MySQLUpgradeJobHandler] createUpgradeCDBPanguWork, instance {} operation record generated successfully",
								instanceId);
						context.info("实例 {} 操作记录生成成功。", instanceId);
					}
				}
				context.info("所有实例操作记录生成完成。");
			}
		} catch (Exception e) {
			log.error("[MySQLUpgradeJobHandler] createUpgradeCDBPanguWork failed", e);
			throw new RuntimeException(String.format("createUpgradeCDBPanguWork 失败: %s", e.getMessage()));
		} finally {
			if (planId != null) {
				context.putOutput(PLAN_ID, planId);
			}
		}
	}

	@TFStepDesc(name = "getUpgradePlanResult", desc = "get upgrade plan results")
	public void getUpgradePlanResult(TFContext context) {
		String planId = context.getStringOutputs(PLAN_ID);

		List<String> totalInstanceIds = context.getClassObjectOutputs(TOTAL_INSTANCE_IDS,
				new TypeReference<List<String>>() {
				});

		Long omegaId = null;

		if (planId == null) {
			throw new RuntimeException("CMP task id为空！");
		}

		context.info("开始获取CMP任务结果, planId: {}", planId);

		boolean hasSetMountainWindow = false;

		// 设置3个变量，解决CMP工单日志大量打印无效日志的问题
		boolean getCMPJobLog = false;
		boolean getCMPOmegaLog = false;
		boolean getCMPTaskCompleteLog = false;


		while (true) {
			try {
				PanguPlanDetail resp = panguService.getPlanDetail(planId);

				// 获取CMP工单关联的Omega工单
				if (omegaId == null) {
					if (resp.getRelatedOmegaIds() != null && !resp.getRelatedOmegaIds().isEmpty()) {
						omegaId = resp.getRelatedOmegaIds().get(0);
						context.putOutput(OMEGA_ID, omegaId);
					}
				}

				log.info("[MySQLUpgradeJobHandler] getPlanResult resp: {}", resp);

				switch (resp.getStatus()) {
					case "REJECT" :
					case "STOP" :
						new Thread(() -> {
							try {
								Thread.sleep(30000);
								TFJob jobInfo = tfJobService.getJob(context.getJobBean().getWorkId());
								tfJobService.updateStatusByWorkId(jobInfo.getWorkId(), jobInfo.getStatusEnum(),
										TFJobStatusEnum.CLOSED_BY_MAN, "SYSTEM");
								if (resp.getStatus().equals("REJECT")) {
									tfOrderService.updateOrderStatus(jobInfo.getWorkId(), TFOrderStatusEnum.REJECTED_OR_CANCELED.name());
								} else {
									tfOrderService.updateOrderStatus(jobInfo.getWorkId(), TFOrderStatusEnum.CLOSED.name());
								}
							} catch (Exception e) {
								log.error("[MySQLCreateOrderJobHandler] getSubPlanResult close job failed", e);
							}
						}).start();
						if (resp.getStatus().equals("REJECT")) {
							throw new RuntimeException("CMP工单已驳回/关闭.");
						} else {
							throw new RuntimeException("CMP工单已被异常结单.");
						}
					case "RUNNING" :
					case "SUCCESS" :
						log.info("[MySQLUpgradeJobHandler] getUpgradePlanResult resp:\n{}",
								JSONUtils.toJSONStringWithPretty(resp));

						// 记录一下这行日志有没有被记录过，记录过就不在往库里面写入
						if(!getCMPJobLog){
							context.info("getPlanDetail成功，获取CMP工单{}对应的JOB:{}",planId,resp.getJobs());
							getCMPJobLog = true;
						}

						if (resp.getJobs() == null || resp.getJobs().isEmpty()) {
							if(!getCMPOmegaLog){
								if (omegaId != null) {
									context.info("CMP工单 {}/ticket/mysql-change/user/{} 还在审批中, 关联omega工单为 {}/#/task/type/exeing/detail/{}.", cmpHomeUrl, planId, Omega.DOMAIN, omegaId);
								} else {
									context.info("CMP工单 {}/ticket/mysql-change/user/{} 还在审批中.",
											cmpHomeUrl, planId);
								}
								getCMPOmegaLog = true;
							}

							Thread.sleep(60000);
							continue;
						}

						int success = 0;
						List<PanguMysqlJobResult> jobResult = new ArrayList<>();

						for (PanguJobDetail job : resp.getJobs()) {
							// omega审批完成，开始设置维护窗口
							log.info(String.format("CMP工单对应的JOB %s 获取成功完成，开始设置维护窗口。", job.getJobId()));
							if (!hasSetMountainWindow) {
								Pair<Boolean, String> result = mysqlCreateService.setMaintenanceWindow(totalInstanceIds, context);
								if (!result.getLeft()) {
									throw new RuntimeException(String.format("JOB %s 设置实例维护窗口失败: %s",job.getJobId(), result.getRight()));
								}
								hasSetMountainWindow = true;
							}
							//log.info(String.format("JOB %s 维护窗口设置完成。", job.getJobId()));
							String jobId = job.getJobId();
							PanguJobDetail jobDetail = panguService.getJobStatus(jobId);
							log.info("[MySQLUpgradeJobHandler] syncUpgradeMetaData: JOB {}的详情为{}",jobId, JSONUtils.toJSONStringWithPretty(jobDetail));
							if (jobDetail.getStatus().equals("SUCCESS")) {
								List<PanguMysqlJobResult> jobResultTemp = panguService.getJobResult(jobId);
								jobResult.addAll(jobResultTemp);
								success += 1;
								continue;
							}
							if(!getCMPTaskCompleteLog){
								context.info("等待CMP任务执行完成 {}/tickets/center/detail/mysql-change/{}", cmpHomeUrl, planId);
								getCMPTaskCompleteLog = true;

							}
						}

						if (resp.getJobs().size() == success) {
							context.info("所有实例扩缩容完成。 jobResult: {}",
									JSONUtils.toJSONStringWithPretty(jobResult));
							context.putOutput(JOB_RESULT, jobResult);
							context.info("准备同步元数据。");
							return;
						}

						Thread.sleep(60000);
						break;
				}
			} catch (Exception e) {
				log.error("[MySQLUpgradeJobHandler] syncUpgradeMetaData failed", e);
				throw new RuntimeException(String.format("Failed to request CMPget order result: %s", e.getMessage()));
			}
		}
	}

	@TFStepDesc(name = "syncUpgradeMetaData", desc = "Sync metadata")
	public void syncUpgradeMetaData(TFContext context) {
		MySQLUpgradeOrderParam content = context.getJobParam(jobParamType);

		List<PanguMysqlJobResult> InstanceChangeResult = context.getClassObjectOutputs(JOB_RESULT,
				new TypeReference<List<PanguMysqlJobResult>>() {
				});

		/*
		* JOB_RESULT 的样例：
		* [{"cloud_instance_no": "cdb-ld51aukg", "instance_name": "st2_spain_3", "ip": "*************", "port": 3306, "memory": 32000, "volume": 3000, "version": "5.7", "master_instance_id": "", "protect_mode": "SemiSync", "instance_ro": "master"}, {"cloud_instance_no": "cdb-mj5ubczq", "instance_name": "st2_spain_0", "ip": "************", "port": 3306, "memory": 32000, "volume": 3000, "version": "5.7", "master_instance_id": "", "protect_mode": "SemiSync", "instance_ro": "master"}, {"cloud_instance_no": "cdb-jpb0dayq", "instance_name": "st2_spain_2", "ip": "************", "port": 3306, "memory": 32000, "volume": 3000, "version": "5.7", "master_instance_id": "", "protect_mode": "SemiSync", "instance_ro": "master"}, {"cloud_instance_no": "cdb-k22u0lts", "instance_name": "st2_spain_1", "ip": "*************", "port": 3306, "memory": 32000, "volume": 3000, "version": "5.7", "master_instance_id": "", "protect_mode": "SemiSync", "instance_ro": "master"}]
		*
		* */

		context.info("开始同步实例元数据, jobResult: "
				+ JSONUtils.toJSONStringWithPretty(InstanceChangeResult));

		List<String> originalInstanceIdsList = content.getInstanceIdList();

		List<String> allInsList = new ArrayList<>();
		for (PanguMysqlJobResult instanceItem : InstanceChangeResult) {
			allInsList.add(instanceItem.getCloudInstanceNo());
		}

		// 工单完结展示信息处理
		List<ShowInfo> showInfos = new ArrayList<>();

		for (PanguMysqlJobResult instanceItem : InstanceChangeResult) {
			String instanceId = instanceItem.getCloudInstanceNo();
			String instanceName = instanceItem.getInstanceName();

			Instance instance = instanceMapper.selectOneByInstanceId(instanceId);

			if (instance == null) {
				log.error("[MySQLUpgradeJobHandler] syncUpgradeMetaData: instance {} does not exist", instanceId);
				throw new RuntimeException(
						String.format("Exception: Instance %s metadata information not found", instanceId));
			}
			String insIp = instance.getIp();
			Integer insPort = instance.getPort();
			String role = instance.getRole();

			ShowInfo tmpShowInfo = ShowInfo.builder().instanceId(instanceId).ip(insIp).port(insPort)
					.instanceName(instanceName).build();

			switch (content.getUpgradeType()) {
				case "cpu_memory_disk" : {
					context.info("开始同步实例 {} cpu_memory_disk upgrade results", instanceId);
					int insCpu = Integer.parseInt(content.getAfterConfig().split(",")[0]);

					if (!originalInstanceIdsList.contains(instanceId)) {
						insCpu = instance.getInstanceCpu();
					}

					int insMemory = instanceItem.getMemory();
					int insVolume = instanceItem.getVolume();

					// Update mountain v2 instance configuration
					instanceMapper.insertOrUpdateInstanceConfig(instanceId, insCpu, insMemory, insVolume);

					// Update mountain v1 instance configuration
					pddInstanceMapper.insertOrUpdateInstanceConfig(instanceId, insCpu, insMemory, insVolume);

					/* 这一段逻辑主站没有，先注掉。感觉跟下面的逻辑主实例变更，同时更新备库元数据有关系，但是主站又不止针对cdb实例，这里先注掉，等以后确实不需要了再删掉这段代码。
					if (instanceId.startsWith("cdb")) {
						List<Instance> backupInstances = instanceMapper.getBackupInfoByMasterIpAndPort(insIp, insPort);

						if (backupInstances != null && !backupInstances.isEmpty()) {
							String insId = backupInstances.get(0).getInstanceId();

							// Update mountain v2 instance configuration
							instanceMapper.updateBackupInstanceConfig(insId, insCpu, insMemory, insVolume);

							// Update mountain v1 instance configuration
							pddInstanceMapper.updateBackupInstanceConfig(insId, insCpu, insMemory, insVolume);
						}
					}*/

					// 主实例变更，同时更新备库元数据
					if (role.equals("master")){
						List<Instance> backupInsList = instanceMapper.selectBackupByMasterId(instanceId);
						if (backupInsList != null && !backupInsList.isEmpty()) {
							String backupInsId = backupInsList.get(0).getInstanceId();
							if(!allInsList.contains(backupInsId)){
								// Update mountain v2 instance configuration
								instanceMapper.updateBackupInstanceConfig(backupInsId, insCpu, insMemory, insVolume);

								// Update mountain v1 instance configuration
								pddInstanceMapper.updateBackupInstanceConfig(backupInsId, insCpu, insMemory, insVolume);
							}
						}
					}
					context.info(String.format(
							"实例 %s 升降配成功,同步元数据: CPU: %s, Memory: %s, Volume: %s",
							instanceId, insCpu, insMemory, insVolume));
					tmpShowInfo.setAfterConfig(String.format("%s核/%sM内存/%sG", insCpu, insMemory, insVolume));

					int finalInsCpu = insCpu;
					Map<String,Object> syncWoodsParam = new HashMap<String,Object>(){{
						put("cpu", finalInsCpu);
						put("memory",insMemory);
						put("volume",insVolume);
					}};
					final Pair<Boolean, String> booleanStringPair = woodsService.updateInstanceWoodsInfo(instanceId, syncWoodsParam);
					log.info(String.format("cpu_memory_disk 场景同步Woods的结果：%s", booleanStringPair));
					break;
				}
				case "engine_version" : {
					context.info("Start synchronizing instance {} engine_version upgrade results", instanceId);
					String insEngineVersion = content.getAfterConfig().replaceAll("(?i)mysql", "");

					// Update mountain v2 instance configuration
					instanceMapper.insertOrUpdateInstanceEngineVersion(instanceId, insEngineVersion);

					// Update mountain v1 instance configuration
					pddInstanceMapper.insertOrUpdateInstanceEngineVersion(instanceId, insEngineVersion);


					// 主实例变更，同时更新备库元数据
					if (role.equals("master")){
						List<Instance> backupInsList = instanceMapper.selectBackupByMasterId(instanceId);
						if (backupInsList != null && !backupInsList.isEmpty()) {
							String backupInsId = backupInsList.get(0).getInstanceId();
							if(!allInsList.contains(backupInsId)){
								// Update mountain v2 instance configuration
								instanceMapper.insertOrUpdateInstanceEngineVersion(backupInsId, insEngineVersion);

								// Update mountain v1 instance configuration
								pddInstanceMapper.insertOrUpdateInstanceEngineVersion(backupInsId, insEngineVersion);
							}
						}
					}

					tmpShowInfo.setAfterConfig(String.format("MySQL版本: %s", insEngineVersion));
					context.info(String.format(
							"同步元数据: %s, %s, %s, %s",
							instanceId, insIp, insPort, insEngineVersion));

					Map<String,Object> syncWoodsParam = new HashMap<String,Object>(){{
						put("version", insEngineVersion);
					}};
					final Pair<Boolean, String> booleanStringPair = woodsService.updateInstanceWoodsInfo(instanceId, syncWoodsParam);
					context.info(String.format("engine_version 场景同步Woods的结果：%s", booleanStringPair));
					break;
				}
				case "double_backup" :
				case "double_backup_sameZone":{
					context.info("开始同步实例 {} to double_backup upgrade results",
							instanceId);
					tmpShowInfo.setAfterConfig("MySQL部署架构: 一主两备");

					context.info("开始创建第二备库的可读IP.");

					// 创建第二备库可读IP，并且实时落元数据
					boolean sameZone = content.getSameZone();

					Pair<Boolean, String> result = mysqlCreateService.createSecondBackupIp(instanceId, context,sameZone);

					if (!result.getLeft()) {
						throw new RuntimeException(String.format("创建第二备库可读IP失败: %s", result.getRight()));
					}

					context.info("第二备库的可读IP已成功创建。");
					Map<String,Object> syncWoodsParam = new HashMap<String,Object>(){{
						put("product_type", "HA");
					}};
					final Pair<Boolean, String> booleanStringPair = woodsService.updateInstanceWoodsInfo(instanceId, syncWoodsParam);
					context.info(String.format("double_backup 场景同步Woods的结果：%s", booleanStringPair));
					break;
				}
				case "cross_idc_migrate":{

					log.info(String.format("cross_idc_migrate步骤中的context为：%s", context));
					// 从上面步骤中创建的CMPBody获取变更后的set、idc、zone信息
					MySQLChangeRequest serverBody = context.getClassObjectOutputs(REQUIREMENT_BODY,
							new TypeReference<MySQLChangeRequest>() {});

					if(serverBody.getRequirements().size()==0){
						throw new RuntimeException("获取实例变更后的目标单元、IDC、可用区信息失败");
					}

					final HashMap<String, String> migrateDest = serverBody.getRequirements().get(0).getMigrateDest();
					String destSet = migrateDest.get("set");
					String destIdc = migrateDest.get("idc");
					String destZone = migrateDest.get("zone");

					Instance instanceTemp = JSONUtils.deepCopy(instance, new TypeReference<Instance>() {
					});
					instanceTemp.setSet(destSet);
					instanceTemp.setIdc(destIdc);
					instanceTemp.setZone(destZone);
					instanceMapper.insertOrUpdateInstance(instanceTemp);

					// 主实例变更，同时更新备库元数据
					if (role.equals("master")){
						List<Instance> backupInsList = instanceMapper.selectBackupByMasterId(instanceId);
						if (backupInsList != null && !backupInsList.isEmpty()) {
							String backupInsId = backupInsList.get(0).getInstanceId();
							if(!allInsList.contains(backupInsId)){
								instance = instanceMapper.selectOneByInstanceId(backupInsId);
								instanceTemp = JSONUtils.deepCopy(instance, new TypeReference<Instance>() {});
								instanceTemp.setSet(destSet);
								instanceTemp.setIdc(destIdc);
								instanceTemp.setZone(destZone);
								instanceMapper.insertOrUpdateInstance(instanceTemp);
							}
						}
					}

					tmpShowInfo.setAfterConfig(String.format("跨区迁移:%s", tmpShowInfo.getAfterConfig()));
					context.info(String.format("同步元数据：%s, %s, %s, %s, %s, %s", instanceId,insIp,insPort,destSet,destIdc,destZone));
					// 同步至woods
					Map<String,Object> syncWoodsParam = new HashMap<String,Object>(){{
						put("opsType", "upgrade");
						put("set", destSet);
						put("idc", destIdc);
					}};
					Pair<Boolean, String> syncWoodsRt = woodsService.updateInstanceWoodsInfo(instanceId, syncWoodsParam);

					break;
				}
			}
			showInfos.add(tmpShowInfo);
		}

		context.putOutput(SHOW_INFOS, showInfos);
		context.info("实例元数据同步成功, showInfos:\n{}",
				JSONUtils.toJSONStringWithPretty(showInfos));

		// 发送消息
		try {
			sendInfo(context);
		} catch (Exception e) {
			context.info("向发起工单的用户发送完成通知失败: " + e.getMessage());
			log.error("[MySQLUpgradeJobHandler] syncUpgradeMetaData: sendInfo error", e);
		}
	}

	private void sendInfo(TFContext context) {
		String title = "Mountain平台Mysql配置变更执行成功";
		String mountainDir = String.format("%s/#/order/myrelease?env=%s&ordertype=otherOrder&searchval=%s", webDomain,
				context.getJobBean().getEnv(), context.getJobBean().getWorkId());
		String kkText = String.format(
				"Work order ID: %s\nWork order status: success\nEnvironment: %s\nOrder: %s\nWork order link: %s\nTrigger time: %s",
				context.getJobBean().getWorkId(), context.getJobBean().getEnv(), context.getJobBean().getApplyUser(),
				mountainDir, new Date());

		informService.sendMsgToDbaInform(Collections.singletonList(context.getJobBean().getApplyUser()), title, kkText);
	}

	@Data
	@Builder
	public static class ShowInfo {
		private String instanceId;
		private String ip;
		private Integer port;
		private String instanceName;
		private String afterConfig;
	}
}
