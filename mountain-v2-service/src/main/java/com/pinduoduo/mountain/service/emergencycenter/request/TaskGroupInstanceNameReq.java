/**
 * <AUTHOR>
 * @date 2025/4/17
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TaskGroupInstanceNameReq {
    @ApiModelProperty(value = "taskGroupId", required = true)
    private String taskGroupId;
    @ApiModelProperty(value = "ip", required = true)
    private String ip;
    @ApiModelProperty(value = "port", required = true)
    private String port;
}
