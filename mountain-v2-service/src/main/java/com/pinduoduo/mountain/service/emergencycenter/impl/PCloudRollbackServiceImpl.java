/**
 * <AUTHOR>
 * @date 2025/5/20
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.impl;

import com.pinduoduo.mountain.integration.rollback.IRollbackService;
import com.pinduoduo.mountain.integration.rollback.bo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class PCloudRollbackServiceImpl implements IRollbackService {


    private static final String cloud = "PCloud";
    @Override
    public List<RollbackRangeTime> describeRollbackRangeTime(List<RollbackInstanceInfo> instanceIdList) {
        List<RollbackRangeTime> result = new ArrayList<>();
        for (int i = 0; i < instanceIdList.size(); i++) {
            result.add(new RollbackRangeTime(Boolean.FALSE, "describeRollbackRangeTime not implemented", instanceIdList.get(i).getInstanceId(), null));
        }
        return result;
    }

    @Override
    public List<RollbackTaskDetail> describeRollbackTaskDetail(List<RollbackInstanceInfo> instanceIdList) {
        List<RollbackTaskDetail> result = new ArrayList<>();
        for (int i = 0; i < instanceIdList.size(); i++) {
            result.add(new RollbackTaskDetail(Boolean.FALSE, "describeRollbackTaskDetail not implemented", "", "","", -1, null, instanceIdList.get(i).getInstanceId()));
        }
        return result;
    }

    @Override
    public List<RollbackResp> batchRollback(List<RollbackBO> rollbackReqList) {
        List<RollbackResp> result = new ArrayList<>();
        for (int i = 0; i < rollbackReqList.size(); i++) {
            result.add(new RollbackResp(Boolean.FALSE, "batchRollback not implemented", cloud, "", rollbackReqList.get(i).getInstanceId(),""));
        }

        return result;
    }

    @Override
    public List<StopRollbackResp> stopRollback(List<RollbackInstanceInfo> strings) {
         List<StopRollbackResp> result = new ArrayList<>();
        for (int i = 0; i < strings.size(); i++) {
            result.add(new StopRollbackResp(Boolean.FALSE, "stopRollback not implemented", cloud, "", strings.get(i).getInstanceId(),""));
        }
        return result;
    }

    @Override
    public String getTargetRollbackName(RollbackTaskDetail.RollbackInfo rollbackInfo) {
        return rollbackInfo.getTarget();
    }
}
