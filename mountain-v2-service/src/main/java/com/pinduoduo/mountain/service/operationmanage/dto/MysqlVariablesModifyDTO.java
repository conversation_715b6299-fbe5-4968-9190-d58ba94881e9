package com.pinduoduo.mountain.service.operationmanage.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("实例参数修改DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MysqlVariablesModifyDTO {
    @ApiModelProperty(value = "实例id列表", required = true)
    private List<String> instanceIdList;

    @ApiModelProperty(value = "修改原因", required = true)
    private String modifyComment;

    private ModifyData modifyData;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ModifyData{

        private String parameterName; //参数名

        private Integer needReboot;

        private String paramType;

        private String enumValue;

        private Long maxValue;

        private Long minValue;

        private Object oldValue;

        private Object newValue;

        private String dbVersion;

    }

}
