package com.pinduoduo.mountain.service.emergencycenter.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.common.thirdparty.avi.AviService;
import com.pinduoduo.mountain.common.thirdparty.panda.PandaFileResolver;
import com.pinduoduo.mountain.common.thirdparty.panda.PandaService;
import com.pinduoduo.mountain.common.util.FileZipUtil;
import com.pinduoduo.mountain.common.util.RandomUtil;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.integration.commandexecutor.CommandExecutor;
import com.pinduoduo.mountain.integration.commandexecutor.FileDownloader;
import com.pinduoduo.mountain.integration.systemconfig.bo.ProcessResultBO;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.*;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.*;
import com.pinduoduo.mountain.service.emergencycenter.bo.*;
import com.pinduoduo.mountain.service.emergencycenter.dto.*;
import com.pinduoduo.mountain.service.emergencycenter.request.DownloadFileReq;
import com.pinduoduo.mountain.service.emergencycenter.request.FlashbackDownloadAllReq;
import com.pinduoduo.mountain.service.emergencycenter.request.FlashbackLogicTaskReq;
import com.pinduoduo.mountain.service.emergencycenter.request.FlashbackTaskReq;
import com.pinduoduo.mountain.service.emergencycenter.response.DownloadFileResp;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FlashbackService {

    private final LogicTableMapper logicTableMapper;
    private final InstanceTableInfoMapper instanceTableInfoMapper;
    private final PhysicalClusterMapper physicalClusterMapper;
    private final PhyClusterInstanceRelationMapper phyClusterInstanceRelationMapper;
    private final PandaService pandaService;
    private final PandaFileResolver pandaFileResolver;
    private final FlashbackTaskGroupMapper flashbackTaskGroupMapper;

    private final String TYPE_LOGIC = "logic";
    private final String TYPE_INSTANCE = "instance";
    private final String ROLLBACK = "rollback";
    private final String ORIGIN = "origin";

    private final LogicDatabaseMapper logicDatabaseMapper;

    public List<FlashbackPandaExecuteHostBO> listPandaExecuteHost() {
        String leoKey = "mountain-v2-api.flashback.panda_execute_host";
        List<FlashbackPandaExecuteHostBO> words = LeoUtils.getJsonProperty(leoKey, new TypeReference<List<FlashbackPandaExecuteHostBO>>() {});
        return words;
    }

    public FlashbackTaskDTO retrieveFlashbackTaskByGroupIdAndIpPort(String taskGroupId, String ip, String port) {
        String targetKey = ip+"::"+ port;
        List<FlashbackTask> flashbackTasks = this.flashbackTaskMapper.retrieveTaskByGroupId(taskGroupId);
        List<FlashbackTask> filteredTasks = new ArrayList<>(16);
        for (FlashbackTask flashbackTask : flashbackTasks) {
            String k = flashbackTask.getIp() + "::" + flashbackTask.getPort();
            if (!StringUtils.equals(k ,targetKey)){
                continue;
            }
            filteredTasks.add(flashbackTask);
        }
        if (filteredTasks.isEmpty()){
            return null;
        }
        FlashbackTask task0 = filteredTasks.get(0);
        FlashbackTaskDTO dto = this.transfer(task0);

        Set<String> dbnames = new HashSet<>();
        Set<String> tableList = new HashSet<>();
        StringBuilder executeLog = new StringBuilder();
        StringBuilder createLog = new StringBuilder();
        FlashbackSampleCountBO originSample = new FlashbackSampleCountBO(new ArrayList<>(), 0L);
        FlashbackSampleCountBO rollbackSample = new FlashbackSampleCountBO(new ArrayList<>(), 0L);

        for (FlashbackTask filteredTask : filteredTasks) {
            dbnames.add(filteredTask.getDbname());
            List<String> flashbackTaskTableList  = new ArrayList<>();
            try{
                flashbackTaskTableList = JSON.parseArray(filteredTask.getTableList(), String.class);
            }catch (Exception e){
                log.error("JSON.parseArray(filteredTask.getTableList(), String.class): "+e.getMessage());
            }

            for (String e : flashbackTaskTableList) {
                tableList.add(filteredTask.getDbname() +"." + e);
            }
            if (filteredTask.getExecuteLog()!=null){
                executeLog.append(filteredTask.getExecuteLog()).append("\n");
            }
            if (filteredTask.getCreateLog()!=null){
                createLog.append(filteredTask.getCreateLog()).append("\n");
            }
            try {
                FlashbackSampleCountBO originSampleStr = JSON.parseObject(filteredTask.getOriginFileSample(), FlashbackSampleCountBO.class);
                originSample.getSamples().addAll(originSampleStr.getSamples());
                originSample.setSize(originSample.getSize() + originSampleStr.getSize());
            } catch (Exception e) {
                log.error("FlashbackService.retrieveFlashbackTaskByIds transfer originSampleStr jsonStr err {}", e.getMessage());
            }
            try {
                FlashbackSampleCountBO rollbackSampleStr = JSON.parseObject(filteredTask.getRollbackFileSample(), FlashbackSampleCountBO.class);
                rollbackSample.getSamples().addAll(rollbackSampleStr.getSamples());
                rollbackSample.setSize(rollbackSample.getSize() + rollbackSampleStr.getSize());
            } catch (Exception e) {
                log.error("FlashbackService.retrieveFlashbackTaskByIds transfer originSampleStr jsonStr err {}", e.getMessage());
            }
        }

        dto.setDbname(String.join(",", dbnames));
        dto.setTableList(new ArrayList<String>(tableList));
        dto.setExecuteLog(executeLog.toString());
        dto.setCreateLog(createLog.toString());


        if (originSample.getSamples().size() > this.sqlSampleCount){
            List<String> strings = originSample.getSamples().subList(0, this.sqlSampleCount);
            originSample.setSamples(strings);
        }
        if (rollbackSample.getSamples().size() > this.sqlSampleCount){
            List<String> strings = rollbackSample.getSamples().subList(0, this.sqlSampleCount);
            rollbackSample.setSamples(strings);
        }
        dto.setOriginFileSample(originSample);
        dto.setRollbackFileSample(rollbackSample);

        Integer originSQLSampleSize = calAverageSqlSampleSize(dto.getOriginFileSample());
        Integer rollbackSQLSampleSize = calAverageSqlSampleSize(dto.getRollbackFileSample());
        if(originSQLSampleSize > sqlSampleSize || rollbackSQLSampleSize> sqlSampleSize){
            dto.getOriginFileSample().setSamples(new ArrayList<>());
            dto.getRollbackFileSample().setSamples(new ArrayList<>());
        }
        return dto;
    }

    public DownloadFileResp downloadAll(FlashbackDownloadAllReq req) {
        DownloadFileResp resp = new DownloadFileResp();
        List<String> filePathList = new ArrayList<>();
        try{
            for (Long id : req.getTaskIds()) {
                DownloadFileResp downloadFileResp = this.download(new DownloadFileReq(id, req.getCate()));
                if (downloadFileResp!=null && downloadFileResp.getResult() && StringUtils.isNotEmpty(downloadFileResp.getPath())){
                    filePathList.add(downloadFileResp.getPath());
                }
            }
            String zippedPath = FileZipUtil.zipFiles(filePathList);
            resp.setResult(true);
            resp.setPath(zippedPath);
            resp.setMsg("success");
            return resp;
        }catch (Exception e){
            log.error("flashback service downloadAll error", e);
            resp.setMsg(e.getMessage());
        }
        resp.setResult(false);
        return resp;
    }


    public Integer softDeleteByTaskGroupId(String taskGroupId) {

        this.flashbackTaskGroupMapper.deleteByTaskGroupId(taskGroupId);
        return this.flashbackTaskMapper.deleteByTaskGroupId(taskGroupId);
    }

    public List<FlashbackTaskDTO> retrieveFlashbackTaskByGroupId(String taskGroupId) {
        List<String> taskGroupIds = new ArrayList<>();
        taskGroupIds.add(taskGroupId);
        List<FlashbackTask> flashbackTasks = this.flashbackTaskMapper.retrieveTaskByGroupIdList(taskGroupIds);
        List<FlashbackTaskDTO> dtos = new ArrayList<>();
        for (FlashbackTask flashbackTask : flashbackTasks) {
            FlashbackTaskDTO dto = this.transfer(flashbackTask);
           dtos.add(dto);
        }
        return dtos;
    }

    enum Status {
         NONE(0),
        SUCCESS(1),
        FAILED(2),
        RUNNING(4);


         private int value;

         Status(int i) {
             this.value = i;
         }
     }

     enum Storage {
        NONE("none"),
         AVI_PFS("avi_pfs"),
         PANDA("panda");


        private String value;

         Storage(String aviPfs) {
             this.value = aviPfs;
         }
     }

    enum Executor {

        LOACAL("local"),
        PANDA("panda");

        private String value;

        Executor(String e) {
            this.value = e;
        }
    }

    private final String mountainRoUser;

    private final String mountainRoPassword;

    private final String mountainRoHtjPassword;

    private final String mountainDbRoPassword;

    private final String mountainDbRoSpecialProdPasswdHost;


    private final InstanceMapper instanceMapper;

    private final ResourceLoader resourceLoader;

    // In all cases, if we need to upload files to somewhere, it must be avi
    private final AviService aviService;
    private final FlashbackTaskMapper flashbackTaskMapper;

    private String originSqlFileKey = "原sql文件保存路径:";
    private Pattern patternOriginSqlFileRegex = Pattern.compile(originSqlFileKey+"(.*)\n");
    private String rollbackSqlFileKey = "回滚sql文件保存路径:";
    private Pattern patternRollbackSqlFileRegex = Pattern.compile(rollbackSqlFileKey + "(.*)\n");

    private String OS_DARWIN = "darwin";
    private static final String TASK_FLASHBACK = "flashback";


    private MutexTaskManager mutexTaskManager;

    private Integer sqlSampleCount;

    private Integer sqlSampleSize;

    private String defaultStorageType;
    private String defaultCommandExecutor;
    private String defaultCommandDir;
    private String defaultResultDir;

    private CommandExecutor commandExecutor;

    private Integer paraller = 4;

    private String flashbackExecuteUser;
    private String flashbackExecutePassword;




    public FlashbackService(@Qualifier("instanceMapper") InstanceMapper instanceMapper,
                            ResourceLoader resourceLoader, AviService aviService,
                            @Qualifier("flashbackTaskMapper") FlashbackTaskMapper flashbackTaskMapper,
                            MutexTaskManager m, @Qualifier("logicTableMapper") LogicTableMapper logicTableMapper,
                            @Qualifier("instanceTableInfoMapper") InstanceTableInfoMapper instanceTableInfoMapper,
                            LocalCommandExecutor localCommandExecutor,
                            PandaCommandExecutor pandaCommandExecutor,
                            @Qualifier("physicalClusterMapper") PhysicalClusterMapper physicalClusterMapper, @Qualifier("phyClusterInstanceRelationMapper") PhyClusterInstanceRelationMapper phyClusterInstanceRelationMapper, PandaService pandaService, PandaFileResolver pandaFileResolver, @Qualifier("flashbackTaskGroupMapper") FlashbackTaskGroupMapper flashbackTaskGroupMapper, LogicDatabaseMapper logicDatabaseMapper, PlatformTransactionManager pdbLogRepositoryPTM) {
        this.instanceMapper = instanceMapper;
        this.resourceLoader = resourceLoader;
        this.aviService = aviService;
        mountainRoUser = LeoUtils.getStringProperty("mountain-backend.mountain_db_ro_user");
        mountainRoPassword = LeoUtils.getStringProperty("dba-mountain.mountain_ro.password");
        mountainRoHtjPassword = LeoUtils.getStringProperty("dba-mountain.dba_mountain_ro.htj.password");
        mountainDbRoPassword = LeoUtils.getStringProperty("dba-mountain.dba_mountain_ro.password");
        mountainDbRoSpecialProdPasswdHost = LeoUtils.getStringProperty("dba-mountain.dba_mountain_ro.special_prod_passwd_host", "");
        this.flashbackTaskMapper = flashbackTaskMapper;
        this.mutexTaskManager = m;
        this.sqlSampleCount = LeoUtils.getIntProperty("mountain-v2-api.flashback.sql_sample_count", 500);
        this.sqlSampleSize = LeoUtils.getIntProperty("mountain-v2-api.flashback.sql_sample_size", 65535);
        this.logicTableMapper = logicTableMapper;
        this.instanceTableInfoMapper = instanceTableInfoMapper;
        //        默认存储方式为 none，表示不存储
        this.defaultStorageType = LeoUtils.getStringProperty("mountain-v2-api.flashback.storage_type", "panda");
        this.defaultCommandExecutor = LeoUtils.getStringProperty("mountain-v2-api.flashback.executor_type", "local");

        if (defaultCommandExecutor.equals(Executor.LOACAL.value)){
            this.commandExecutor  = localCommandExecutor;
        }else if(defaultCommandExecutor.equals(Executor.PANDA.value)){
            this.commandExecutor = pandaCommandExecutor;
        }
        this.defaultCommandDir = LeoUtils.getStringProperty("mountain-v2-api.flashback.executor_command_dir", "/data0/yinglong/app/");
        this.defaultResultDir = LeoUtils.getStringProperty("mountain-v2-api.flashback.result_dir","/data0/share-log/rollback-sqls/");
        this.physicalClusterMapper = physicalClusterMapper;
        this.phyClusterInstanceRelationMapper = phyClusterInstanceRelationMapper;
        this.pandaService = pandaService;
        this.pandaFileResolver = pandaFileResolver;
        this.flashbackTaskGroupMapper = flashbackTaskGroupMapper;
        this.logicDatabaseMapper = logicDatabaseMapper;
        this.paraller = LeoUtils.getIntProperty("mountain-v2-api.flashback.execute.paraller", 4);
        this.flashbackExecuteUser = LeoUtils.getStringProperty("mountain-v2-api.flashback_user");
        this.flashbackExecutePassword = LeoUtils.getStringProperty("mountain-v2-api.flashback_password");
    }

    public FlashbackTaskSubmitResultDTO submitInstanceTask(String username, List<FlashbackTaskReq> req) {
        List<FlashbackTaskBO> taskBOS = new ArrayList<>();
        for (FlashbackTaskReq t : req) {
            FlashbackTaskBO flashbackTaskBO = new FlashbackTaskBO();
            flashbackTaskBO.setSqlType(t.getSqlType());
            flashbackTaskBO.setIp(t.getIp());
            flashbackTaskBO.setPort(t.getPort());
            Instance instance = instanceMapper.selectOneByIpPort(t.getIp(), Integer.parseInt(t.getPort()));
            flashbackTaskBO.setInstanceName(instance.getInstanceName());
            flashbackTaskBO.setDbname(t.getDbname());
            flashbackTaskBO.setTbname(t.getTbname());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            flashbackTaskBO.setStartTime(LocalDateTime.parse(t.getStartTime(), formatter));
            flashbackTaskBO.setEndTime(LocalDateTime.parse(t.getEndTime(), formatter));
            flashbackTaskBO.setSessionId(t.getSessionId());
            flashbackTaskBO.setMatchSql(t.getMatchSql());
            flashbackTaskBO.setCreator(username);
            flashbackTaskBO.setExecuteHost(t.getExecuteHost());
            taskBOS.add(flashbackTaskBO);
        }
        FlashbackTaskSubmitResultDTO resultDTO = this.submitTask(taskBOS);


        List<FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO> returnTaskList = resultDTO.getTaskList();
        List<String> dbnames = new ArrayList<>();
        List<String> tableNames = new ArrayList<>();
        List<String> ips = new ArrayList<>();
        List<String> matchSqls = new ArrayList<>();

        for (FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO flashbackTaskReq : returnTaskList) {
            dbnames.add(flashbackTaskReq.getDbname());
            tableNames.addAll(flashbackTaskReq.getTbname());
            ips.add(flashbackTaskReq.getIp());
            matchSqls.add(flashbackTaskReq.getMatchSql());
        }

        String taskGroupId = resultDTO.getTaskGroupId();
        FlashbackTaskGroup flashbackTaskGroup = new FlashbackTaskGroup();
        flashbackTaskGroup.setTaskGroupId(taskGroupId);
        flashbackTaskGroup.setDbname(JSON.toJSONString(dbnames));
        flashbackTaskGroup.setTableList(JSON.toJSONString(tableNames));
        flashbackTaskGroup.setIp(String.join(",",ips));
        flashbackTaskGroup.setCreator(username);
        flashbackTaskGroup.setMatchSql(JSON.toJSONString(matchSqls));
        flashbackTaskGroup.setCate(TYPE_INSTANCE);

        List<FlashbackTaskGroup> l = new ArrayList<>();
        l.add(flashbackTaskGroup);
        this.flashbackTaskGroupMapper.batchSubmit(l);

        return resultDTO;
    }

    public FlashbackTaskSubmitResultDTO submitLogicTask(String username, List<FlashbackLogicTaskReq> req) {
        List<FlashbackTaskBO> taskBOS = new ArrayList<>();
//        select i.instance_id,lt.logic_table_id,lt.cluster_id,physical_cluster_id, iti.db_name,iti.table_name
//        from instance i, instance_table_info iti,logic_table lt,physical_cluster pc
//        where i.instance_id=iti.instance_id
//          and lt.logic_table_id=iti.logic_table_id
//          and pc.cluster_id=lt.cluster_id
//          and iti.logic_table_id in (202501161625430000,202503032047031490,202502141421061641) order by instance_id;
        // phy_cluster_instance_relation
        for (FlashbackLogicTaskReq t : req) {
            List<InstanceTableInfo> targetTables = new ArrayList<>();
            List<Long> logicTables = t.getLogicTables();
            // logic tables should not be empty
            if (logicTables == null || logicTables.isEmpty()) {
                FlashbackTaskSubmitResultDTO resultDTO = new FlashbackTaskSubmitResultDTO();
                resultDTO.setResult(false);
                resultDTO.setMessage("logic_tables should not be empty");
                return resultDTO;
            }
            // user pas through logic tables
            List<InstanceTableInfo> instanceTableList = instanceTableInfoMapper.selectByLogicTableIds(logicTables);
            List<String> physicalClusterInstances = null;
            if (t.getPhysicalClusterIds()!=null && !t.getPhysicalClusterIds().isEmpty()){
                physicalClusterInstances = phyClusterInstanceRelationMapper.selectInstanceIdsByPhysicalClusterIds(t.getPhysicalClusterIds());
            }
            // which means use pass through physical cluster id list
            if (physicalClusterInstances != null) {
                for (InstanceTableInfo tableInfo : instanceTableList) {
                    if(CollectionUtils.contains(physicalClusterInstances.iterator(), tableInfo.getInstanceId())){
                        targetTables.add(tableInfo);
                    }
                }
            }else {
                targetTables.addAll(instanceTableList);
            }
            // (3) retrieve all relevant physical tables by logic tables
            // need to validate if these physical tables belong to this logic tables
            List<Long> physicalTableIds = t.getPhysicalTableIds();
            if (physicalTableIds!=null && !physicalTableIds.isEmpty()) {
                List<InstanceTableInfo> instanceTableInfos = instanceTableInfoMapper.selectByIds(physicalTableIds);
                // validate physical table should belong to logic tables
                for (InstanceTableInfo info : instanceTableInfos) {
                    if (!logicTables.isEmpty() && !logicTables.contains(info.getLogicTableId())){
                        FlashbackTaskSubmitResultDTO resultDTO = new FlashbackTaskSubmitResultDTO();
                        resultDTO.setResult(false);
                        String m = String.format("physical_table id: %d name: %s %s.%s not belong to logic tables: %s",info.getId(),info.getInstanceId(),info.getDbName(),info.getTableName(), t.getLogicTables());
                        resultDTO.setMessage(m);
                        return resultDTO;
                    }
                    // validate physical table should belong to physical clusters
                    if (!physicalClusterInstances.isEmpty() && !physicalClusterInstances.contains(info.getInstanceId())){
                        FlashbackTaskSubmitResultDTO resultDTO = new FlashbackTaskSubmitResultDTO();
                        resultDTO.setResult(false);
                        String m = String.format("physical_table id: %d name: %s %s.%s not belong to physical clusters: %s",info.getId(),info.getInstanceId(),info.getDbName(),info.getTableName(), t.getPhysicalClusterIds());
                        resultDTO.setMessage(m);
                        return resultDTO;
                    }
                }
                targetTables.addAll(instanceTableInfos);
            }
            List<InstanceTableInfo> distinctTargetTables  =new ArrayList<>();
            Set<Long> tmp = new HashSet<>();
            for (InstanceTableInfo targetTable : targetTables) {
                Long id = targetTable.getId();
                if (tmp.contains(id)){
                    continue;
                }
                tmp.add(id);
                distinctTargetTables.add(targetTable);
            }
            // distinctTargetTables contains all needed instance tables
            Set<String> instanceIdsSet = new HashSet<>();
            for (InstanceTableInfo tableInfo : distinctTargetTables) {
                instanceIdsSet.add(tableInfo.getInstanceId());
            }
            List<String> instanceIds = new ArrayList<>(instanceIdsSet);
            List<Instance> instances = instanceMapper.selectByInstanceIdList(instanceIds);
            Map<String, Instance> instanceMap = new HashMap<>(16);
            for (Instance instance : instances) {
                instanceMap.put(instance.getInstanceId(), instance);
            }
            // 若物理表在同一个实例，同一个数据库下，则属于同一个闪回任务
//            SortedMap<String, List<InstanceTableInfo>> distinctInstanceDbTask = new TreeMap<>();
//            for (InstanceTableInfo tableInfo : instanceTableList)  {
//                String k = tableInfo.getInstanceId() +"###"+tableInfo.getDbName();
//                distinctInstanceDbTask.computeIfAbsent(k, k1 -> new ArrayList<>()).add(tableInfo);
//            }
            for (InstanceTableInfo info : distinctTargetTables) {
//                String key = stringListEntry.getKey();
//                List<InstanceTableInfo> tableInfoList = stringListEntry.getValue();
//                if (info.isEmpty()){
//                    continue;
//                }
                InstanceTableInfo tableInfoSample = info;
                FlashbackTaskBO taskBO = new FlashbackTaskBO();
                taskBO.setSqlType(t.getSqlType());
                Instance instance = instanceMap.get(tableInfoSample.getInstanceId());
                taskBO.setIp(instance.getIp());
                taskBO.setPort(String.valueOf(instance.getPort()));
                taskBO.setDbname(tableInfoSample.getDbName());
                List<String> tbnames=  new ArrayList<>();
                tbnames.add(info.getTableName());
//                for (InstanceTableInfo tableInfo : tableInfoList) {
//                    tbnames.add(tableInfo.getTableName());
//                }
                Collections.sort(tbnames);
                taskBO.setTbname(tbnames);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                taskBO.setStartTime(LocalDateTime.parse(t.getStartTime(), formatter));
                taskBO.setEndTime(LocalDateTime.parse(t.getEndTime(), formatter));
                taskBO.setSessionId(taskBO.getSessionId());
                taskBO.setMatchSql(t.getMatchSql());
                taskBO.setCreator(username);
                taskBO.setSessionId(t.getSessionId());
                taskBO.setExecuteHost(t.getExecuteHost());
                taskBO.setInstanceName(instance.getInstanceName());
                taskBOS.add(taskBO);
            }
        }
        taskBOS.sort(new Comparator<FlashbackTaskBO>() {
            @Override
            public int compare(FlashbackTaskBO o1, FlashbackTaskBO o2) {
                String dbname1 = o1.getDbname();
                String dbname2 = o2.getDbname();
                if (!StringUtils.equals(dbname1, dbname2)) {
                    return dbname1.compareTo(dbname2);
                }
                 return o1.getTbname().toString().compareTo( o2.getTbname().toString());
            }
        });


        FlashbackTaskSubmitResultDTO resultDTO = this.submitTask(taskBOS);

        List<FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO> returnTaskList = resultDTO.getTaskList();


        FlashbackTaskGroup flashbackTaskGroup = new FlashbackTaskGroup();
        List<String> ips = new ArrayList<>();

        for (FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO flashbackTaskReq : returnTaskList) {
            ips.add(flashbackTaskReq.getIp());
        }


        Set<String> dbnameSet = new HashSet<>();
        Set<String> tablenameSet = new HashSet<>();
        for (FlashbackLogicTaskReq taskReq : req) {
            List<Long> logicTables = taskReq.getLogicTables();

            if (!logicTables.isEmpty()) {
                for (Long logicTableId : logicTables) {
                    LogicTable logicTable = logicTableMapper.selectOneByLogicTableId(logicTableId);
                    tablenameSet.add(logicTable.getLogicTableName());
                    LogicDatabase logicDatabase = logicDatabaseMapper.selectOneByLogicDbId(logicTable.getLogicDbId());
                    dbnameSet.add(logicDatabase.getLogicDbName());
                }
            }
        }

        ArrayList<String> dbnameList = new ArrayList<>(dbnameSet);
        ArrayList<String> tablenameList = new ArrayList<>(tablenameSet);
        flashbackTaskGroup.setDbname(JSON.toJSONString(dbnameList));
        flashbackTaskGroup.setTableList(JSON.toJSONString(tablenameList));
        String taskGroupId = resultDTO.getTaskGroupId();

        flashbackTaskGroup.setTaskGroupId(taskGroupId);


        flashbackTaskGroup.setIp(String.join(",",ips));
        flashbackTaskGroup.setCreator(username);

        flashbackTaskGroup.setCate(TYPE_LOGIC);
        List<FlashbackTaskGroup> l = new ArrayList<>();
        l.add(flashbackTaskGroup);
        this.flashbackTaskGroupMapper.batchSubmit(l);

        return resultDTO;
    }

    private String generateFileName(String prefix, FlashbackTask t) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = new Date();
        String dateString = formatter.format(currentDate);
        String tableListStr = t.getTableList();
        List<String> parsedTableNames = JSON.parseArray(tableListStr, String.class);
        parsedTableNames.sort(String::compareTo);
        // Notice that this rule is not the filename that can be generated by yinglong
        String filename = String.format("%s_%s_%d_%s_%s_%s.%s.sql",
                prefix, t.getIp(), t.getPort(), t.getDbname(), parsedTableNames.get(0),
                dateString,StringUtil.generateRandomString(8));
        return filename;
    }

    public FlashbackRetrieveResultBO flashbackRetrieve(String username, String password, FlashbackTask t) {
        FlashbackRetrieveResultBO bo = new FlashbackRetrieveResultBO();
        FlashbackTaskDTO to = this.transfer(t);
        bo.setTask(to);
        List<String> commandArgs = new ArrayList<>();
        String tmpDir = System.getProperty("java.io.tmpdir");
        String saveDir = tmpDir;
        if (StringUtils.isNotEmpty(this.defaultResultDir)){
            saveDir = this.defaultResultDir;
        }
        saveDir = saveDir + t.getTaskGroupId() ;
        String encryptedResultSaveDir = saveDir +".encrypted/";
        commandArgs.add("create");
        commandArgs.add("--db-host=" + t.getIp());
        commandArgs.add("--db-port=" + t.getPort());
        commandArgs.add("--db-username=" + username);
        commandArgs.add("--db-password=" + password);
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTime = t.getStartTime().format(pattern);
        commandArgs.add("--start-time=" + startTime);
        String endTime = t.getEndTime().format(pattern);
        commandArgs.add("--end-time="+ endTime);
        commandArgs.add("--save-dir=" + saveDir);

        String sqlType = t.getSqlType();
        String all = "all";
        List<String> parsedSqlTypes = JSON.parseArray(sqlType, String.class);
        String insert = "insert";
        if (!parsedSqlTypes.contains(insert)) {
            commandArgs.add("--enable-rollback-insert=false");
        }
        String update = "update";
        if (!parsedSqlTypes.contains(update)) {
            commandArgs.add("--enable-rollback-update=false");
        }
        String delete = "delete";
        if (!parsedSqlTypes.contains(delete)) {
            commandArgs.add("--enable-rollback-delete=false");
        }
        if (!StringUtils.isEmpty(t.getDbname())) {
            String tableListStr = t.getTableList();
            List<String> parsedTableNames = JSON.parseArray(tableListStr, String.class);
            if (!parsedTableNames.isEmpty()) {
               List<String> rollbackTables = new ArrayList<>();
                for (String tn : parsedTableNames) {
                    rollbackTables.add(t.getDbname() +"."+tn);
                }
                String rollbackTablesCommandArgs = String.join(",",rollbackTables);
                commandArgs.add("--rollback-table=" + rollbackTablesCommandArgs);
            } else {
                commandArgs.add("--rollback-schema=" + t.getDbname());
            }
        }
        if (!StringUtils.isEmpty(t.getMatchSql())) {
            commandArgs.add("--match-sql=" + t.getMatchSql());
        }
        if (!StringUtils.isEmpty(t.getSessionId())) {
            commandArgs.add("--thread-id="+t.getSessionId());
        }
        String dir = "";
        // panda need this config
         if (StringUtils.equals(defaultCommandExecutor, Executor.PANDA.value)){
             dir = defaultCommandDir;
        }
        String commandline = dir+ "yinglong";
        String os = System.getenv("GOHOSTOS");
        // HINT: 该文件比较大，因此darwin的未存放到resoureces文件夹中
        if (StringUtils.equals(os, OS_DARWIN)) {
            commandline = commandline + "_" + OS_DARWIN;
        }
        this.flashbackTaskMapper.updateStatus(t.getId(), Status.RUNNING.value);
        String executeHost = t.getExecuteHost();
        String traceId = String.format("%s#%s:%s#%s.%s",t.getId() , t.getIp(), t.getPort(), t.getDbname(), t.getTableList().toString());
        ProcessResultBO<String> processResultBO  = this.commandExecutor.process(traceId,executeHost,commandline, commandArgs);
        bo.setExecuteLog(processResultBO.getMsg());
        Boolean executeError = Boolean.FALSE;
        if (bo.getExecuteLog().contains("No such file or directory")){
            executeError = Boolean.TRUE;
        }

        if (processResultBO.getCode()!=0){
            bo.setResult(false);
            bo.setMessage("command execute failed");
            return  bo;
        }
        Matcher originalSqlFileMatcher = patternOriginSqlFileRegex.matcher(processResultBO.getMsg());
        Matcher rollbackSqlFileMatcher = patternRollbackSqlFileRegex.matcher(processResultBO.getMsg());

        String originSqlFilePath = "";
        String rollbackSqlFilePath = "";

        if (originalSqlFileMatcher.find()) {
            originSqlFilePath = originalSqlFileMatcher.group();
            originSqlFilePath = originSqlFilePath.replace(originSqlFileKey, "").trim();
            bo.setOriginSqlFile(originSqlFilePath);
        }
        if (rollbackSqlFileMatcher.find()) {
            rollbackSqlFilePath = rollbackSqlFileMatcher.group();
            rollbackSqlFilePath = rollbackSqlFilePath.replace(rollbackSqlFileKey, "").trim();
            bo.setRollbackSqlFile(rollbackSqlFilePath);
        }
        Date currentDate = new Date();
        // 创建SimpleDateFormat对象，定义日期格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String tableListStr = t.getTableList();
        List<String> parsedTableNames = JSON.parseArray(tableListStr, String.class);
        parsedTableNames.sort(String::compareTo);
        String dateString = formatter.format(currentDate);
        if(StringUtils.isEmpty(originSqlFilePath) && !executeError){
            // if can not extract from executeLog, we could use templat// filename： ${实例名}_${ip}_${port}_${dbname}_${tablename}_${date +"%F-%T"}.sql

            // 原sql文件保存路径: /tmp/origin_172.22.5.40_3306_pdd_mountain_pdd_instance_2025-04-06.sql

            String filepath = String.format("%s/origin_%s_%d_%s_%s_%s.sql", saveDir ,
                    t.getIp(), t.getPort(), t.getDbname(), parsedTableNames.get(0), dateString);
            originSqlFilePath = filepath;

            bo.setOriginSqlFile(originSqlFilePath);
        }
        if (StringUtils.isEmpty(rollbackSqlFilePath) && !executeError){
            // if can not extract from executeLog, we could use template
            // 回滚sql文件保存路径: /tmp/rollback_172.22.5.40_3306_pdd_mountain_pdd_instance_2025-04-06.sql
            String filepath = String.format("%s/rollback_%s_%d_%s_%s_%s.sql",saveDir,
                    t.getIp(), t.getPort(), t.getDbname(), parsedTableNames.get(0), dateString);
            rollbackSqlFilePath = filepath;
            bo.setRollbackSqlFile(rollbackSqlFilePath);
        }
//        String l = System.getenv("LOG_LEVEL");
//        if (StringUtils.equals(l, "yuanxili")){
//            originSqlFilePath = "/Users/<USER>/IdeaProjects/notes/mountain/mountain-v2/急救中心/MOUNTAIN-6090-闪回/mountainv1-htj.http";
//            rollbackSqlFilePath = "/Users/<USER>/IdeaProjects/notes/mountain/mountain-v2/急救中心/MOUNTAIN-6090-闪回/mountainv2-local.http";
//        }
        log.info("flashbackRetrieve originSqlFilePath: {} rollbackSqlFilePath: {}", originSqlFilePath, rollbackSqlFilePath);

        if (StringUtils.isEmpty(originSqlFilePath) || StringUtils.isEmpty(rollbackSqlFilePath)) {
            bo.setResult(false);
            bo.setMessage("flashbackRetrieve flashback execute failed, log: " + processResultBO.getMsg());
            return bo;
        }
        File originSqlFile = new File(originSqlFilePath);
        File rollbackSqlFile = new File(rollbackSqlFilePath);
        bo.setResult(true);
        bo.setMessage("success");


        try {
            // HINT can be extract
            FileDownloader downloader = null;
            if (StringUtils.equals(defaultStorageType, Storage.AVI_PFS.value)){
                downloader = aviService;
                bo.setStorage(Storage.AVI_PFS.value);
                // when flashback in Pod, we could check if the result sql file exists
                if (!originSqlFile.exists()) {
                    bo.setResult(false);
                    String m = "flashbackRetrieve originSqlFile not exist: " + originSqlFilePath;
                    bo.setExecuteLog(bo.getExecuteLog() + "\n" + m);
                    log.error(m);
                    bo.setMessage(m);
                    return bo;
                }
                if (!rollbackSqlFile.exists()) {
                    bo.setResult(false);
                    String m = "flashbackRetrieve rollbackSqlFile not exist: " + rollbackSqlFilePath;
                    bo.setExecuteLog(bo.getExecuteLog() + "\n" + m);
                    bo.setMessage(m);
                    return bo;
                }
            } else if (StringUtils.equals(defaultStorageType, Storage.PANDA.value)) {
                // execute yinglong through panda, we need to encrypt the target file
                // must be success when come to here
                downloader = pandaFileResolver;
                bo.setStorage(Storage.PANDA.value);
                // Next, we need to encrypt these files
                // executeHost must exist when using panda storage type
                // encrypt: openssl enc -aes-256-cbc -in Main.java -out Main.java.encrypted -pass pass:12321
                // decrypt: openssl enc -aes-256-cbc -d -in Main.java.encrypted -out Main.java.decrypted -pass pass:12321
                // /tmp/origin_172.22.5.40_3306_pdd_mountain_pdd_instance_2025-04-06.sql
                // /tmp/.encrypted/origin_172.22.5.40_3306_pdd_mountain_pdd_instance_2025-04-06.sql
            }
            String originFileUrlEncrypted = downloader.upload(originSqlFile, executeHost);
            if (StringUtils.isEmpty(originFileUrlEncrypted)){
                bo.setResult(false);
                String m = "flashbackRetrieve originFileUrlEncrypted failed!";
                bo.setExecuteLog(bo.getExecuteLog() +"\n"+ m);
                log.error(m);
                bo.setMessage(m);
                return bo;
            }
            log.info("flashbackRetrieve originFileUrl path: {} url:{}", originSqlFile.getAbsoluteFile(), originFileUrlEncrypted);
            bo.setEncryptedOriginFile(originFileUrlEncrypted);
            String rollbackFileUrlEncrypted = downloader.upload(rollbackSqlFile, executeHost);
            if (StringUtils.isEmpty(rollbackFileUrlEncrypted)){
                bo.setResult(false);
                String m = "flashbackRetrieve rollbackFileUrlEncrypted failed!";
                bo.setExecuteLog(bo.getExecuteLog() +"\n"+ m);
                log.error(m);
                bo.setMessage(m);
                return bo;
            }
            bo.setEncryptedRollbackFile(rollbackFileUrlEncrypted);

            this.flashbackTaskMapper.updateEncryptedResultFile(t.getId(), originFileUrlEncrypted, rollbackFileUrlEncrypted, defaultStorageType);

            String originFn = this.generateFileName(ORIGIN, t);
            File originSqlFileDownloaded = new File(downloader.download(bo.getEncryptedOriginFile(), originFn));

            String rollbackFn = this.generateFileName(ROLLBACK, t);
            File rollbackSqlFileDownloaded = new File(downloader.download(bo.getEncryptedRollbackFile(), rollbackFn));

            FlashbackSampleCountBO originSqlFileCal = this.calculateSqlResult(originSqlFileDownloaded);
            String originSqlFileCalStr = JSON.toJSONString(originSqlFileCal);
            FlashbackSampleCountBO rollbackSqlFileCal = this.calculateSqlResult(rollbackSqlFileDownloaded);
            String rollbackSqlFileCalStr = JSON.toJSONString(rollbackSqlFileCal);
            this.flashbackTaskMapper.updateSqlSample(t.getId(), originSqlFileCalStr, rollbackSqlFileCalStr);
            log.info("flashbackRetrieve rollbackFileUrl path: {} url:{}", rollbackSqlFile.getAbsoluteFile(), rollbackFileUrlEncrypted);


        } catch (Exception e) {
            String msg = "flashbackRetrieve upload file originFileUrl: " + originSqlFile + "+ rollbackFileUrl: " + rollbackSqlFile + " error: " + e.getMessage();
            log.error(msg);
            bo.setMessage(msg);
            bo.setResult(false);
        }
        return bo;
    }



    /**
     * calculate rollback/source sql size, as we will only store some sqls in DB for frontend presentation
     * @param f
     */
    private FlashbackSampleCountBO calculateSqlResult(File f) {
        FlashbackSampleCountBO bo = new FlashbackSampleCountBO();
        if (!f.exists()) {
            return bo;
        }
        Integer n = sqlSampleCount;
        LinkedList<String> sqlLines = new LinkedList<>();
        try(BufferedReader bufferedReader = new BufferedReader(new FileReader(f))){
            String line;
            Long count = 0L;
//            65535 MYSQL TEXT 最大长度
            while ((line = bufferedReader.readLine()) != null ) {
                if (sqlLines.size() >= n){
                    sqlLines.removeFirst();
                }
                sqlLines.add(line);
                count++;
            }
//            reverse the sample list, as we need to present the sql to user by latest order
            Collections.reverse(sqlLines);
            bo.setSamples(sqlLines);
            bo.setSize(count);
            return bo;
        } catch (IOException e) {
            log.error("FlashbackSampleCountBO calculateSqlResult error: {}", e.getMessage());
            return new FlashbackSampleCountBO();
        }
    }

    private FlashbackTaskDTO transfer(FlashbackTask task) {

        FlashbackTaskDTO flashbackTaskDTO = new FlashbackTaskDTO();
        flashbackTaskDTO.setId(task.getId());
        flashbackTaskDTO.setTaskGroupId(task.getTaskGroupId());
        flashbackTaskDTO.setStorage(task.getStorage());
        flashbackTaskDTO.setOriginFile(task.getOriginFile());
        flashbackTaskDTO.setRollbackFile(task.getRollbackFile());
//        flashbackTaskDTO.setCreateLog(task.getCreateLog());
//        flashbackTaskDTO.setExecuteLog(task.getExecuteLog());
        flashbackTaskDTO.setCreatedAt(task.getCreatedAt());
        flashbackTaskDTO.setUpdatedAt(task.getUpdatedAt());
        flashbackTaskDTO.setIp(task.getIp());
        flashbackTaskDTO.setPort(task.getPort());
        flashbackTaskDTO.setDbname(task.getDbname());
        List<String> parsed = JSON.parseArray(task.getTableList(), String.class);
        flashbackTaskDTO.setTableList(parsed);
        flashbackTaskDTO.setCStatus(task.getCStatus());
        List<String> parsed1 = JSON.parseArray(task.getSqlType(), String.class);
        flashbackTaskDTO.setSqlType(parsed1);
        flashbackTaskDTO.setStartTime(task.getStartTime());
        flashbackTaskDTO.setEndTime(task.getEndTime());
        flashbackTaskDTO.setMatchSql(task.getMatchSql());
        flashbackTaskDTO.setCreator(task.getCreator());
        flashbackTaskDTO.setExecutor(task.getExecutor());
        flashbackTaskDTO.setExecuteTime(task.getExecuteTime());
        flashbackTaskDTO.setCStatus(task.getCStatus());
        flashbackTaskDTO.setExecuteHost(task.getExecuteHost());
        flashbackTaskDTO.setEStatus(task.getEStatus());
        return flashbackTaskDTO;
    }



    /**
     * batch execute command`yinglong create`
     * @param taskIdList
     * @return
     */
    public List<FlashbackRetrieveResultDTO> flashbackRetrieve(List<Long> taskIdList) {
        List<FlashbackTask> flashbackTaskList = flashbackTaskMapper.retrieveTaskByIds(taskIdList);
        List<FlashbackRetrieveResultDTO> resultDTOS = new ArrayList<>();
        for (FlashbackTask t : flashbackTaskList) {
            String mutex = t.getIp() + ":" + t.getPort();
            FlashbackRetrieveResultDTO dto = new FlashbackRetrieveResultDTO();
//            （1）task is already success
            if (t.getCStatus() == Status.SUCCESS.value || t.getCStatus() == Status.RUNNING.value) {
                dto.setResult(false);
                String m = "";
                if (t.getCStatus() == Status.SUCCESS.value ){
                    m = String.format("flashback for task id: %d db: %s:%s %s already success", t.getId(), t.getIp(), t.getPort(), t.getDbname());
                }else if (t.getCStatus() == Status.RUNNING.value){
                    m = String.format("flashback for task id: %d db: %s:%s %s is running", t.getId(), t.getIp(), t.getPort(), t.getDbname());
                }
                dto.setMessage(m);
                log.info("flashbackTasksCreate {} end {} mutex: {} {}", Thread.currentThread().getName(), t.getId(), t.getIp() + ":" + t.getPort(), m);
                resultDTOS.add(dto);
                continue;
            }
            this.mutexTaskManager.submitTask(mutex, () -> {
                flashbackTasksCreate(t);
            });
            dto.setResult(Boolean.TRUE);
            dto.setMessage("start yinglong create");
            dto.setTask(this.transfer(t));
            resultDTOS.add(dto);
        }
        return resultDTOS;

    }

    public FlashbackRetrieveResultDTO flashbackTasksCreate(FlashbackTask t) {
        log.info("flashbackTasksCreate {} start taskId:{} mutex: {}", Thread.currentThread().getName(), t.getId(), t.getIp() + ":" + t.getPort());
        FlashbackRetrieveResultDTO dto = new FlashbackRetrieveResultDTO();
        Instance instance = instanceMapper.selectOneByIpPort(t.getIp(), t.getPort());
        if (instance == null) {
            String m = String.format("flashback instance not found for %s:%s", t.getIp(), t.getPort());
            dto.setResult(false);
            dto.setMessage(m);
//            resultDTOS.add(dto);
            Integer affectedRows = this.flashbackTaskMapper.updateCreateStatusAndLog(t.getId(), Status.FAILED.value, m);
            log.warn(m);
            log.info("flashbackTasksCreate {} end taskId: {} mutex: {} instance == null", Thread.currentThread().getName(), t.getId(), t.getIp() + ":" + t.getPort());
            return dto;
        }
        String ip = t.getIp();
        String env = instance.getEnv();
        String password = mountainRoPassword;
        String user = mountainRoUser;
        String test = "test";
        if (StringUtils.equals(env, test)) {
            password = mountainRoHtjPassword;
        }
        List<String> specialHosts = new ArrayList<>();
        try {
            specialHosts = Arrays.asList(mountainDbRoSpecialProdPasswdHost.split(","));
        } catch (Exception e) {
            log.warn("flashback retrieve specialHost failed: {}", mountainDbRoSpecialProdPasswdHost);
        }
        if (specialHosts.contains(ip)) {
            password = mountainDbRoPassword;
        }
        FlashbackRetrieveResultBO bo = this.flashbackRetrieve(user, password, t);
        String executeLog = bo.getExecuteLog();
        Integer affectedRows = 0;
        Integer maxs = 6257;
        Integer ts = executeLog.length();
        if (ts > maxs){
            ts = maxs;
        }
        executeLog = executeLog.substring(0,ts) + "...";
        if (bo.getResult()) {
            // 闪回任务执行成功
            affectedRows = this.flashbackTaskMapper.updateStatusAndCreateLog(t.getId(), Status.SUCCESS.value, executeLog+"\n yinglong create success", bo.getOriginSqlFile(), bo.getRollbackSqlFile());
        } else {
            // 闪回任务执行失败
            affectedRows = this.flashbackTaskMapper.updateStatusAndCreateLog(t.getId(), Status.FAILED.value, executeLog+"\n yinglong create failed", bo.getOriginSqlFile(), bo.getRollbackSqlFile());
        }
        if (affectedRows == 0) {
            log.error("update flashback_task error affectedRows==0 id: {} cStatus: {}", t.getId(), t.getCStatus());
        }
        dto = this.transfer(bo);
        log.info("flashbackTasksCreate {} end {} mutex: {} success",Thread.currentThread().getName(),t.getId(), t.getIp() + ":" + t.getPort());
        return dto;
    }


    private FlashbackExecuteResultDTO flashbackTaskExecute(FlashbackTask t) {
        FlashbackExecuteResultDTO res = new FlashbackExecuteResultDTO();
        Instance instance = instanceMapper.selectOneByIpPort(t.getIp(), t.getPort());
        if (instance == null) {
            String m = String.format("flashback instance not found for %s:%s", t.getIp(), t.getPort());
            res.setResult(false);
            res.setMessage(m);
//            resultDTOS.add(dto);
//            Integer affectedRows = this.flashbackTaskMapper.updateExecuteStatusExecuteLog(t.getId(), Status.FAILED.value, m);
            log.warn(m);
            log.info("flashbackTasksCreate {} end taskId: {} mutex: {} instance == null", Thread.currentThread().getName(), t.getId(), t.getIp() + ":" + t.getPort());
            return res;
        }

        String ip = t.getIp();
        String env = instance.getEnv();
        String password = mountainRoPassword;
        String user = mountainRoUser;
        String test = "test";
        if (StringUtils.equals(env, test)) {
            password = mountainRoHtjPassword;
        }
        List<String> specialHosts = new ArrayList<>();
        try {
            specialHosts = Arrays.asList(mountainDbRoSpecialProdPasswdHost.split(","));
        } catch (Exception e) {
            log.warn("flashback execute specialHost failed: {}", mountainDbRoSpecialProdPasswdHost);
        }
        if (specialHosts.contains(ip)) {
            password = mountainDbRoPassword;
        }
        user = flashbackExecuteUser;
        password = flashbackExecutePassword;
        FlashbackExecuteResultBO bo = this.flashbackExecute(user, password, t);
        String executeLog = bo.getExecuteLog();
        Integer affectedRows = 0;
        Integer maxs = 70000;
        Integer ts = executeLog.length();
        if (ts > maxs){
            ts = maxs;
        }
        executeLog = executeLog.substring(0,ts) + "...";
        if (bo.getResult()) {
            // yinglong execute 执行成功
            this.flashbackTaskMapper.updateExecuteStatusAndLog(t.getId(), Status.SUCCESS.value, executeLog+"\n yinglong execute success");
        } else {
            // yinglong execute 执行失败
            this.flashbackTaskMapper.updateExecuteStatusAndLog(t.getId(), Status.FAILED.value, executeLog + "\n yinglong execute failed");
        }
        return null;
    }


    public List<FlashbackExecuteResultDTO> flashbackExecute(List<Long> taskIdList) {
        List<FlashbackTask> flashbackTaskList = flashbackTaskMapper.retrieveTaskByIds(taskIdList);

        List<FlashbackExecuteResultDTO> resultDTOS = new ArrayList<>();
        for (FlashbackTask t : flashbackTaskList) {
            FlashbackExecuteResultDTO res = new FlashbackExecuteResultDTO();
            String mutex = t.getIp() + ":" + t.getPort();
            FlashbackRetrieveResultDTO dto = new FlashbackRetrieveResultDTO();
            if (t.getCStatus() != Status.SUCCESS.value) {
                res.setMessage("flashbackTask status not in CREATE_SUCCESS");
                res.setResult(false);
                resultDTOS.add(res);
                continue;
            }
            if (t.getEStatus() == Status.SUCCESS.value || t.getEStatus() == Status.RUNNING.value) {
                res.setMessage("flashbackTask task in SUCCESS/RUNNING status");
                res.setResult(false);
               resultDTOS.add(res);
                continue;
            }
            try{
                FlashbackSampleCountBO originSampleStr = JSON.parseObject(t.getOriginFileSample(), FlashbackSampleCountBO.class);
                FlashbackSampleCountBO rollbackSampleStr = JSON.parseObject(t.getRollbackFileSample(), FlashbackSampleCountBO.class);
                if (originSampleStr.getSize() > this.sqlSampleCount || rollbackSampleStr.getSize() > this.sqlSampleCount){
                    String m = "flashbackTask sql is more than "+ this.sqlSampleCount +", please submit DML order!";
                    res.setMessage(m);
                    this.flashbackTaskMapper.updateExecuteStatusAndLog(t.getId(), Status.FAILED.value, m);
                    res.setResult(false);
                    resultDTOS.add(res);
                    continue;
                }
            }catch (Exception e){
                String m = "flashbackTask json deserializer originSampleStr/rollbackSampleStr failed, can not judge if sql sample number is less than "+ this.sqlSampleCount;
                res.setMessage(m);
                this.flashbackTaskMapper.updateExecuteStatusAndLog(t.getId(), Status.FAILED.value, m);
                res.setResult(false);
                resultDTOS.add(res);
                continue;
            }
            this.mutexTaskManager.submitTask(mutex, () -> {
                flashbackTaskExecute(t);
            });
            res.setResult(true);
            res.setMessage("start yinglong execute");
            res.setTask(this.transfer(t));
            resultDTOS.add(res);
        }
        return resultDTOS;
    }
    private FlashbackExecuteResultBO flashbackExecute(String username, String password, FlashbackTask t){
        FlashbackExecuteResultBO bo = new FlashbackExecuteResultBO();

        // 开始执行 yinglong execute
        String traceId = String.format("%s#%s:%s#%s.%s", t.getId(), t.getIp(), t.getPort(), t.getDbname(), t.getTableList().toString());
        String executeHost = t.getExecuteHost();
        FileDownloader downloader = null;
        String storage = t.getStorage();
        String fp = null;
        if (StringUtils.equals(storage, Storage.PANDA.value)) {
            downloader = new PandaFileResolver(pandaService);
            fp = t.getRollbackFile();
        } else if (StringUtils.equals(storage, Storage.AVI_PFS.value)) {
            downloader = aviService;
            try {
                String filename = this.generateFileName(ROLLBACK, t);
                fp = downloader.download(t.getEncryptedRollbackFile(), filename);
            } catch (Exception e) {
                bo.setResult(false);
                bo.setMessage("download failed: " + e.getMessage());
                return bo;
            }
        } else {
            bo.setResult(false);
            String m = String.format("taskId: %d storageType: %s not found!", t.getId(), storage);
            log.error(m);
            bo.setMessage(m);
            return bo;
        }
        String dir = "";
        // panda need this config
         if (StringUtils.equals(defaultCommandExecutor, Executor.PANDA.value)){
             dir = defaultCommandDir;
        }
         // /Users/<USER>/IdeaProjects/yinglong/yinglong execute
        //  --db-host=************ --db-port=3306
        //      --db-username=mountainhtj --db-password=mountainhtj
        //      --paraller=4
        //      --filepath="/data0/rollback_sqls//flashback_smzefsqgmivdoqce/rollback_************_3306_1_huatingdong_test_single_not_ma_qxq_test_qq_2025-04-13.sql"

        String commandline = dir+ "yinglong";
        String os = System.getenv("GOHOSTOS");
        // HINT: 该文件比较大，因此darwin的未存放到resoureces文件夹中
        if (StringUtils.equals(os, OS_DARWIN)) {
            commandline = commandline + "_" + OS_DARWIN;
        }
        commandline +=" execute";
        List<String> commandArgs = new ArrayList<>();
        commandArgs.add("--db-host="+t.getIp());
        commandArgs.add("--db-port="+t.getPort());
        commandArgs.add("--db-username="+ username);
        commandArgs.add("--db-password="+ password);
        commandArgs.add("--paraller="+ this.paraller);
        commandArgs.add("--single-transaction=true");

        commandArgs.add("--filepath="+ fp);
        this.flashbackTaskMapper.updateExecuteStatusAndLog(t.getId(), Status.RUNNING.value, "");
        ProcessResultBO<String> processResultBO = this.commandExecutor.process(traceId, executeHost, commandline, commandArgs);
        bo.setExecuteLog(processResultBO.getMsg());
        Boolean executeError = Boolean.FALSE;
        String executeLog = bo.getExecuteLog();
        if (StringUtils.isEmpty(executeLog) || executeLog.contains("No such file or directory") || executeLog.contains("执行回滚失败")){
            executeError = Boolean.TRUE;
        }
        if (processResultBO.getCode()!=0){
            bo.setResult(false);
            bo.setMessage("command execute failed");
            return bo;
        }
        if (executeError) {
            bo.setResult(false);
            bo.setMessage("command execute failed");
            return bo;
        }
        bo.setResult(true);
        bo.setMessage("execute success");
        return bo;
    }

    private FlashbackRetrieveResultDTO transfer(FlashbackRetrieveResultBO bo) {
        FlashbackRetrieveResultDTO dto = new FlashbackRetrieveResultDTO();
        dto.setTask(bo.getTask());
        dto.setResult(bo.getResult());
        dto.setMessage(bo.getMessage());
        dto.setOriginSqlFile(bo.getOriginSqlFile());
        dto.setRollbackSqlFile(bo.getRollbackSqlFile());
        return dto;
    }


    public DownloadFileResp download(DownloadFileReq req) {
        DownloadFileResp resp = new DownloadFileResp();
        resp.setResult(true);
        String CATE_ORIGIN_SQL = "origin";
        String CATE_ROLLBACK_SQL = "rollback";
        List<String> cates = new ArrayList<>();
        cates.add(CATE_ORIGIN_SQL);
        cates.add(CATE_ROLLBACK_SQL);

        try {
            Long id = req.getId();
            List<Long> ids = new ArrayList<>();
            ids.add(id);
            List<FlashbackTask> flashbackTasks = flashbackTaskMapper.retrieveTaskByIds(ids);
            if (flashbackTasks.isEmpty()) {
                resp.setResult(Boolean.FALSE);
                resp.setMsg("task id not exist: " + id);
                return resp;
            }
            FlashbackTask task = flashbackTasks.get(0);
            String storage = task.getStorage();
            String cate = req.getCate();
            if (!cates.contains(cate)) {
                resp.setMsg("FlashbackService download failed as cate not valid, should be in " + cates.toString());
                resp.setResult(false);
                return resp;
            }

            FileDownloader downloader = null;
            if (StringUtils.equals(storage, Storage.PANDA.value)) {
                downloader = new PandaFileResolver(pandaService);

            } else if (StringUtils.equals(storage, Storage.AVI_PFS.value)) {
                downloader = aviService;
            } else {
                resp.setResult(false);
                String m = String.format("taskId: %d storageType: %s not found!", req.getId(), storage);
                log.error(m);
                resp.setMsg(m);
                return resp;
            }
            try {
                if (StringUtils.equals(cate, CATE_ORIGIN_SQL)) {
                    String fn = this.generateFileName(ORIGIN, task);
                    String path = downloader.download(task.getEncryptedOriginFile(), fn);
                    resp.setPath(path);
                } else if (StringUtils.equals(cate, CATE_ROLLBACK_SQL)) {
                    String fn = this.generateFileName(ROLLBACK, task);
                    String path = downloader.download(task.getEncryptedRollbackFile(), fn);
                    path = downloader.resolveDownloadedFile(path);
                    resp.setPath(path);
                }
            } catch (Exception e) {
                String m = String.format("FlashbackService download failed: %s", e.getMessage());
                log.error(m);
                resp.setResult(false);
                resp.setMsg(m);
                return resp;
            }
            return resp;
        } catch (Exception e) {
            log.error("flashback service download error", e);
        }
        return null;
    }

    private FlashbackTask transfer(FlashbackTaskBO bo) {
        FlashbackTask flashbackTask = new FlashbackTask();
        flashbackTask.setIp(bo.getIp());
        flashbackTask.setPort(Integer.parseInt(bo.getPort()));
        flashbackTask.setDbname(bo.getDbname());
        String tbnames = JSON.toJSONString(bo.getTbname());
        flashbackTask.setTableList(tbnames);
        String sqlTypes = JSON.toJSONString(bo.getSqlType());
        flashbackTask.setSqlType(sqlTypes);
        flashbackTask.setMatchSql(bo.getMatchSql());
        flashbackTask.setCreator(bo.getCreator());
        flashbackTask.setStartTime(bo.getStartTime());
        flashbackTask.setEndTime(bo.getEndTime());
        String sessionId = bo.getSessionId();
        if (StringUtils.isEmpty(sessionId)){
            sessionId = "";
        }
        flashbackTask.setSessionId(sessionId);
        String executeHost = bo.getExecuteHost();
        if (StringUtils.isEmpty(executeHost)){
            executeHost = "";
        }
        flashbackTask.setExecuteHost(executeHost);
        flashbackTask.setInstanceName(bo.getInstanceName());
        return flashbackTask;
    }

    public FlashbackTaskSubmitResultDTO submitTask(List<FlashbackTaskBO> taskBOS) {
        FlashbackTaskSubmitResultDTO r = new FlashbackTaskSubmitResultDTO();
        if (taskBOS.isEmpty()){
            r.setMessage("taskList is empty");
            r.setResult(false);
            return r;
        }
        List<FlashbackTask> taskList = new ArrayList<>(taskBOS.size());
        String taskGroupId = TASK_FLASHBACK + "_"+ RandomUtil.generateRandomLowerCaseString(16);




        for (FlashbackTaskBO bo : taskBOS) {
            FlashbackTask flashbackTask = transfer(bo);
            flashbackTask.setTaskGroupId(taskGroupId);
            taskList.add(flashbackTask);
        }
        int affectedRows = flashbackTaskMapper.batchSubmit(taskList);

        List<FlashbackTask> tasksFromDB = flashbackTaskMapper.retrieveTaskByGroupId(taskGroupId);

        List<FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO> retDbIds =  new ArrayList<>();
        for (FlashbackTask taskInDB : tasksFromDB) {
            FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO dto = new FlashbackTaskSubmitResultDTO.FlashbackTaskSubmitDTO();
            dto.setId(taskInDB.getId());
            dto.setTaskGroupId(taskGroupId);
            String sqlType = taskInDB.getSqlType();
            List<String> sqlTypes = JSON.parseArray(sqlType, String.class);
            dto.setSqlType(sqlTypes);
            dto.setIp(taskInDB.getIp());
            dto.setPort(taskInDB.getPort());
            dto.setDbname(taskInDB.getDbname());
            String tableList = taskInDB.getTableList();
            List<String> parsed = JSON.parseArray(tableList, String.class);
            dto.setTbname(parsed);
            dto.setMatchSql(taskInDB.getMatchSql());
            dto.setSessionId(taskInDB.getSessionId());
            dto.setExecuteHost(taskInDB.getExecuteHost());
            retDbIds.add(dto);
        }
        r.setResult(Boolean.FALSE);
        if(affectedRows > 0){
            r.setTaskList(retDbIds);
            r.setTaskGroupId(taskGroupId);
            r.setResult(Boolean.TRUE);
        }else {
            r.setMessage("submit flashback task failed!");
        }
        return r;
    }

    public PageResult<FlashbackTaskGroupListResultDTO> retrieveFlashbackTaskList(String searchVal, Integer page, Integer pageSize) {
        List<FlashbackTaskGroup> flashbackTaskGroups = new ArrayList<>();
        try (Page<Object> ignored = PageHelper.startPage(page, pageSize)) {
            flashbackTaskGroups = flashbackTaskGroupMapper.listSimple(searchVal);
//            flashbackTaskList = this.flashbackTaskMapper.listSimple(searchVal);
        }
        List<String> taskGroupIds = new ArrayList<>();
        for (FlashbackTaskGroup taskGroup : flashbackTaskGroups) {
            taskGroupIds.add(taskGroup.getTaskGroupId());
        }
        List<FlashbackTask> flashbackTasks = this.flashbackTaskMapper.retrieveSimpleTaskByGroupIdList(taskGroupIds);
        Map<String, List<FlashbackTask>> tmpMap = new HashMap<>(16);
        for (FlashbackTask flashbackTask : flashbackTasks) {
            String taskGroupId = flashbackTask.getTaskGroupId();
            tmpMap.computeIfAbsent(taskGroupId, k -> new ArrayList<>());
            tmpMap.get(taskGroupId).add(flashbackTask);
        }
        List<FlashbackTaskGroupListResultDTO> result = new ArrayList<>();
        for (FlashbackTaskGroup flashbackTaskGroup : flashbackTaskGroups) {
            String taskGroupId = flashbackTaskGroup.getTaskGroupId();
            List<FlashbackTask> taskList = tmpMap.get(taskGroupId);
            List<FlashbackTaskListResultDTO> r = new ArrayList<>();
            String cate = flashbackTaskGroup.getCate();
            FlashbackTaskGroupListResultDTO resultDTO = new FlashbackTaskGroupListResultDTO();

            BeanUtils.copyProperties(flashbackTaskGroup, resultDTO);
            if (StringUtils.equals(cate, TYPE_INSTANCE)) {
                FlashbackTask task0 = taskList.get(0);
                BeanUtils.copyProperties(task0, resultDTO);
                List<String> parsed0 = JSON.parseArray(task0.getSqlType(), String.class);
                resultDTO.setSqlType(parsed0);
                List<String> parsed = JSON.parseArray(flashbackTaskGroup.getTableList(), String.class);
                resultDTO.setTableList(parsed);
                List<String> parsedDbnames = JSON.parseArray(flashbackTaskGroup.getDbname(), String.class);
                resultDTO.setDbname(String.join(",",parsedDbnames));
            } else if (StringUtils.equals(cate, TYPE_LOGIC)) {
                if(!taskList.isEmpty()){
                    FlashbackTask task0 = taskList.get(0);
                    BeanUtils.copyProperties(task0, resultDTO);
                    List<String> parsed0 = JSON.parseArray(task0.getSqlType(), String.class);
                    resultDTO.setSqlType(parsed0);
                    resultDTO.setTaskList(r);
                }
                resultDTO.setCStatus(-1);
                resultDTO.setInstanceName("");
                List<String> parsed = JSON.parseArray(flashbackTaskGroup.getTableList(), String.class);
                resultDTO.setTableList(parsed);

                List<String> parsed3 = JSON.parseArray(flashbackTaskGroup.getDbname(), String.class);
                resultDTO.setDbname(String.join(",", parsed3));

                for (FlashbackTask task : taskList) {
                    FlashbackTaskListResultDTO ftlrd = new FlashbackTaskListResultDTO(task);
                    List<String> parsed2 = JSON.parseArray(task.getTableList(), String.class);
                    ftlrd.setTableList(parsed2);
                    List<String> parsed1 = JSON.parseArray(task.getSqlType(), String.class);
                    ftlrd.setSqlType(parsed1);
                    r.add(ftlrd);
                }
                List<FlashbackTaskListResultDTO> instancetaskList = this.extractInstanceTaskList(r);
                resultDTO.setInstanceTaskList(instancetaskList);
            }
            resultDTO.setCate(flashbackTaskGroup.getCate());

//        "port": null,
//        "sql_type": null,
//        "start_time": null,
//        "end_time": null,
//        "session_id": null,
//         "match_sql": null,
            result.add(resultDTO);
        }
        return PageResult.of(result, new PageInfo<>(flashbackTaskGroups));
    }

    /**
     * group task list by instance level
     * @param taskList
     * @return
     */
    private List<FlashbackTaskListResultDTO> extractInstanceTaskList(List<FlashbackTaskListResultDTO> taskList) {
        List<FlashbackTaskListResultDTO> taskListResultDTOS = new ArrayList<>();
        Map<String, List<FlashbackTaskListResultDTO>> m  = new HashMap<>(16);
        for (FlashbackTaskListResultDTO resultDTO : taskList) {
            String k = resultDTO.getIp() +"::"+ resultDTO.getPort();
            m.putIfAbsent(k, new ArrayList<>());
            m.get(k).add(resultDTO);
        }
        for (Map.Entry<String, List<FlashbackTaskListResultDTO>> entry : m.entrySet()) {
            FlashbackTaskListResultDTO dto = new FlashbackTaskListResultDTO();
            // must not be empty
            FlashbackTaskListResultDTO task0 = entry.getValue().get(0);
            dto.setTaskGroupId(task0.getTaskGroupId());
            dto.setIp(task0.getIp());
            dto.setPort(task0.getPort());
            dto.setInstanceName(task0.getInstanceName());
            dto.setCate(task0.getCate());
            dto.setExecuteHost(task0.getExecuteHost());
            dto.setSqlType(task0.getSqlType());
            dto.setMatchSql(task0.getMatchSql());
            dto.setCreator(task0.getCreator());
            dto.setExecutor(task0.getExecutor());
            List<FlashbackTaskListResultDTO> valueTaskList = entry.getValue();
            Set<String> dbnames = new HashSet<>();
            Set<String> tableList = new HashSet<>();
            //1. 有失败的直接显示失败 2
            //2. 全部成功才显示成功 1
            //3. 全部未开始显示未开始 0
            //4. 其余显示进行中  4
            Map<Integer,Integer> statusCountMap = new HashMap<>();
            for (FlashbackTaskListResultDTO t : valueTaskList) {
                statusCountMap.putIfAbsent(t.getCStatus(), 0);
                statusCountMap.put(t.getCStatus(),  statusCountMap.get(t.getCStatus()) + 1);
                dbnames.add(t.getDbname());
                for (String tablename : t.getTableList()) {
                    tableList.add(t.getDbname() + "." + tablename);
                }
            }
            dto.setDbname(String.join(",", dbnames));
            dto.setTableList(new ArrayList<>(tableList));

            Integer countFailed = statusCountMap.get(Status.FAILED.value);
            Integer countSuccess = statusCountMap.get(Status.SUCCESS.value);
            Integer countNone = statusCountMap.get(Status.NONE.value);
            if (countFailed!=null && countFailed> 0){
                dto.setCStatus(Status.FAILED.value);
            }else if (countSuccess!=null && countSuccess == valueTaskList.size()){
                dto.setCStatus(Status.SUCCESS.value);
            }else if (countNone!=null && countNone == valueTaskList.size()){
                dto.setCStatus(Status.NONE.value);
            }else {
                dto.setCStatus(Status.RUNNING.value);
            }
            dto.setExecuteHost(task0.getExecuteHost());
            taskListResultDTOS.add(dto);
        }
        return taskListResultDTOS;
    }


    public List<FlashbackTaskDTO> retrieveFlashbackTaskByIds(List<Long> ids) {
        List<FlashbackTask> flashbackTasks = this.flashbackTaskMapper.retrieveTaskByIds(ids);

        List<FlashbackTaskDTO> flashbackTaskDTOS = new ArrayList<>();
        for (FlashbackTask task : flashbackTasks) {
            FlashbackTaskDTO flashbackTaskDTO = transfer(task);
            flashbackTaskDTO.setCreateLog(task.getCreateLog());
            flashbackTaskDTO.setExecuteLog(task.getExecuteLog());
            try {
                FlashbackSampleCountBO originSampleStr = JSON.parseObject(task.getOriginFileSample(), FlashbackSampleCountBO.class);
                flashbackTaskDTO.setOriginFileSample(originSampleStr);
            } catch (Exception e) {
                log.error("FlashbackService.retrieveFlashbackTaskByIds transfer originSampleStr jsonStr err {}", e.getMessage());
            }
            try {
                FlashbackSampleCountBO originSampleStr = JSON.parseObject(task.getRollbackFileSample(), FlashbackSampleCountBO.class);
                flashbackTaskDTO.setRollbackFileSample(originSampleStr);
            } catch (Exception e) {
                log.error("FlashbackService.retrieveFlashbackTaskByIds transfer originSampleStr jsonStr err {}", e.getMessage());
            }
            String createLog = flashbackTaskDTO.getCreateLog();
            if(StringUtils.isEmpty(createLog)){
                flashbackTaskDTO.setCreateLog("");
            }
            flashbackTaskDTOS.add(flashbackTaskDTO);
        }

        // 按照单行大小来过滤出 平均行数大小小雨 sqlSampleSize 的 才返回给前端
        for (FlashbackTaskDTO flashbackTaskDTO : flashbackTaskDTOS) {
            FlashbackSampleCountBO originFileSample = flashbackTaskDTO.getOriginFileSample();
            FlashbackSampleCountBO rollbackFileSample = flashbackTaskDTO.getRollbackFileSample();
            Integer avgOriginSqlSamples = calAverageSqlSampleSize(originFileSample);
            Integer avgRollbackSqlSamples = calAverageSqlSampleSize(rollbackFileSample);
            if (avgOriginSqlSamples > sqlSampleSize || avgRollbackSqlSamples > sqlSampleSize){
                flashbackTaskDTO.getOriginFileSample().setSamples(new ArrayList<>());
                flashbackTaskDTO.getRollbackFileSample().setSamples(new ArrayList<>());
            }
        }
        return flashbackTaskDTOS;
    }

    private Integer calAverageSqlSampleSize(FlashbackSampleCountBO sampleCountBO){
        if(sampleCountBO==null || sampleCountBO.getSize()==0){
            return 0;
        }
        int s = 0;
        for (String sample : sampleCountBO.getSamples()) {
            s+=sample.length();
        }
        return s/sampleCountBO.getSamples().size();
    }
}
