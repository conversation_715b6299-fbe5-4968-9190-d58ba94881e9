/**
 * <AUTHOR>
 * @date 2025/5/20
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pinduoduo.mountain.common.thirdparty.xcloud.XcloudProxyService;
import com.pinduoduo.mountain.integration.rollback.IRollbackService;
import com.pinduoduo.mountain.integration.rollback.bo.*;
import com.pinduoduo.mountain.service.emergencycenter.bo.DdcRecoveryToSourceInstanceModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class BCloudRollbackServiceImpl implements IRollbackService {

    private final String cloud = "BCloud";
    private final XcloudProxyService xcloudProxyService;

    private final String RESTORE_MODE_DATABASE = "database";
    private final String RESTORE_MODE_TABLE = "table";

    public BCloudRollbackServiceImpl(XcloudProxyService xcloudProxyService) {
        this.xcloudProxyService = xcloudProxyService;
    }

    /**
     * <a href="https://apis.baidu.com/doc/#/doc?productId=GWSE-vMgzmfwvsqU&stage=edit">获取可回档区间范围</>
     * @param instanceIdList
     * @return
     */
    @Override
    public List<RollbackRangeTime> describeRollbackRangeTime(List<RollbackInstanceInfo> instanceIdList) {
        List<RollbackRangeTime> result = new ArrayList<>();
        for (RollbackInstanceInfo rollbackInstanceInfo : instanceIdList) {
            RollbackRangeTime rangeTime = new RollbackRangeTime();
            rangeTime.setInstanceId(rollbackInstanceInfo.getInstanceId());
            try{
                JSONObject requestParams = new JSONObject();
                requestParams.put("Method", "GET");
                requestParams.put("uri", String.format("/v1/ddc/instance/%s/database/recoverableDateTimes", rollbackInstanceInfo.getInstanceId()));
                JSONObject resp = xcloudProxyService.withParams(cloud, "V1","ddc", rollbackInstanceInfo.getRegion()).request(requestParams);
                JSONArray times = resp.getJSONArray("recoverableDateTimes");
                rangeTime.setRangeTimeList(new ArrayList<>());
                rangeTime.setResult(Boolean.TRUE);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
                DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                ZoneId localZone = ZoneId.systemDefault();
               for (int i=0;i<times.size();i++){
                   JSONObject ji = times.getJSONObject(i);
                   // 2024-08-08T19:29:17Z ~ 2025-06-10T09:00:33Z, 注意这是UTC时间，需要转换为本地时间
                   String startDateTime = ji.getString("startDateTime");
                   LocalDateTime utcStartTime = LocalDateTime.parse(startDateTime, formatter);
                   String endDateTime = ji.getString("endDateTime");
                   LocalDateTime utcEndTime = LocalDateTime.parse(endDateTime, formatter);

                   LocalDateTime zonedStartTime = utcStartTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(localZone).toLocalDateTime();
                   String startTime = zonedStartTime.format(targetFormatter);

                   LocalDateTime zonedEndTime = utcEndTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(localZone).toLocalDateTime();
                   String endTime = zonedEndTime.format(targetFormatter);
                   rangeTime.getRangeTimeList().add(new RollbackRangeTime.RangeTime(startTime, endTime));
               }
               rangeTime.setMessage("success");
            } catch (Exception e) {
                log.error("DdcRollbackServiceImpl.describeRollbackRangeTime error: "+ e.getMessage());
                rangeTime.setResult(Boolean.FALSE);
                rangeTime.setMessage(e.getMessage());
            }
            result.add(rangeTime);
        }
        return result;
    }

    /*
    <a href="https://apis.baidu.com/doc/#/doc?productId=GWSE-vMgzmfwvsqU&stage=edit">百度云任务列表接口文档</a>
    https://github.com/baidubce/bce-sdk-go/blob/master/doc/RDS.md
    Example： {"result":[{"taskId":"3646251","region":"su","startTime":"2025-06-03 20:09:56","endTime":"0000-00-00 00:00:00","taskName":"数据库回档","instanceId":"ddc-me3lpomk","instanceName":"sbd2_hutaojie_multisource_switch","taskStatus":"running","taskAction":"","supportedOperate":[],"progress":null},{"taskId":"3646248","region":"su","startTime":"2025-06-03 20:08:26","endTime":"2025-06-03 20:09:17","taskName":"数据库回档","instanceId":"ddc-me3lpomk","instanceName":"sbd2_hutaojie_multisource_switch","taskStatus":"failed","taskAction":"","supportedOperate":[],"progress":null}],"marker":"-1","isTruncated":false,"nextMarker":null,"maxKeys":1000}
     */
    @Override
    public List<RollbackTaskDetail> describeRollbackTaskDetail(List<RollbackInstanceInfo> instanceIdList) {

        Map<String, List<RollbackInstanceInfo>> stringListMap = new HashMap<>(16);
        for (RollbackInstanceInfo rollbackInstanceInfo : instanceIdList) {
            stringListMap.computeIfAbsent(rollbackInstanceInfo.getRegion(), k -> new ArrayList<>());
            stringListMap.get(rollbackInstanceInfo.getRegion()).add(rollbackInstanceInfo);
        }

        List<RollbackTaskDetail> rollbackTaskDetails = new ArrayList<>();

        stringListMap.forEach((k, v) -> {
            try {
                JSONObject requestParams = new JSONObject();
                requestParams.put("Method", "GET");
                requestParams.put("uri", "/v1/ddc/task");
                List<String> instanceIds = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();
                // 最多查询获取过去10年的回档历史记录
                LocalDateTime startLocalDateTime = now.minusYears(3);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String endTime = now.format(formatter);
                String startTime = startLocalDateTime.format(formatter);
                for (RollbackInstanceInfo rollbackInstanceInfo : v) {
                    instanceIds.add(rollbackInstanceInfo.getInstanceId());
                }
                requestParams.put("instanceIds", String.join(",", instanceIds));
                requestParams.put("taskName", "restore");
                requestParams.put("startTime", startTime);
                requestParams.put("endTime", endTime);
                JSONObject resp = xcloudProxyService.withParams(cloud, "V1", "ddc", k).request(requestParams);
                JSONArray result = resp.getJSONArray("result");
                for (int i = 0; i < result.size(); i++) {
                    JSONObject resultJSONObject = result.getJSONObject(i);
                    String taskId = resultJSONObject.getString("taskId");
                    String region = resultJSONObject.getString("region");
                    String st = resultJSONObject.getString("startTime");
                    String et = resultJSONObject.getString("endTime");
                    String taskName = resultJSONObject.getString("taskName");
                    String instanceId = resultJSONObject.getString("instanceId");
                    String instanceName = resultJSONObject.getString("instanceName");
                    String taskStatus = resultJSONObject.getString("taskStatus");
                    String taskAction = resultJSONObject.getString("taskAction");
//                    JSONObject progress = resultJSONObject.getJSONObject("progress");
//                    if (progress!=null){
//                        Integer success = progress.getInteger("success");
//                        Integer total = progress.getInteger("total");
//                    }
//                    Integer success = progress.getInteger("success");
//                    Integer total = progress.getInteger("total");
                    //                任务当前状态（running：任务正在运行 failed：任务已失败 success：任务执行成功
                    //                now_executing：立即执行任务 cancelling：正在取消任务 cancelled：任务已取消）
                    taskStatus = this.transferTaskStatus(taskStatus);
                    Integer progress = 0;
                    if (StringUtils.equals(taskStatus, "SUCCESS")){
                        progress = 100;
                    }
//                腾讯云状态： 任务执行结果。可能的取值：INITIAL - 初始化，RUNNING - 运行中，SUCCESS - 执行成功，
//                FAILED - 执行失败，KILLED - 已终止，REMOVED - 已删除，PAUSED - 终止中。
                    RollbackTaskDetail taskDetail = new RollbackTaskDetail(Boolean.TRUE,
                            "get ddc rollbackTaskDetail success", st, et, taskStatus,
                            progress, new ArrayList<>(), instanceId);
                    rollbackTaskDetails.add(taskDetail);
                }
            } catch (Exception e) {
                rollbackTaskDetails.add(new RollbackTaskDetail(Boolean.FALSE, "BCloudRollbackServiceImpl describeRollbackTaskDetail error for : "+ v.toString() +" errorMsg: "+e.getMessage(),
                        "","","",-1,new ArrayList<>(), v.toString()));
            }
        });

        return rollbackTaskDetails;
    }

    /**
     * 以腾讯云状体为标准，其余任务的状态均需要转换为百度云
     * @param taskStatus
     * @return
     */
    private String transferTaskStatus(String taskStatus) {
                        // 任务当前状态（running：任务正在运行 failed：任务已失败 success：任务执行成功
                //                now_executing：立即执行任务 cancelling：正在取消任务 cancelled：任务已取消）
//                腾讯云状态： 任务执行结果。可能的取值：INITIAL - 初始化，RUNNING - 运行中，SUCCESS - 执行成功，
//                FAILED - 执行失败，KILLED - 已终止，REMOVED - 已删除，PAUSED - 终止中
        if (StringUtils.equals(taskStatus, "failed")) {
            return "FAILED";
        }
        if (StringUtils.equals(taskStatus, "running")) {
            return  "RUNNING";
        }
        if (StringUtils.equals(taskStatus, "success")) {
            return  "SUCCESS";
        }
        if (StringUtils.equals(taskStatus, "now_executing")) {
            return "RUNNING";
        }
        if(StringUtils.equals(taskStatus, "cancelling")){
            return "PAUSED";
        }
        if (StringUtils.equals(taskStatus, "cancelled")){
            return "KILLED";
        }
        return taskStatus;
    }

    /**
     *
     * <a href="https://cloud.baidu.com/doc/RDS/s/5kzcgep6e">百度云回档</>
     *
     * <a href="https://apis.baidu.com/doc/#/doc?productId=GWSE-vMgzmfwvsqU&stage=edit">回档API</>
     *
     * PUT /v{version}/instance/{instanceId}/recoveryToSourceInstanceByDatetime
     * HOST: rds.bj.baidubce.com
     * Authorization:authorization string
     * {
     *   "sourceInstanceId":"sourceInstanceId",
     *   "targetInstanceId":"targetInstanceId",
     *   "datetime": "datetime",
     *   "data": [ {
     *                "dbName": "dbName",
     *                "newDbname": "newDbname",
     *                "restoreMode": "restoreMode"
     *             },
     *             {
     *                "dbname": "dbname",
     *                "newDbname": "newDbname",
     *                "restoreMode": "restoreMode",
     *                "tables": [
     *                    {
     *                      "tableName": "tableName",
     *                      "newTablename": "newTablename"
     *                    }
     *                ]
     *              } ]
     *  }
     *
     * @param rollbackReqList
     * @return
     */
    @Override
    public List<RollbackResp> batchRollback(List<RollbackBO> rollbackReqList) {
        ArrayList<RollbackResp> rollbackResps = new ArrayList<>();
        for (RollbackBO rollbackReq : rollbackReqList) {
            try {
                // 待回档的数据库列表
//                List<RollbackTaskDetail.RollbackInfo> rollbackReqDatabases = rollbackReq.getDatabases();
                List<RollbackTaskDetail.RollbackTable> rollbackReqTables = rollbackReq.getData();
                List<DdcRecoveryToSourceInstanceModel> data = new ArrayList<>();
                for (RollbackTaskDetail.RollbackTable rollbackReqTable : rollbackReqTables) {
                    List<DdcRecoveryToSourceInstanceModel.DdcRecoveryToSourceInstanceTableModel> toSourceInstanceTableModels = new ArrayList<>();
                    if (rollbackReqTable.getTables() == null || CollectionUtils.isEmpty(rollbackReqTable.getTables())) {
                        String targetName = this.getTargetRollbackName(rollbackReqTable.getDatabase());
                        data.add(new DdcRecoveryToSourceInstanceModel(rollbackReqTable.getDatabase().getSource(),
                                targetName, RESTORE_MODE_DATABASE, null));
                    } else {
                        List<RollbackTaskDetail.RollbackInfo> tables = rollbackReqTable.getTables();
                        for (RollbackTaskDetail.RollbackInfo table : tables) {
                            String targetName = this.getTargetRollbackName(table);
                            toSourceInstanceTableModels.add(new DdcRecoveryToSourceInstanceModel.DdcRecoveryToSourceInstanceTableModel(table.getSource(), targetName));
                        }
                        // 表级别回档，将目标表回档到同一个数据库下
                        data.add(new DdcRecoveryToSourceInstanceModel(rollbackReqTable.getDatabase().getSource(),
                                rollbackReqTable.getDatabase().getSource(),
                                RESTORE_MODE_TABLE, toSourceInstanceTableModels));
                    }

                }

                JSONObject requestParams = new JSONObject();
                requestParams.put("Method", "PUT");
//                /v{version}/ddc/instance/{instanceId}/recoveryToSourceInstanceByDatetime
                requestParams.put("uri", String.format("/v1/ddc/instance/%s/recoveryToSourceInstanceByDatetime", rollbackReq.getInstanceId()));
                // return_data[{"requestId":"","code":"InternalError","message":"parsing time \"2025-06-08 14:59:10\" as
                // \"2006-01-02T15:04:05Z07:00\": cannot parse \" 14:59:10\" as \"T\""}
                DateTimeFormatter originalFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime originDateTime = LocalDateTime.parse(rollbackReq.getRollbackTime(), originalFormat);
                ZonedDateTime zonedDateTime = originDateTime.atZone(ZoneId.systemDefault());
                ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
                String utcString = utcDateTime.format(formatter);
                requestParams.put("datetime", utcString);
                requestParams.put("data", data);

                JSONObject res = xcloudProxyService.withParams(cloud, "V1", "ddc", rollbackReq.getRegion())
                        .request(requestParams);
                if (res==null){
                    rollbackResps.add(new RollbackResp(Boolean.FALSE, "ddc recoveryToSourceInstanceByDatetime return null: ",cloud, rollbackReq.getInstanceId(), "", ""));
                    continue;
                }
                String taskId = res.getString("taskId");
                Integer errorCode = res.getInteger("error_code");
                if (taskId == null) {
                    String errorMsg = res.getString("error_msg");
                    rollbackResps.add(new RollbackResp(Boolean.FALSE, "ddc recoveryToSourceInstanceByDatetime errorCode: " + errorCode + " errorMsg:" + errorMsg, cloud, rollbackReq.getInstanceId(), "", ""));
                    continue;
                }
                rollbackResps.add(new RollbackResp(Boolean.TRUE, "ddc recoveryToSourceInstanceByDatetime success", cloud, rollbackReq.getInstanceId(), "", taskId));
            }catch (Exception e){
                log.error("DdcRollbackServiceImpl batchRollback error: ", e);
                rollbackResps.add(new RollbackResp(Boolean.FALSE, "ddc recoveryToSourceInstanceByDatetime error: "+ e.getMessage(), cloud, rollbackReq.getInstanceId(), "", ""));
            }
        }
        return rollbackResps;
    }

    @Override
    public List<StopRollbackResp> stopRollback(List<RollbackInstanceInfo> strings) {
        List<StopRollbackResp> result = new ArrayList<>();
        for (int i = 0; i < strings.size(); i++) {
            result.add(new StopRollbackResp(Boolean.FALSE, "stopRollback not implemented", cloud, "", strings.get(i).getInstanceId(),""));
        }
        return result;
    }

    @Override
    public String getTargetRollbackName(RollbackTaskDetail.RollbackInfo rollbackInfo) {
         if (StringUtils.isNotEmpty(rollbackInfo.getTarget())){
            return rollbackInfo.getTarget();
        }
        //        表的命名我们要不给个默认值，如
        //源库为 "ies"，回档后库 "LEFT(ies,10)_rolback_${timestamp}"
        //源表位 "es_index"，回档后表位 "LEFT(es_index, 10)_rollback_${timestamp}"
        String source = rollbackInfo.getSource();
        long millis = System.currentTimeMillis();
        return  source.substring(0, 3) +"_rollback_"+ millis;
    }
}
