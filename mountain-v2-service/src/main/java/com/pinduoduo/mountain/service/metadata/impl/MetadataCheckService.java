package com.pinduoduo.mountain.service.metadata.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.ThreadPoolMetric;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pinduoduo.mountain.common.constant.Time;
import com.pinduoduo.mountain.common.thirdparty.inform.InformService;
import com.pinduoduo.mountain.common.util.CollectionUtil;
import com.pinduoduo.mountain.common.util.SqlUtil;
import com.pinduoduo.mountain.common.util.StringExtractUtil;
import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Columns;
import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Tables;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.*;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.*;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.HeboCluster;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.HeboClusterKeychain;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.HeboClusterResourceGroupOfMF;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.InstanceDbSyncInfo;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.mapper.HeboClusterKeychainMapper;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.mapper.HeboClusterMapper;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.mapper.HeboClusterResourceGroupMapperOfMF;
import com.pinduoduo.mountain.repository.mysql.pddmountain.entity.*;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.CdbFieldMapper;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.CoreCdbEncryptionMapper;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.CoreCdbShardMapper;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.PddInstanceMapper;
import com.pinduoduo.mountain.repository.util.InformationSchemaUtil;
import com.pinduoduo.mountain.service.metadata.bo.PhysicalTableInfo;
import com.pinduoduo.mountain.service.metadata.dto.LdLtLfInfoDTO;
import com.pinduoduo.mountain.service.metadata.request.CoreCdbShardCheckReq;
import com.pinduoduo.mountain.service.metadata.request.HeboClusterCheckReq;
import com.pinduoduo.mountain.service.metadata.request.KeychainLogicDbMatchReq;
import com.pinduoduo.mountain.service.metadata.request.LogicMetadataCheckReq;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MetadataCheckService {

    private final LogicDatabaseMapper logicDatabaseMapper;
    private final ClusterMapper clusterMapper;
    private final PhysicalClusterMapper physicalClusterMapper;
    private final PhyClusterInstanceRelationMapper phyClusterInstanceRelationMapper;
    private final InstanceMapper instanceMapper;
    private final CoreCdbShardMapper coreCdbShardMapper;
    private final HeboClusterMapper heboClusterMapper;
    private final OrderDdlSubMapper orderDdlSubMapper;
    private final PddInstanceMapper pddInstanceMapper;

    private static final ExecutorService META_DATA_CHECK_THREAD_POOL = new ThreadPoolExecutor(
            32, 32,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024), new ThreadPoolExecutor.CallerRunsPolicy());

    static {
        ThreadPoolMetric.getInstance().addThreadPool("mountain-v2-task-metadata-check", (ThreadPoolExecutor) META_DATA_CHECK_THREAD_POOL);
    }

    private final MetadataInspectResultMapper metadataInspectResultMapper;
    private final LogicTableMapper logicTableMapper;
    private final LogicFieldMapper logicFieldMapper;
    private final InstanceDbInfoMapper instanceDbInfoMapper;
    private final InstanceTableInfoMapper instanceTableInfoMapper;
    private final HeboClusterResourceGroupMapperOfMF heboClusterResourceGroupMapperOfMF;
    private final InformService informService;
    private final PddLogicMetadataService pddLogicMetadataService;
    private final CdbFieldMapper cdbFieldMapper;
    private final CoreCdbEncryptionMapper coreCdbEncryptionMapper;
    private final LogicTableService logicTableService;
    private final LogicTableVersionsMapper logicTableVersionsMapper;
    private final LogicFieldService logicFieldService;
    private final KeychainMapper keychainMapper;
    private final HeboClusterKeychainMapper heboClusterKeychainMapper;

    public MetadataCheckService(@Qualifier("logicDatabaseMapper") LogicDatabaseMapper logicDatabaseMapper, @Qualifier("clusterMapper") ClusterMapper clusterMapper, @Qualifier("physicalClusterMapper") PhysicalClusterMapper physicalClusterMapper, @Qualifier("phyClusterInstanceRelationMapper") PhyClusterInstanceRelationMapper phyClusterInstanceRelationMapper, @Qualifier("instanceMapper") InstanceMapper instanceMapper,
                                CoreCdbShardMapper coreCdbShardMapper, @Qualifier("heboClusterMapper") HeboClusterMapper heboClusterMapper, @Qualifier("orderDdlSubMapper") OrderDdlSubMapper orderDdlSubMapper,
                                @Qualifier("pddInstanceMapper") PddInstanceMapper pddInstanceMapper, @Qualifier("metadataInspectResultMapper") MetadataInspectResultMapper metadataInspectResultMapper, @Qualifier("logicTableMapper") LogicTableMapper logicTableMapper, @Qualifier("logicFieldMapper") LogicFieldMapper logicFieldMapper, InstanceDbInfoMapper instanceDbInfoMapper, @Qualifier("instanceTableInfoMapper") InstanceTableInfoMapper instanceTableInfoMapper, @Qualifier("heboClusterResourceGroupMapperOfMF") HeboClusterResourceGroupMapperOfMF heboClusterResourceGroupMapperOfMF, InformService informService, PddLogicMetadataService pddLogicMetadataService, @Qualifier("cdbFieldMapper") CdbFieldMapper cdbFieldMapper, @Qualifier("coreCdbEncryptionMapper") CoreCdbEncryptionMapper coreCdbEncryptionMapper, LogicTableService logicTableService, @Qualifier("logicTableVersionsMapper") LogicTableVersionsMapper logicTableVersionsMapper, LogicFieldService logicFieldService, @Qualifier("keychainMapper") KeychainMapper keychainMapper, HeboClusterKeychainMapper heboClusterKeychainMapper) {
        this.logicDatabaseMapper = logicDatabaseMapper;
        this.clusterMapper = clusterMapper;
        this.physicalClusterMapper = physicalClusterMapper;
        this.phyClusterInstanceRelationMapper = phyClusterInstanceRelationMapper;
        this.instanceMapper = instanceMapper;
        this.coreCdbShardMapper = coreCdbShardMapper;
        this.heboClusterMapper = heboClusterMapper;
        this.orderDdlSubMapper = orderDdlSubMapper;
        this.pddInstanceMapper = pddInstanceMapper;
        this.metadataInspectResultMapper = metadataInspectResultMapper;
        this.logicTableMapper = logicTableMapper;
        this.logicFieldMapper = logicFieldMapper;
        this.instanceDbInfoMapper = instanceDbInfoMapper;
        this.instanceTableInfoMapper = instanceTableInfoMapper;
        this.heboClusterResourceGroupMapperOfMF = heboClusterResourceGroupMapperOfMF;
        this.informService = informService;
        this.pddLogicMetadataService = pddLogicMetadataService;
        this.cdbFieldMapper = cdbFieldMapper;
        this.coreCdbEncryptionMapper = coreCdbEncryptionMapper;
        this.logicTableService = logicTableService;
        this.logicTableVersionsMapper = logicTableVersionsMapper;
        this.logicFieldService = logicFieldService;
        this.keychainMapper = keychainMapper;
        this.heboClusterKeychainMapper = heboClusterKeychainMapper;
    }

    /**
     * ddl 执行前，再做一次主要元数据的校验，校验实例地址，以及源端目标端是否和 v1 一致
     */
    public Pair<Boolean, String> metadataCheckBeforeDdl(OrderSql orderSql) {
        try {
            if ((orderSql.getMultiLogicDatabaseOrder() != null && orderSql.getMultiLogicDatabaseOrder().split(",").length > 1) ||
                    (orderSql.getMultiPhysicalClusterOrder() != null && orderSql.getMultiPhysicalClusterOrder().split(",").length > 1)) {
                return Pair.of(true, "");
            }

            // 【v2 的元数据】
            LogicDatabase logicDatabase = logicDatabaseMapper.selectOneByLogicDbId(orderSql.getLogicDbId());
            Cluster cluster = clusterMapper.selectOneByClusterId(orderSql.getClusterId());
            List<PhysicalCluster> physicalClusterList = physicalClusterMapper.selectByClusterId(cluster.getClusterId());
            List<String> phyClusterIds = physicalClusterList.stream()
                    .map(PhysicalCluster::getPhysicalClusterId)
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            List<String> instanceIdListV2 = phyClusterInstanceRelationMapper.selectInstanceIdListByPhyClusterIdsInSql(String.join(",", phyClusterIds));
            List<Instance> instanceListV2 = instanceMapper.selectByInstanceIdsInSql(String.format("'%s'", String.join("', '", instanceIdListV2)));
            List<OrderDdlSub> orderDdlSubList = orderDdlSubMapper.selectByWorkId(orderSql.getWorkId());


            // 按照实例分组
            Map<String, List<OrderDdlSub>> instanceToOrderDdlSubMap = null;
            // 1、检查生成的执行计划
            // 1.1、执行的实例个数和元数据是否一致
            if (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                instanceToOrderDdlSubMap = orderDdlSubList.stream().collect(Collectors.groupingBy(OrderDdlSub::getPlanAddr));
                if (instanceIdListV2.size() / 2 != instanceToOrderDdlSubMap.size()) {
                    return Pair.of(false, String.format("工单执行的实例个数和元数据中实例个数不一致，元数据中实例个数 %d 工单执行实例个数 %d （多活集群）", instanceIdListV2.size() / 2, instanceToOrderDdlSubMap.size()));
                }
            } else {
                instanceToOrderDdlSubMap = orderDdlSubList.stream().collect(Collectors.groupingBy(OrderDdlSub::getInstanceId));
                if (instanceIdListV2.size() != instanceToOrderDdlSubMap.size()) {
                    return Pair.of(false, String.format("工单执行的实例个数和元数据中实例个数不一致，元数据中实例个数 %d 工单执行实例个数 %d （非多活集群）", instanceIdListV2.size(), instanceToOrderDdlSubMap.size()));
                }
            }

            // 1.2、各个实例执行的表数量是否一致
            String lastInstance = null;
            int lastInstanceDdlCount = 0;
            for (Map.Entry<String, List<OrderDdlSub>> entry : instanceToOrderDdlSubMap.entrySet()) {
                if (lastInstance == null && lastInstanceDdlCount == 0) {
                    lastInstance = entry.getKey();
                    lastInstanceDdlCount = entry.getValue().size();
                } else if (entry.getValue().size() != lastInstanceDdlCount) {
                    return Pair.of(false, String.format("存在两个实例执行的表数量不一致的情况， %s 执行 %d 个 ddl，%s 执行 %d 个 ddl", lastInstance, lastInstanceDdlCount, entry.getKey(), entry.getValue().size()));
                }
            }

            // 2、检查物理集群个数
            if (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE) && physicalClusterList.size() != 2) {
                return Pair.of(false, String.format("v2 元数据中多活集群 %s 的物理集群个数 %d 不是 2 个", cluster.getClusterId(), physicalClusterList.size()));
            }
            if (cluster.getType().equals(Cluster.ROLE_GENERAL) && physicalClusterList.size() != 1) {
                return Pair.of(false, String.format("v2 元数据中非多活集群 %s 的物理集群个数 %d 不是 1 个", cluster.getClusterId(), physicalClusterList.size()));
            }

            // 获取 v1 的实例元数据，同时和 v2 元数据做校验
            if (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                HeboCluster heboClusterV1 = heboClusterMapper.selectOneByClusterId(cluster.getClusterId());
                if (heboClusterV1 == null) {
                    return Pair.of(false, String.format("v1 元数据不存在逻辑库 %s 集群 %s 的信息", logicDatabase.getLogicDbId(), cluster.getClusterId()));
                }
                if (!cluster.getClusterId().equals(heboClusterV1.getClusterId().longValue())
                        || !cluster.getClusterName().equals(heboClusterV1.getClusterName())) {
                    return Pair.of(false, String.format("v1 元数据中集群 %s/%s 和 v2 元数据集群 %s/%s 信息不一致",
                            heboClusterV1.getClusterId(), heboClusterV1.getClusterName(), cluster.getClusterId(), cluster.getClusterName()));
                }
            } else {
                List<PddInstance> instanceListV1;
                if (logicDatabase.getIsShard()) {
                    CoreCdbShard coreCdbShard = coreCdbShardMapper.selectByShardId(physicalClusterList.get(0).getResourceId());
                    List<String> instanceIdListV1 = Arrays.asList(coreCdbShard.getCdbIdList().split(","));
                    instanceListV1 = pddInstanceMapper.selectByPddIds(String.format("'%s'", String.join("', '", instanceIdListV1)));
                } else {
                    PddInstance pddInstance = pddInstanceMapper.selectByPddId(instanceIdListV2.get(0));
                    instanceListV1 = Collections.singletonList(pddInstance);
                }
                Pair<Boolean, String> checkInstanceResult = checkInstance(instanceListV2, instanceListV1);
                if (!checkInstanceResult.getLeft()) {
                    return checkInstanceResult;
                }
            }

            return Pair.of(true, "");
        } catch (Exception e) {
            log.error("metadataCheckBeforeDdl error", e);
            return Pair.of(false, String.format("metadataCheckBeforeDdl error, %s", e.getMessage()));
        }
    }

    private Pair<Boolean, String> checkInstanceDbSyncInfo(List<InstanceDbSyncInfo> instanceDbSyncInfoV2, List<com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.InstanceDbSyncInfo> instanceDbSyncInfoV1) {
        // 比较 size 是否一致
        if (instanceDbSyncInfoV2.size() != instanceDbSyncInfoV1.size()) {
            return Pair.of(false, String.format("v2 [分库粒度同步方向] 元数据数量 %d 和 v1 %d 不一致", instanceDbSyncInfoV2.size(), instanceDbSyncInfoV1.size()));
        }

        instanceDbSyncInfoV2.sort(
                Comparator.comparing(InstanceDbSyncInfo::getInstanceId)
                        .thenComparing(InstanceDbSyncInfo::getRealDbName));
        instanceDbSyncInfoV1.sort(Comparator.comparing(com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.InstanceDbSyncInfo::getInstanceId)
                .thenComparing(com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.InstanceDbSyncInfo::getRealDbName));
        for (int i = 0; i < instanceDbSyncInfoV2.size(); i++) {
            InstanceDbSyncInfo instanceDbSyncInfoV2Item = instanceDbSyncInfoV2.get(i);
            com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.InstanceDbSyncInfo instanceDbSyncInfoV1Item = instanceDbSyncInfoV1.get(i);
            if (!instanceDbSyncInfoV2Item.getInstanceId().equals(instanceDbSyncInfoV1Item.getInstanceId())
                    || !instanceDbSyncInfoV2Item.getSrcInstanceId().equals(instanceDbSyncInfoV1Item.getSrcInstanceId())
                    || !instanceDbSyncInfoV2Item.getIp().equals(instanceDbSyncInfoV1Item.getIp())
                    || !instanceDbSyncInfoV2Item.getPort().equals(instanceDbSyncInfoV1Item.getPort())
                    || !instanceDbSyncInfoV2Item.getRealDbName().equals(instanceDbSyncInfoV1Item.getRealDbName())) {
                return Pair.of(false, String.format("v2 [分库粒度同步方向] 元数据 %s 和 v1 %s 不一致", instanceDbSyncInfoV2Item.getInstanceId() + "/" + instanceDbSyncInfoV2Item.getRealDbName(),
                        instanceDbSyncInfoV1Item.getInstanceId() + "/" + instanceDbSyncInfoV2Item.getRealDbName()));
            }
        }
        return Pair.of(true, "");
    }

    private Pair<Boolean, String> checkInstance(List<Instance> instanceListV2, List<PddInstance> instanceListV1) {
        if (instanceListV2.size() != instanceListV1.size()) {
            return Pair.of(false, String.format("v2 关联实例个数 %d 和 v1 %d 不一致", instanceListV2.size(), instanceListV1.size()));
        }

        instanceListV2.sort(Comparator.comparing(Instance::getInstanceId));
        instanceListV1.sort(Comparator.comparing(PddInstance::getPddId));

        for (int i = 0; i < instanceListV2.size(); i++) {
            Instance instanceV2 = instanceListV2.get(i);
            PddInstance instanceV1 = instanceListV1.get(i);
            if (!instanceV2.getIp().equals(instanceV1.getIp())
                    || !instanceV2.getPort().equals(instanceV1.getPort())) {
                return Pair.of(false, String.format("v2 关联实例 %s:%d 和 v1 %s:%d 不一致", instanceV2.getIp(), instanceV2.getPort(), instanceV1.getIp(), instanceV1.getPort()));
            }
        }

        return Pair.of(true, "");
    }

    public void logicDatabaseCheck(LogicMetadataCheckReq logicMetadataCheckReq) {
        log.info("logicDatabaseCheck start {}", logicMetadataCheckReq);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_logic_database_check_start_msg")
                .issue(String.format("v2_check_start_msg %s", logicMetadataCheckReq)));
        LocalDateTime start = LocalDateTime.now().minusSeconds(2);
        try {
            List<LogicDatabase> logicDatabases;
            switch (logicMetadataCheckReq.getCheckType()) {
                case "all":
                    logicDatabases = logicDatabaseMapper.selectAll();
                    break;
                case "special":
                    List<Long> logicDbIds = logicDatabaseMapper.selectLogicDbIdsByUniqueDbNames(logicMetadataCheckReq.getUniqueDbNames());
                    logicDbIds.addAll(logicMetadataCheckReq.getLogicDbIds() == null ? new ArrayList<>() : logicMetadataCheckReq.getLogicDbIds());
                    logicDatabases = logicDatabaseMapper.selectByLogicDbIds(logicDbIds);
                    break;
                default:
                    log.error("logicDatabaseCheck type {} not support", logicMetadataCheckReq.getCheckType());
                    return;
            }

            log.info("Total LogicDatabases to check: {}", logicDatabases.size());

            Set<Long> logicDbIdWhiteList = LeoUtils.getJsonProperty("mountain-v2-api.logic_db_id_check_white_list", new TypeReference<Set<Long>>() {
            });
            CountDownLatch countDownLatch = new CountDownLatch(logicDatabases.size());
            for (LogicDatabase logicDatabase : logicDatabases) {
                if (logicDbIdWhiteList.contains(logicDatabase.getLogicDbId())) {
                    log.info("logicDbId {} in white list, skip check", logicDatabase.getLogicDbId());
                    countDownLatch.countDown();
                    continue;
                }
                Runnable runnable = () -> {
                    try {
                        checkLogicDatabaseSub(logicDatabase);
                        countDownLatch.countDown();
                    } catch (Exception e) {
                        log.error("checkLogicDatabaseSub runnable exception", e);
                    }
                };
                META_DATA_CHECK_THREAD_POOL.execute(runnable);
            }
            countDownLatch.await();
        } catch (Exception e) {
            log.error("logicDatabaseCheck error", e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_database_check_exception")
                    .issue(String.format("v2_check_exception of logicDatabaseCheck %s", e.getMessage())));
        }
        log.info("logicDatabaseCheck end {}", logicMetadataCheckReq);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_logic_database_check_end_msg")
                .issue(String.format("v2_check_end_msg %s", logicMetadataCheckReq)));
        checkLogicDatabaseCheckResultAndSendInform(start);
    }

    private void checkLogicDatabaseCheckResultAndSendInform(LocalDateTime start) {
        LocalDateTime end = LocalDateTime.now().plusSeconds(2);
        List<MetadataInspectResult> metadataInspectResultList = metadataInspectResultMapper.selectByStartAndEndAndCodePrefix(start, end, "v2_logic_database");
        // 除去 start 和 end 两条记录，超出的，说明有巡检到异常元数据
        if (metadataInspectResultList.size() > 2) {
            String content = String.format("逻辑库巡检存在 %d 条异常记录，请及时查看处理\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_logic_database%%'; ",
                    metadataInspectResultList.size() - 2, start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
                    })
                    , "mountain-v2 元数据巡检异常通知", content);
        } else {
            String content = String.format("逻辑库巡检完成无异常，可通过 SQL 查看开始和结束时间\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_logic_database%%'; ",
                    start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
                    })
                    , "mountain-v2 元数据巡检完成通知", content);
        }

    }

    /**
     * 巡检是否正常
     */
    private boolean checkLogicDatabaseSub(LogicDatabase logicDatabase) {
        try {
            log.info("checkLogicDatabaseSub start {} {}", logicDatabase.getLogicDbId(), logicDatabase);
            // v2 元数据
            Cluster cluster = clusterMapper.selectOneByClusterId(logicDatabase.getClusterId());
            if (cluster == null) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_database_cluster_not_exist")
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .clusterId(logicDatabase.getClusterId().toString())
                        .issue(String.format("v2_cluster_not_exist %s", logicDatabase.getClusterId())));
                return false;
            }
            List<PhysicalCluster> physicalClusterList = physicalClusterMapper.selectByClusterId(cluster.getClusterId());
            if (physicalClusterList.isEmpty()) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_database_physical_cluster_empty")
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .clusterId(cluster.getClusterId().toString())
                        .issue(String.format("v2_physical_cluster_empty %s", cluster.getClusterId())));
                return false;
            }
            List<String> instanceIdList = phyClusterInstanceRelationMapper.selectInstanceIdsByPhysicalClusterIds(physicalClusterList.stream().map(PhysicalCluster::getPhysicalClusterId).collect(Collectors.toList()));
            List<Instance> instanceList = instanceMapper.selectByInstanceIdList(instanceIdList);

            if (instanceList.isEmpty()) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_database_instance_empty")
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .clusterId(cluster.getClusterId().toString())
                        .issue(String.format("v2 instance empty, physical cluster [%s]", physicalClusterList.stream().map(PhysicalCluster::getPhysicalClusterId).collect(Collectors.toList()))));
                return false;
            }
            // 校验单库的实例数量是否准确
            if (!logicDatabase.getIsShard() && instanceList.size() != (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE) ? 2 : 1)) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_database_instance_num_not_match")
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .clusterId(cluster.getClusterId().toString())
                        .issue(String.format("v2 instance num not match, expect %d, actual %d, physical cluster %s cluster is multi %s",
                                cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE) ? 2 : 1,
                                instanceList.size(),
                                physicalClusterList.stream().map(PhysicalCluster::getPhysicalClusterId).collect(Collectors.toList()), cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE))));
                return false;
            }

            // 1. 检查基础元数据：逻辑库，集群，物理集群，实例
            List<PddInstance> pddInstanceList = new ArrayList<>(10);
            if (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                // v1 和 v2 cluster 的 status 含义是相反的
                HeboCluster heboCluster = heboClusterMapper.selectOneByClusterIdAndStatus(cluster.getClusterId(), cluster.getStatus() ? 0 : 1);
                if (heboCluster == null) {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_database_cluster_not_in_v1_hebo_cluster")
                            .logicDbId(logicDatabase.getLogicDbId().toString())
                            .clusterId(cluster.getClusterId().toString())
                            .issue(String.format("v2 cluster not in v1 hebo_cluster, v2 cluster %s status = %s", cluster.getClusterId(), cluster.getStatus())));
                    return false;
                }

                List<HeboClusterResourceGroupOfMF> heboClusterResourceGroupOfMFList = heboClusterResourceGroupMapperOfMF.selectByClusterId(heboCluster.getClusterId());

                if (heboClusterResourceGroupOfMFList == null || heboClusterResourceGroupOfMFList.isEmpty()) {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_database_hebo_cluster_resource_group_empty")
                            .logicDbId(logicDatabase.getLogicDbId().toString())
                            .logicDbName(logicDatabase.getLogicDbName())
                            .clusterId(cluster.getClusterId().toString())
                            .issue("v2 hebo_cluster_resource_group empty or null"));
                    return false;
                }

                List<String> pddIdList = heboClusterResourceGroupOfMFList.stream().map(HeboClusterResourceGroupOfMF::getPddId).collect(Collectors.toList());
                pddInstanceList = pddInstanceMapper.selectByPddIdList(pddIdList);
            } else {
                if (logicDatabase.getIsShard()) {
                    List<CoreCdbShard> coreCdbShardList = coreCdbShardMapper.selectByShardIdList(physicalClusterList.stream().map(PhysicalCluster::getResourceId).collect(Collectors.toList()));
                    if (coreCdbShardList.size() != physicalClusterList.size()) {
                        recordInspectIssue(MetadataInspectResult.builder()
                                .code("v2_logic_database_physical_cluster_not_same_v1_core_cdb_shard")
                                .logicDbId(logicDatabase.getLogicDbId().toString())
                                .clusterId(cluster.getClusterId().toString())
                                .issue(String.format("v2 physical_cluster %s not same v1 core_cdb_shard size: %s",
                                        physicalClusterList.stream().map(PhysicalCluster::getResourceId).collect(Collectors.toList()),
                                        coreCdbShardList.stream().map(CoreCdbShard::getShardId).collect(Collectors.toList()))));
                        return false;
                    }
                    for (CoreCdbShard coreCdbShard : coreCdbShardList) {
                        List<PddInstance> pddInstanceListSub = pddInstanceMapper.selectByPddIdList(Arrays.asList(coreCdbShard.getCdbIdList().split(",")));
                        pddInstanceList.addAll(pddInstanceListSub);
                    }
                } else {
                    pddInstanceList = pddInstanceMapper.selectByPddIdList(instanceList.stream().map(Instance::getInstanceId).collect(Collectors.toList()).subList(0, 1));
                }
            }
            Pair<Boolean, String> instanceCheckResult = checkInstance(instanceList, pddInstanceList);
            if (!instanceCheckResult.getLeft()) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_database_instance_not_same_v1_pdd_instance")
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .clusterId(cluster.getClusterId().toString())
                        .issue(instanceCheckResult.getRight()));
                return false;
            }

            // 2. 检查实例中的库表元数据：逻辑表，逻辑字段
            Map<String, Set<String>> physicalDb2InstanceIdSetMap = new HashMap<>(16);
            List<Integer> physicalDbNumList = new ArrayList<>(10);
            for (Instance instance : instanceList) {
                List<String> physicalDbByLogicDb = InformationSchemaUtil.getPhysicalDbByLogicDb(instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName());
                if (physicalDbByLogicDb.isEmpty()) {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_database_logic_db_not_found_phy_db_in_v1")
                            .logicDbId(logicDatabase.getLogicDbId().toString())
                            .logicDbName(logicDatabase.getLogicDbName())
                            .clusterId(cluster.getClusterId().toString())
                            .instanceId(instance.getInstanceId())
                            .issue("v2_logic_db_not_found_phy_db_in_v1"));
                    return false;
                }
                physicalDbByLogicDb.sort(String::compareTo);
                if (logicDatabase.getIsShard()) {
                    // 如果是 shard，并且多个物理分库，第一个物理分库是个单库，那么移除它
                    // eg: mountain, mountain_1, mountain_10, mountain_100
                    String firstDb = physicalDbByLogicDb.get(0);
                    Pair<String, Integer> logicNameAndSequence = StringExtractUtil.extractLogicNameAndSequenceByCharTraverse(firstDb);
                    if (physicalDbByLogicDb.size() > 1 && logicNameAndSequence.getRight() == -1) {
                        physicalDbByLogicDb = physicalDbByLogicDb.subList(1, physicalDbByLogicDb.size());
                    } else if (physicalDbByLogicDb.size() > 1 && logicNameAndSequence.getRight() != -1) {
                        // 特别地，下面这种需要结合 core_cdb_shard 判断，第一个物理分库的逻辑库名解析为 night_guard 然后匹配不到 shard，那么也移除它
                        // eg: night_guard1, night_guard1_0, night_guard1_1, night_guard1_10
                        CoreCdbShard coreCdbShard = coreCdbShardMapper.selectOneByInstanceIdAndLogicDbName(instance.getInstanceId(), logicNameAndSequence.getLeft());
                        if (coreCdbShard == null) {
                            physicalDbByLogicDb = physicalDbByLogicDb.subList(1, physicalDbByLogicDb.size());
                        }
                    }
                    for (String db : physicalDbByLogicDb) {
                        Pair<String, Integer> logicNameAndSequence2 = StringExtractUtil.extractLogicNameAndSequenceByCharTraverse(db);
                        if (logicNameAndSequence2.getRight() != -1) {
                            physicalDbNumList.add(logicNameAndSequence2.getRight());
                        } else {
                            physicalDbNumList.add(0);
                        }
                        if (!physicalDb2InstanceIdSetMap.containsKey(db)) {
                            physicalDb2InstanceIdSetMap.put(db, new HashSet<>(Collections.singletonList(instance.getInstanceId())));
                        } else {
                            physicalDb2InstanceIdSetMap.get(db).add(instance.getInstanceId());
                        }
                    }
                } else {
                    // 如果不是 shard，只有一个单库，序号定位 0
                    physicalDbNumList.add(0);
                    if (!physicalDb2InstanceIdSetMap.containsKey(physicalDbByLogicDb.get(0))) {
                        physicalDb2InstanceIdSetMap.put(physicalDbByLogicDb.get(0), new HashSet<>(Collections.singletonList(instance.getInstanceId())));
                    } else {
                        physicalDb2InstanceIdSetMap.get(physicalDbByLogicDb.get(0)).add(instance.getInstanceId());
                    }
                }
            }

            // instance_db_info 的校验
            List<InstanceDbInfo> instanceDbInfoList = instanceDbInfoMapper.selectByLogicDbId(logicDatabase.getLogicDbId());
            Map<String, Set<String>> physicalDb2InstanceIdOfDbMap = instanceDbInfoList.stream()
                    .collect(Collectors.groupingBy(InstanceDbInfo::getDbName, Collectors.mapping(InstanceDbInfo::getInstanceId, Collectors.toSet())));
            if (!physicalDb2InstanceIdSetMap.equals(physicalDb2InstanceIdOfDbMap)) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_database_instance_db_info_not_same_real_query")
                        .instanceId(instanceList.get(0).getInstanceId())
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .clusterId(cluster.getClusterId().toString())
                        .issue(String.format("v2 instance_db_info %s not same real query %s", physicalDb2InstanceIdOfDbMap, physicalDb2InstanceIdSetMap)));
                return false;
            }

            physicalDbNumList.sort(Integer::compareTo);
            if (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                // 多活的需要去重两端的重复逻辑库
                physicalDbNumList = physicalDbNumList.stream().distinct().collect(Collectors.toList());
            }
            if (physicalDbNumList.size() != logicDatabase.getDbNum() ||
                    !physicalDbNumList.get(0).equals(logicDatabase.getStart()) ||
                    !physicalDbNumList.get(physicalDbNumList.size() - 1).equals(logicDatabase.getEnd())) {
                if (physicalDbNumList.size() != logicDatabase.getDbNum() && physicalDbNumList.get(0).equals(logicDatabase.getStart()) &&
                        physicalDbNumList.get(physicalDbNumList.size() - 1).equals(logicDatabase.getEnd())) {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_database_physical_db_num_not_same_real_query")
                            .logicDbId(logicDatabase.getLogicDbId().toString())
                            .logicDbName(logicDatabase.getLogicDbName())
                            .clusterId(cluster.getClusterId().toString())
                            .issue(String.format("v2 logic_database size %d start end %d %d, real query size %d start end %d %d"
                                    , logicDatabase.getDbNum(), logicDatabase.getStart(), logicDatabase.getEnd(),
                                    physicalDbNumList.size(), physicalDbNumList.get(0), physicalDbNumList.get(physicalDbNumList.size() - 1)))
                    );
                } else if (physicalDbNumList.size() == logicDatabase.getDbNum() && (!physicalDbNumList.get(0).equals(logicDatabase.getStart()) ||
                        !physicalDbNumList.get(physicalDbNumList.size() - 1).equals(logicDatabase.getEnd()))) {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_database_physical_start_end_not_same_real_query")
                            .logicDbId(logicDatabase.getLogicDbId().toString())
                            .logicDbName(logicDatabase.getLogicDbName())
                            .clusterId(cluster.getClusterId().toString())
                            .issue(String.format("v2 logic_database size %d start end %d %d, real query size %d start end %d %d"
                                    , logicDatabase.getDbNum(), logicDatabase.getStart(), logicDatabase.getEnd(),
                                    physicalDbNumList.size(), physicalDbNumList.get(0), physicalDbNumList.get(physicalDbNumList.size() - 1)))
                    );
                } else {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_database_physical_db_num_start_end_not_same_real_query")
                            .logicDbId(logicDatabase.getLogicDbId().toString())
                            .logicDbName(logicDatabase.getLogicDbName())
                            .clusterId(cluster.getClusterId().toString())
                            .issue(String.format("v2 logic_database size %d start end %d %d, real query size %d start end %d %d"
                                    , logicDatabase.getDbNum(), logicDatabase.getStart(), logicDatabase.getEnd(),
                                    physicalDbNumList.size(), physicalDbNumList.get(0), physicalDbNumList.get(physicalDbNumList.size() - 1)))
                    );
                }
                return false;
            }
            log.info("checkLogicDatabaseSub end {} {}", logicDatabase.getLogicDbId(), logicDatabase);
        } catch (Exception e) {
            log.error("checkLogicDatabase error, logicDbId: {}", logicDatabase.getLogicDbId(), e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_database_sub_check_exception")
                    .logicDbId(logicDatabase.getLogicDbId().toString())
                    .issue(String.format("v2 check exception of checkLogicDatabaseSub: %s", e.getMessage())));
        }
        return true;
    }

    /**
     * 校验 logic_table 和 logic_field 元数据
     */
    private boolean checkLogicTableAndLogicField(LogicDatabase logicDatabase, Instance instance, String phyDatabase, List<String> phyTableListOfRealQuery, List<LogicTable> logicTableList, List<LogicField> logicFieldList) {
        phyTableListOfRealQuery.sort(String::compareTo);
        List<String> logicTableListOfRealQuery = phyTableListOfRealQuery;
        if (logicDatabase.getIsShard()) {
            logicTableListOfRealQuery = getLogicTableListByPhyTableList(phyTableListOfRealQuery);
        }
        logicTableListOfRealQuery.sort(String::compareTo);
        List<String> logicTableListOfV2Sorted = logicTableList.stream().map(LogicTable::getLogicTableName).sorted(String::compareTo).collect(Collectors.toList());
        if (!logicTableListOfV2Sorted.equals(logicTableListOfRealQuery)) {
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_logic_table_not_same_v1_real_query")
                    .logicDbId(logicDatabase.getLogicDbId().toString())
                    .logicDbName(logicDatabase.getLogicDbName())
                    .instanceId(instance.getInstanceId())
                    .issue(String.format("v2 logic_table list %s not same real query logic_table list: %s",
                            logicTableListOfV2Sorted, logicTableListOfRealQuery)));
            return false;
        }

        Map<Long, List<LogicField>> logicFieldMap = logicFieldList.stream().collect(Collectors.groupingBy(LogicField::getLogicTableId));
        for (LogicTable logicTable : logicTableList) {
            List<LogicField> logicFieldListOfTable = logicFieldMap.get(logicTable.getLogicTableId());
            // 校验 logic_table 表其他字段的数据, table_comment 和 audit_created_at 和 audit_updated_at
            PddLogicTable pddLogicTable = pddLogicMetadataService.getPddLogicTableByInstanceDatabaseTable(instance.getInstanceId(), phyDatabase, logicTable.getLogicTableName(), logicDatabase.getIsShard());
            if (pddLogicTable != null) {
                if (!Objects.equals(pddLogicTable.getAuditCreatedAt(), logicTable.getAuditCreatedAt()) || !Objects.equals(pddLogicTable.getAuditUpdatedAt(), logicTable.getAuditUpdatedAt())) {
                    if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_fix_table_time_audit_column", false)) {
                        logicTableMapper.updateTableTimeAuditColumnByLogicTableId(pddLogicTable.getAuditCreatedAt(), pddLogicTable.getAuditUpdatedAt(), logicTable.getLogicTableId());
                        log.warn("checkLogicTableAndLogicField fix logic_table time audit column, logic_table_id: {}, audit_created_at: {}, audit_updated_at: {} pdd_logic_table: {}", logicTable.getLogicTableId(),
                                pddLogicTable.getAuditCreatedAt(), pddLogicTable.getAuditUpdatedAt(), pddLogicTable.getLogicTableId());
                    } else {
                        recordInspectIssue(MetadataInspectResult.builder()
                                .code("v2_logic_table_field_logic_table_audit_time_column_not_same_v1")
                                .logicDbId(logicTable.getLogicDbId().toString())
                                .logicDbName(logicDatabase.getLogicDbName())
                                .logicTableId(logicTable.getLogicTableId().toString())
                                .logicTableName(logicTable.getLogicTableName())
                                .instanceId(instance.getInstanceId())
                                .issue(String.format("逻辑表时间审计列 v1 v2 不一致，v1 pdd_logic_table %d [%s %s] v2 logic_table %d [%s %s]",
                                        pddLogicTable.getLogicTableId(), pddLogicTable.getAuditCreatedAt(), pddLogicTable.getAuditUpdatedAt(),
                                        logicTable.getLogicTableId(), logicTable.getAuditCreatedAt(), logicTable.getAuditUpdatedAt()))
                        );
                    }
                }
            } else {
                log.error("checkLogicTableAndLogicField got pddLogicTable is null {} {} {}", instance.getInstanceId(), phyDatabase, logicTable.getLogicTableName());
            }

            Tables tables;
            if (logicDatabase.getIsShard()) {
                tables = InformationSchemaUtil.getTableMetaInfoByPhyDbAndLogicTable(instance.getIp(), instance.getPort(), phyDatabase, logicTable.getLogicTableName()).get(0);
            } else {
                tables = InformationSchemaUtil.getTableMetaInfoByPhysical(instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName(), logicTable.getLogicTableName());
            }
            if (!logicTable.getTableComment().equals(tables.getTableComment())) {
                if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_fix_table_comment", false)) {
                    logicTableMapper.updateTableCommentByLogicTableId(tables.getTableComment(), logicTable.getLogicTableId());
                    log.warn("checkLogicTableAndLogicField fix table comment, logicTableId: {} {}, tableComment: {} from {}:{}/{}", logicTable.getLogicTableId(), logicTable.getLogicTableName(),
                            tables.getTableComment(), instance.getIp(), instance.getPort(), phyDatabase);
                } else {
                    recordInspectIssue(MetadataInspectResult.builder()
                            .code("v2_logic_table_field_logic_table_comment_not_same_v1")
                            .logicDbId(logicTable.getLogicDbId().toString())
                            .logicDbName(logicDatabase.getLogicDbName())
                            .logicTableId(logicTable.getLogicTableId().toString())
                            .logicTableName(logicTable.getLogicTableName())
                            .instanceId(instance.getInstanceId())
                            .issue(String.format("逻辑表备注 v1 v2 不一致，v1 real query from %s:%d/%s/%s/%s v2 logic_table %d %s",
                                    instance.getIp(), instance.getPort(), phyDatabase, tables.getTableName(), tables.getTableComment(), logicTable.getLogicTableId(), logicTable.getTableComment()))
                    );
                }
            }

            if (logicFieldListOfTable == null || logicFieldListOfTable.isEmpty()) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_table_field_logic_table_not_have_logic_field")
                        .logicDbId(logicTable.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .instanceId(instance.getInstanceId())
                        .logicTableId(logicTable.getLogicTableId().toString())
                        .logicTableName(logicTable.getLogicTableName())
                        .issue("v2 logic_field of the logic_table list is null or empty"));
                return false;
            }

            List<Columns> columnsList = InformationSchemaUtil.getSortedColumnsListByPhysical(instance.getIp(), instance.getPort(), phyDatabase, tables.getTableName());
            List<String> v2LogicFieldName = logicFieldListOfTable.stream().map(LogicField::getLogicFieldName).sorted(String::compareTo).collect(Collectors.toList());
            List<String> v1LogicFieldName = columnsList.stream().map(Columns::getColumnName).sorted(String::compareTo).collect(Collectors.toList());
            if (!v2LogicFieldName.equals(v1LogicFieldName)) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_table_field_logic_field_not_same_v1_real_query")
                        .logicDbId(logicTable.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .instanceId(instance.getInstanceId())
                        .logicTableId(logicTable.getLogicTableId().toString())
                        .logicTableName(logicTable.getLogicTableName())
                        .issue(String.format("v2 logic_field list %s not same v1 logic_field list: %s",
                                v2LogicFieldName, v1LogicFieldName)));
                return false;
            }
            Map<String, String> fieldName2CommentMap = columnsList.stream().collect(Collectors.toMap(Columns::getColumnName, Columns::getColumnComment));
            for (LogicField logicField : logicFieldListOfTable) {
                if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_field_comment", false) &&
                        !logicField.getFieldComment().equals(fieldName2CommentMap.get(logicField.getLogicFieldName()))
                ) {
                    if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_fix_field_comment", false)) {
                        logicFieldMapper.updateFieldCommentByLogicFieldId(fieldName2CommentMap.get(logicField.getLogicFieldName()), logicField.getLogicFieldId());
                        log.warn("checkLogicTableAndLogicField fix field comment, logicFieldId: {} {}, fieldComment: {} from {}:{}/{}/{}", logicField.getLogicFieldId(), logicField.getLogicFieldName(),
                                fieldName2CommentMap.get(logicField.getLogicFieldName()), instance.getIp(), instance.getPort(), phyDatabase, tables.getTableName());
                    } else {
                        recordInspectIssue(MetadataInspectResult.builder()
                                .code("v2_logic_table_field_logic_field_field_comment_not_same_v1")
                                .logicDbId(logicTable.getLogicDbId().toString())
                                .logicDbName(logicDatabase.getLogicDbName())
                                .logicTableId(logicTable.getLogicTableId().toString())
                                .logicTableName(logicTable.getLogicTableName())
                                .instanceId(instance.getInstanceId())
                                .logicFieldId(logicField.getLogicFieldId().toString())
                                .logicFieldName(logicField.getLogicFieldName())
                                .issue(String.format("逻辑字段备注 v1 v2 不一致，v1 real query table %s:%d/%s/%s/%s v2 logic_field %d %s",
                                        instance.getIp(), instance.getPort(), phyDatabase, logicTable.getLogicTableName(), fieldName2CommentMap.get(logicField.getLogicFieldName()), logicField.getLogicFieldId(), logicField.getFieldComment())));
                    }
                }
                CdbField cdbField = cdbFieldMapper.selectByCdbIdAndDbnameAndTbnameAndFieldname(instance.getInstanceId(), phyDatabase, tables.getTableName(), logicField.getLogicFieldName());
                if (cdbField == null) {
                    log.error("checkLogicTableAndLogicField get cdbField is null set level 0, {}/{}/{}/{} other param {} {}", instance.getInstanceId(), phyDatabase, tables.getTableName(), logicField.getLogicFieldName(), logicTable.getLogicTableId(), logicField.getLogicFieldId());
                    cdbField = CdbField.builder().level(0L).build();
                }
                if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_field_level", false) &&
                        !logicField.getLevel().equals(cdbField.getLevel().intValue())) {
                    if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_fix_field_level", false)) {
                        logicFieldMapper.updateLevelByLogicFieldId(cdbField.getLevel().intValue(), logicField.getLogicFieldId());
                        log.warn("checkLogicTableAndLogicField fix field level, logicFieldId: {} {}, level: {} from {}:{}/{}/{}", logicField.getLogicFieldId(), logicField.getLogicFieldName(),
                                cdbField.getLevel().intValue(), instance.getIp(), instance.getPort(), phyDatabase, tables.getTableName());
                    } else {
                        recordInspectIssue(MetadataInspectResult.builder()
                                .code("v2_logic_table_field_logic_field_field_level_not_same_v1")
                                .logicDbId(logicTable.getLogicDbId().toString())
                                .logicDbName(logicDatabase.getLogicDbName())
                                .logicTableId(logicTable.getLogicTableId().toString())
                                .logicTableName(logicTable.getLogicTableName())
                                .instanceId(instance.getInstanceId())
                                .logicFieldId(logicField.getLogicFieldId().toString())
                                .logicFieldName(logicField.getLogicFieldName())
                                .issue(String.format("逻辑字段安全等级 v1 v2 不一致，v1 real query table %s:%d/%s/%s/%s/%s v2 logic_field %d %s",
                                        instance.getIp(), instance.getPort(), phyDatabase, tables.getTableName(), logicField.getLogicFieldName(), cdbField.getLevel().intValue(), logicField.getLogicFieldId(), logicField.getLevel())));
                    }
                }
                CoreCdbEncryption coreCdbEncryption = coreCdbEncryptionMapper.selectByCdbIdAndDbnameAndTbnameAndField(instance.getInstanceId(), phyDatabase, tables.getTableName(), logicField.getLogicFieldName());
                if (coreCdbEncryption == null) {
                    log.error("checkLogicTableAndLogicField get coreCdbEncryption is null, {}/{}/{}/{} other param {} {}", instance.getInstanceId(), phyDatabase, tables.getTableName(), logicField.getLogicFieldName(), logicTable.getLogicTableId(), logicField.getLogicFieldId());
                    coreCdbEncryption = CoreCdbEncryption.builder().securitykey("").business("").build();
                }
                if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_field_key", false) &&
                        (!logicField.getSecurityKey().equals(coreCdbEncryption.getSecuritykey()) || !logicField.getSecurityPlatform().equals(coreCdbEncryption.getBusiness()))) {
                    if (LeoUtils.getBooleanProperty("mountain-v2-api.logic_table_field_check_fix_field_key", false)) {
                        logicFieldMapper.updateSecurityKeyAndSecurityPlatformByLogicFieldId(coreCdbEncryption.getSecuritykey(), coreCdbEncryption.getBusiness(), logicField.getLogicFieldId());
                        log.warn("checkLogicTableAndLogicField fix field key, logicFieldId: {} {}, securityKey: {} {} from {}:{}/{}/{}", logicField.getLogicFieldId(), logicField.getLogicFieldName(),
                                coreCdbEncryption.getSecuritykey(), coreCdbEncryption.getBusiness(), instance.getIp(), instance.getPort(), phyDatabase, tables.getTableName());
                    } else {
                        recordInspectIssue(MetadataInspectResult.builder()
                                .code("v2_logic_table_field_logic_field_field_security_key_not_same_v1")
                                .logicDbId(logicTable.getLogicDbId().toString())
                                .logicDbName(logicDatabase.getLogicDbName())
                                .logicTableId(logicTable.getLogicTableId().toString())
                                .logicTableName(logicTable.getLogicTableName())
                                .instanceId(instance.getInstanceId())
                                .logicFieldId(logicField.getLogicFieldId().toString())
                                .logicFieldName(logicField.getLogicFieldName())
                                .issue(String.format("逻辑字段加密key v1 v2 不一致，v1 real query table %s:%d/%s/%s/%s v2 logic_field %d %s",
                                        instance.getIp(), instance.getPort(), phyDatabase, logicTable.getLogicTableName(), coreCdbEncryption.getSecuritykey(), logicField.getLogicFieldId(), logicField.getFieldComment())));
                    }
                }
            }
        }
        return true;
    }

    /**
     * 校验 instance_db_info 元数据和 instance_table_info 元数据
     */
    private boolean checkPhysicalDatabaseAndPhysicalTable(LogicDatabase logicDatabase, Instance instance, List<String> physicalDbList, List<InstanceDbInfo> instanceDbInfoList, Map<String, List<String>> physicalDb2PhysicalTableMap) {
        // instance_db_info 元数据
        physicalDbList.sort(String::compareTo);
        List<String> instanceDbInfoDbNameList = instanceDbInfoList.stream().map(InstanceDbInfo::getDbName).sorted(String::compareTo).collect(Collectors.toList());
        if (instanceDbInfoList.isEmpty()) {
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_instance_db_info_empty")
                    .logicDbId(logicDatabase.getLogicDbId().toString())
                    .logicDbName(logicDatabase.getLogicDbName())
                    .instanceId(instance.getInstanceId())
                    .issue("v2 instance_db_info is empty"));
            return false;
        }

        if (!physicalDbList.equals(instanceDbInfoDbNameList)) {
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_instance_db_info_not_same_v1_physical_db")
                    .logicDbId(logicDatabase.getLogicDbId().toString())
                    .logicDbName(logicDatabase.getLogicDbName())
                    .instanceId(instance.getInstanceId())
                    .issue(String.format("v2 instance_db_info db_name list %s not same v1 physical_db list: %s",
                            instanceDbInfoDbNameList, physicalDbList)));
            return false;
        }
        Set<Long> instanceDbIdSet = instanceDbInfoList.stream().map(InstanceDbInfo::getInstanceDbId).collect(Collectors.toSet());

        // instance_table_info 元数据
        for (String physicalDb : physicalDbList) {
            List<String> physicalTableList = InformationSchemaUtil.getAllBusinessPhyTablesByIpPortAndPhyDatabase(instance.getIp(), instance.getPort(), physicalDb);
            physicalTableList.sort(String::compareTo);
            physicalDb2PhysicalTableMap.put(physicalDb, physicalTableList);
            List<InstanceTableInfo> instanceTableInfoList = instanceTableInfoMapper.selectByInstanceIdAndDbName(instance.getInstanceId(), physicalDb);
            instanceTableInfoList = instanceTableInfoList.stream().filter(instanceTableInfo -> instanceDbIdSet.contains(instanceTableInfo.getInstanceDbId())).collect(Collectors.toList());
            List<String> instanceTableInfoTableNameList = instanceTableInfoList.stream().map(InstanceTableInfo::getTableName).sorted(String::compareTo).collect(Collectors.toList());
            if (!physicalTableList.equals(instanceTableInfoTableNameList)) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .code("v2_logic_table_field_instance_table_info_not_same_v1_physical_table")
                        .logicDbId(logicDatabase.getLogicDbId().toString())
                        .logicDbName(logicDatabase.getLogicDbName())
                        .instanceId(instance.getInstanceId())
                        .issue(String.format("v2 instance_table_info table_name list %s not same v1 physical_table list: %s",
                                instanceTableInfoTableNameList, physicalTableList)));
                return false;
            }
        }
        return true;
    }

    private void recordInspectIssue(MetadataInspectResult.MetadataInspectResultBuilder builder) {
        try {
            MetadataInspectResult result = builder.build().fillZeroOnNullField();
            metadataInspectResultMapper.insert(result);
        } catch (Exception e) {
            log.error("Failed to record metadata inspect issue, {}", builder, e);
        }
    }

    public void coreCdbShardCheck(CoreCdbShardCheckReq req) {
        log.info("coreCdbShardCheck start req: {}", req);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_core_cdb_shard_check_start_msg")
                .issue(String.format("v2 core_cdb_shard check start %s", req)));
        LocalDateTime start = LocalDateTime.now().minusSeconds(2);
        try {
            List<CoreCdbShard> coreCdbShardList;
            if (req.getCheckType().equals("all")) {
                coreCdbShardList = coreCdbShardMapper.selectAll();
            } else {
                coreCdbShardList = coreCdbShardMapper.selectByShardNames(req.getShardNames());
            }
            log.info("coreCdbShardCheck check total: {}", coreCdbShardList.size());

            Set<Long> shardIdWhiteList = LeoUtils.getJsonProperty("mountain-v2-api.shard_id_check_white_list", new TypeReference<Set<Long>>() {
            });
            CountDownLatch countDownLatch = new CountDownLatch(coreCdbShardList.size());
            for (CoreCdbShard coreCdbShard : coreCdbShardList) {
                if (shardIdWhiteList.contains(coreCdbShard.getShardId().longValue())) {
                    log.info("coreCdbShardCheck shardIdWhiteList contains shardId: {}, skip check", coreCdbShard.getShardId());
                    countDownLatch.countDown();
                    continue;
                }
                Runnable runnable = () -> {
                    try {
                        checkCoreCdbShard(coreCdbShard);
                        countDownLatch.countDown();
                    } catch (Exception e) {
                        log.error("coreCdbShardCheck runnable exception", e);
                    }
                };
                META_DATA_CHECK_THREAD_POOL.execute(runnable);
            }
            countDownLatch.await();
        } catch (Exception e) {
            log.error("coreCdbShardCheck error {}", req, e);
            recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_check_exception")
                    .issue(String.format("v2 launch core_cdb_shard check error %s", e.getMessage())));
        }

        recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_check_end_msg")
                .issue(String.format("v2 core_cdb_shard check end %s", req)));
        checkCoreCdbShardCheckResultAndSendInform(start);
    }

    private void checkCoreCdbShardCheckResultAndSendInform(LocalDateTime start) {
        LocalDateTime end = LocalDateTime.now().plusSeconds(2);
        List<MetadataInspectResult> metadataInspectResultList = metadataInspectResultMapper.selectByStartAndEndAndCodePrefix(start, end, "v2_core_cdb_shard");
        // 除去 start 和 end 两条记录，超出的，说明有巡检到异常元数据
        if (metadataInspectResultList.size() > 2) {
            String content = String.format("shard 巡检存在 %d 条异常记录，请及时查看处理\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_core_cdb_shard%%'; ",
                    metadataInspectResultList.size() - 2, start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
                    })
                    , "mountain-v2 元数据巡检异常通知", content);
        } else {
            String content = String.format("shard 巡检完成无异常，可通过 SQL 查看开始和结束时间\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_core_cdb_shard%%'; ",
                    start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
                    })
                    , "mountain-v2 元数据巡检完成通知", content);
        }
    }

    private void checkCoreCdbShard(CoreCdbShard coreCdbShard) {
        try {
            List<String> pddIdList = Arrays.asList(coreCdbShard.getCdbIdList().split(","));
            List<PddInstance> pddInstanceList = pddInstanceMapper.selectByPddIdList(pddIdList);
            Pair<Boolean, String> checkResult = containsNotNeedTransferInstance(pddInstanceList);
            if (checkResult.getLeft()) {
                log.warn("coreCdbShardCheck contains not need transfer instance, skip check, {}, {}", coreCdbShard.getShardName(), checkResult.getRight());
                return;
            }

            pddIdList.sort(String::compareTo);
            boolean findInV2 = false;
            Long findLogicDbId = null;
            // 根据这些实例找到 v2 中涉及的物理集群
            List<PhyClusterInstanceRelation> pcirListByPddIdList = phyClusterInstanceRelationMapper.selectByInstanceIds(pddIdList);
            // 找到这些物理集群的所有关系记录
            List<PhyClusterInstanceRelation> pcirListByPhyClusterIds = phyClusterInstanceRelationMapper.selectByPhyClusterIds(pcirListByPddIdList.stream().map(PhyClusterInstanceRelation::getPhysicalClusterId).distinct().collect(Collectors.toList()));
            // 按照 physical_cluster_id 分组，每组是对应的 instance_id 列表
            Map<Long, List<String>> groupByPhysicalClusterId = pcirListByPhyClusterIds.stream().
                    collect(Collectors.groupingBy(PhyClusterInstanceRelation::getPhysicalClusterId, Collectors.mapping(PhyClusterInstanceRelation::getInstanceId, Collectors.toList())));
            for (Map.Entry<Long, List<String>> entry : groupByPhysicalClusterId.entrySet()) {
                List<String> instanceIdList = entry.getValue();
                instanceIdList.sort(String::compareTo);
                if (!instanceIdList.equals(pddIdList)) {
                    continue;
                }
                log.info("find physical_cluster of this core_cdb_shard, {} physical_cluster_id {}", coreCdbShard.getShardName(), entry.getKey());
                // 找到实例一致的物理集群，再看关联的逻辑库是不是和 core_cdb_shard 一致
                PhysicalCluster physicalCluster = physicalClusterMapper.selectOneByPhyClusterId(entry.getKey());
                if (physicalCluster == null) {
                    recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_physical_cluster_not_exist_from_pc_ins_relation")
                            .shardId(coreCdbShard.getShardId().longValue())
                            .physicalClusterId(entry.getKey().toString())
                            .issue("v2_physical_cluster_not_exist_of_relation"));
                    continue;
                }
                Cluster cluster = clusterMapper.selectOneByClusterId(physicalCluster.getClusterId());
                if (cluster == null) {
                    recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_cluster_not_exist_from_physical_cluster")
                            .clusterId(physicalCluster.getClusterId().toString())
                            .shardId(coreCdbShard.getShardId().longValue())
                            .physicalClusterId(physicalCluster.getPhysicalClusterId().toString())
                            .issue("v2_cluster_not_exist_of_physical_cluster"));
                    continue;
                }
                List<LogicDatabase> logicDatabaseList = logicDatabaseMapper.selectByClusterIdAndIsShard(cluster.getClusterId(), 1);
                for (LogicDatabase logicDatabase : logicDatabaseList) {
                    log.info("find logic_database, cluster, physical_cluster of this core_cdb_shard, {} logic_database {} cluster {} physical_cluster {}, logic db of shard {} v2 logic db {}",
                            coreCdbShard.getShardName(), logicDatabase.getLogicDbId(), cluster.getClusterId(), physicalCluster.getPhysicalClusterId(), coreCdbShard.getShardName(), logicDatabase.getLogicDbName());
                    if (logicDatabase.getLogicDbName().equals(coreCdbShard.getDatabaseNameList()) || (logicDatabase.getLogicDbName() + "_").equals(coreCdbShard.getShardName())) {
                        // 多活集群时，会存在一个 shard 对应 v2 的两个物理集群
                        // 例如 cluster1: sh2 sh5, cluster2: sh2 shb1，sh2 的个 shard 就对应了两个 v2 的物理集群
                        if (findInV2 && !cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                            recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_find_not_only")
                                    .logicDbId(logicDatabase.getLogicDbId().toString())
                                    .logicDbName(logicDatabase.getLogicDbName())
                                    .clusterId(cluster.getClusterId().toString())
                                    .physicalClusterId(physicalCluster.getPhysicalClusterId().toString())
                                    .shardId(coreCdbShard.getShardId().longValue())
                                    .issue(String.format("v2_core_cdb_shard_find_not_only, %s, curr %s last %s",
                                            coreCdbShard.getShardName(), logicDatabase.getLogicDbId(), findLogicDbId)));
                        }
                        findInV2 = true;
                        findLogicDbId = logicDatabase.getLogicDbId();
                    }
                }
            }
            if (!findInV2) {
                recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_not_transfer")
                        .shardId(coreCdbShard.getShardId().longValue())
                        .instanceId(pddIdList.get(0))
                        .logicDbName(coreCdbShard.getDatabaseNameList())
                        .issue(String.format("v2_core_cdb_shard_not_exist, %s", coreCdbShard.getShardName())));
            }
        } catch (Exception e) {
            log.error("coreCdbShardCheck checkCoreCdbShard error {}", coreCdbShard, e);
            recordInspectIssue(MetadataInspectResult.builder().code("v2_core_cdb_shard_sub_check_exception")
                    .issue(String.format("v2 core_cdb_shard check error %s", e.getMessage())));
        }
    }

    private Pair<Boolean, String> containsNotNeedTransferInstance(List<PddInstance> pddInstanceList) {
        for (PddInstance pddInstance : pddInstanceList) {
            if (pddInstance.getType().equals("isv") || pddInstance.getTaglist().contains("canotaccess") || pddInstance.getBusiness().equals("knock平台")
                    || pddInstance.getPddId().equals("cdb-jmrnkg3v") || pddInstance.getPddId().equals("cdb-5ia8v481")) {
                return Pair.of(true, String.format("pdd_instance %s info: %s %s %s", pddInstance.getPddId(), pddInstance.getBusiness(), pddInstance.getType(), pddInstance.getTaglist()));
            }
        }
        return Pair.of(false, "");
    }

    public void heboClusterCheck(HeboClusterCheckReq req) {
        log.info("heboClusterCheck req {}", req);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_hebo_cluster_check_start_msg")
                .issue(String.format("v2_hebo_cluster_check_start_msg, %s", req.getCheckType())));
        LocalDateTime start = LocalDateTime.now().minusSeconds(2);
        try {
            List<HeboCluster> heboClusterList;
            if (req.getCheckType().equals("all")) {
                heboClusterList = heboClusterMapper.selectByBusiness("knock平台");
            } else {
                heboClusterList = heboClusterMapper.selectByClusterIds(req.getClusterIds());
            }
            log.info("heboClusterList to check total {}", heboClusterList.size());

            Set<Long> heboClusterIdWhiteList = LeoUtils.getJsonProperty("mountain-v2-api.hebo_cluster_id_check_white_list", new TypeReference<Set<Long>>() {
            });
            CountDownLatch countDownLatch = new CountDownLatch(heboClusterList.size());
            for (HeboCluster heboCluster : heboClusterList) {
                if (heboClusterIdWhiteList.contains(heboCluster.getClusterId().longValue())) {
                    log.warn("heboClusterIdWhiteList contains heboClusterId {}, skip it", heboCluster.getClusterId());
                    countDownLatch.countDown();
                    continue;
                }
                Runnable runnable = () -> {
                    try {
                        heboClusterCheckSub(heboCluster);
                        countDownLatch.countDown();
                    } catch (Exception e) {
                        log.error("heboClusterCheck runnable error {}", heboCluster, e);
                    }
                };
                META_DATA_CHECK_THREAD_POOL.execute(runnable);
            }
            countDownLatch.await();
        } catch (Exception e) {
            log.error("heboClusterCheck error {}", req, e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_hebo_cluster_check_exception")
                    .issue(String.format("v2_hebo_cluster_check_exception, %s", e.getMessage())));
        }
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_hebo_cluster_check_end_msg")
                .issue(String.format("v2_hebo_cluster_check_end_msg, %s", req.getCheckType())));
        checkHeboClusterCheckResultAndSendInform(start);
    }

    private void heboClusterCheckSub(HeboCluster heboCluster) {
        try {
            Cluster cluster = clusterMapper.selectOneByClusterId(heboCluster.getClusterId().longValue());
            if (cluster == null) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .clusterId(heboCluster.getClusterId().toString())
                        .code("v2_hebo_cluster_not_found_cluster_in_v2")
                        .issue(String.format("v2_hebo_cluster_not_found_cluster_in_v2, %s", heboCluster.getClusterName())));
                return;
            }
            int statusV1 = heboCluster.getStatus();
            int statusV2 = cluster.getStatus() ? 1 : 0;
            if (statusV1 == statusV2) { // v1 的 status 和 v2 的 status 含义相反，所以这里相等反而是转换不对
                recordInspectIssue(MetadataInspectResult.builder()
                        .clusterId(heboCluster.getClusterId().toString())
                        .code("v2_hebo_cluster_status_not_consistent_with_cluster_in_v2")
                        .issue(String.format("v1 status %s v2 status %s", heboCluster.getStatus(), cluster.getStatus())));
                return;
            }
            if (!cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .clusterId(heboCluster.getClusterId().toString())
                        .code("v2_hebo_cluster_not_cluster_multi_active_in_v2")
                        .issue(String.format("v2_hebo_cluster_not_cluster_multi_active_in_v2, %s", heboCluster.getClusterName())));
                return;
            }
            String logicDbName = heboCluster.getDatabaseNameList();
            if (logicDbName.endsWith("_")) {
                logicDbName = logicDbName.substring(0, logicDbName.length() - 1);
            }
            List<LogicDatabase> logicDatabaseList = logicDatabaseMapper.selectByClusterIdAndIsShardAndLogicDbName(heboCluster.getClusterId().longValue(), heboCluster.getIsShard(), logicDbName);
            if (logicDatabaseList.isEmpty()) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .clusterId(heboCluster.getClusterId().toString())
                        .logicDbName(logicDbName)
                        .code("v2_hebo_cluster_not_found_logic_db_in_v2")
                        .issue(String.format("v2_hebo_cluster_not_found_logic_db_in_v2, %s", heboCluster.getClusterName())));
                return;
            } else if (logicDatabaseList.size() > 1) {
                recordInspectIssue(MetadataInspectResult.builder()
                        .clusterId(heboCluster.getClusterId().toString())
                        .logicDbName(logicDbName)
                        .code("v2_hebo_cluster_found_more_than_one_logic_db_in_v2")
                        .issue(String.format("v2_hebo_cluster_found_more_than_one_logic_db_in_v2, %s", heboCluster.getClusterName())));
                return;
            }
        } catch (Exception e) {
            log.error("heboClusterCheckSub exception {}", heboCluster, e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_hebo_cluster_sub_check_exception")
                    .issue(String.format("v2 hebo_cluster check error %s", e.getMessage())));
        }
    }

    private void checkHeboClusterCheckResultAndSendInform(LocalDateTime start) {
        LocalDateTime end = LocalDateTime.now().plusSeconds(2);
        List<MetadataInspectResult> metadataInspectResultList = metadataInspectResultMapper.selectByStartAndEndAndCodePrefix(start, end, "v2_hebo_cluster");
        // 除去 start 和 end 两条记录，超出的，说明有巡检到异常元数据
        if (metadataInspectResultList.size() > 2) {
            String content = String.format("hebo_cluster 巡检存在 %d 条异常记录，请及时查看处理\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_hebo_cluster%%'; ",
                    metadataInspectResultList.size() - 2, start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
            }), "mountain-v2 元数据巡检异常通知", content);
        } else {
            String content = String.format("hebo_cluster 巡检完成无异常，可通过 SQL 查看开始和结束时间\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_hebo_cluster%%'; ",
                    start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
            }), "mountain-v2 元数据巡检完成通知", content);
        }
    }

    public void logicTableAndFieldCheck(LogicMetadataCheckReq logicMetadataCheckReq) {
        log.info("logicTableAndFieldCheck start {}", logicMetadataCheckReq);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_logic_table_field_check_start_msg")
                .issue(String.format("v2_check_start_msg %s", logicMetadataCheckReq)));
        LocalDateTime start = LocalDateTime.now().minusSeconds(2);
        try {
            List<LogicDatabase> logicDatabases;
            switch (logicMetadataCheckReq.getCheckType()) {
                case "all":
                    logicDatabases = logicDatabaseMapper.selectAll();
                    break;
                case "special":
                    List<Long> logicDbIds = logicDatabaseMapper.selectLogicDbIdsByUniqueDbNames(logicMetadataCheckReq.getUniqueDbNames());
                    logicDbIds.addAll(logicMetadataCheckReq.getLogicDbIds() == null ? new ArrayList<>() : logicMetadataCheckReq.getLogicDbIds());
                    logicDatabases = logicDatabaseMapper.selectByLogicDbIds(logicDbIds);
                    break;
                default:
                    log.error("logicTableAndFieldCheck type {} not support", logicMetadataCheckReq.getCheckType());
                    return;
            }

            log.info("Total logicTableAndFieldCheck to check: {}", logicDatabases.size());

            Set<Long> logicDbIdWhiteList = LeoUtils.getJsonProperty("mountain-v2-api.logic_db_id_check_white_list", new TypeReference<Set<Long>>() {
            });
            CountDownLatch countDownLatch = new CountDownLatch(logicDatabases.size());
            for (LogicDatabase logicDatabase : logicDatabases) {
                if (logicDbIdWhiteList.contains(logicDatabase.getLogicDbId())) {
                    log.info("logicDbId {} in white list, skip check", logicDatabase.getLogicDbId());
                    countDownLatch.countDown();
                    continue;
                }
                Runnable runnable = () -> {
                    try {
                        checkLogicTableAndFieldSub(logicDatabase);
                        countDownLatch.countDown();
                    } catch (Exception e) {
                        log.error("checkLogicTableAndFieldSub runnable exception", e);
                    }
                };
                META_DATA_CHECK_THREAD_POOL.execute(runnable);
            }
            countDownLatch.await();
        } catch (Exception e) {
            log.error("logicTableAndFieldCheck error", e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_check_exception")
                    .issue(String.format("v2_check_exception of logicTableAndFieldCheck %s", e.getMessage())));
        }
        log.info("logicTableAndFieldCheck end {}", logicMetadataCheckReq);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_logic_table_field_check_end_msg")
                .issue(String.format("v2_check_end_msg %s", logicMetadataCheckReq)));
        checkLogicTableAndFieldCheckResultAndSendInform(start);
    }

    private void checkLogicTableAndFieldCheckResultAndSendInform(LocalDateTime start) {
        LocalDateTime end = LocalDateTime.now().plusSeconds(2);
        List<MetadataInspectResult> metadataInspectResultList = metadataInspectResultMapper.selectByStartAndEndAndCodePrefix(start, end, "v2_logic_table_field");
        // 除去 start 和 end 两条记录，超出的，说明有巡检到异常元数据
        if (metadataInspectResultList.size() > 2) {
            String content = String.format("逻辑表和逻辑字段巡检存在 %d 条异常记录，请及时查看处理\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_logic_table_field%%'; ",
                    metadataInspectResultList.size() - 2, start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
                    })
                    , "mountain-v2 元数据巡检异常通知", content);
        } else {
            String content = String.format("逻辑表和逻辑字段巡检完成无异常，可通过 SQL 查看开始和结束时间\n查看 SQL：SELECT * FROM metadata_inspect_result WHERE created_at >= '%s' AND created_at <= '%s' AND code LIKE 'v2_logic_table_field%%'; ",
                    start.format(Time.FORMATTER), end.format(Time.FORMATTER));
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
                    })
                    , "mountain-v2 元数据巡检完成通知", content);
        }
    }

    private void checkLogicTableAndFieldSub(LogicDatabase logicDatabase) {
        try {
            log.info("checkLogicTableAndFieldSub start {} {}", logicDatabase.getLogicDbId(), logicDatabase);
            // v2 元数据
            Cluster cluster = clusterMapper.selectOneByClusterId(logicDatabase.getClusterId());
            List<PhysicalCluster> physicalClusterList = physicalClusterMapper.selectByClusterId(cluster.getClusterId());
            List<String> instanceIdList = phyClusterInstanceRelationMapper.selectInstanceIdsByPhysicalClusterIds(physicalClusterList.stream().map(PhysicalCluster::getPhysicalClusterId).collect(Collectors.toList()));
            List<Instance> instanceList = instanceMapper.selectByInstanceIdList(instanceIdList);
            List<LogicTable> logicTableList = logicTableMapper.selectByLogicDbId(logicDatabase.getLogicDbId());
            List<LogicField> logicFieldList = new ArrayList<>(10);
            // 每次 100 个 logicTableList 查询，避免慢查把 mountain 库 cpu 打得太高
            for (int i = 0; i < logicTableList.size(); i += 100) {
                List<LogicTable> logicTableListBatch = logicTableList.subList(i, Math.min(i + 100, logicTableList.size()));
                List<Long> logicTableIdList = logicTableListBatch.stream().map(LogicTable::getLogicTableId).collect(Collectors.toList());
                logicFieldList.addAll(logicFieldMapper.selectByLogicTableIds(logicTableIdList));
                Thread.sleep(100);
            }

            // 先找到逻辑库下的实例，遍历每个实例
            Map<String, String> physicalDb2InstanceIdMap = new HashMap<>(16);
            Map<String, List<String>> physicalDb2PhysicalTableMap = new HashMap<>(16);
            for (int instanceIndex = 0; instanceIndex < instanceList.size(); instanceIndex++) {
                Instance instance = instanceList.get(instanceIndex);
                // 再找到每个实例中的物理库
                List<String> physicalDbByLogicDb = InformationSchemaUtil.getPhysicalDbByLogicDb(instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName());
                physicalDbByLogicDb.sort(String::compareTo);
                if (logicDatabase.getIsShard()) {
                    // 如果是 shard，并且多个物理分库，第一个物理分库是个单库，那么移除它
                    // eg: mountain, mountain_1, mountain_10, mountain_100
                    String firstDb = physicalDbByLogicDb.get(0);
                    Pair<String, Integer> logicNameAndSequence = StringExtractUtil.extractLogicNameAndSequenceByCharTraverse(firstDb);
                    if (physicalDbByLogicDb.size() > 1 && logicNameAndSequence.getRight() == -1) {
                        physicalDbByLogicDb = physicalDbByLogicDb.subList(1, physicalDbByLogicDb.size());
                    } else if (physicalDbByLogicDb.size() > 1 && logicNameAndSequence.getRight() != -1) {
                        // 特别地，下面这种需要结合 core_cdb_shard 判断，第一个物理分库的逻辑库名解析为 night_guard 然后匹配不到 shard，那么也移除它
                        // eg: night_guard1, night_guard1_0, night_guard1_1, night_guard1_10
                        CoreCdbShard coreCdbShard = coreCdbShardMapper.selectOneByInstanceIdAndLogicDbName(instance.getInstanceId(), logicNameAndSequence.getLeft());
                        if (coreCdbShard == null) {
                            physicalDbByLogicDb = physicalDbByLogicDb.subList(1, physicalDbByLogicDb.size());
                        }
                    }
                } else {
                    // 如果不是 shard，只保留和逻辑库名相等的那个物理分库
                    physicalDbByLogicDb = physicalDbByLogicDb.stream().filter(db -> db.equals(logicDatabase.getLogicDbName())).collect(Collectors.toList());
                }
                List<InstanceDbInfo> instanceDbInfoList = instanceDbInfoMapper.selectInstanceDbInfoByLogicDbIdAndInstanceId(logicDatabase.getLogicDbId(), instance.getInstanceId());
                // 校验这个实例的 instance_db_info 元数据和 instance_table_info 元数据
                boolean result = checkPhysicalDatabaseAndPhysicalTable(logicDatabase, instance, physicalDbByLogicDb, instanceDbInfoList, physicalDb2PhysicalTableMap);
                if (!result) {
                    // 校验 instance_db_info 元数据和 instance_table_info 元数据失败，就不继续校验接下来的元数据了，很有可能是不对的，并且下面的校验有依赖上面这个校验过程中保存的 physicalDb2PhysicalTableMap
                    return;
                }

                // 遍历每个物理库
                for (String physicalDb : physicalDbByLogicDb) {
                    physicalDb2InstanceIdMap.put(physicalDb, instance.getInstanceId());
                }

                // 校验第一个实例的 logic_table 元数据和 logic_field 元数据，如果每个实例都校验，会很耗时
                if (instanceIndex == 0) {
                    List<String> phyTableListOfRealQuery = physicalDb2PhysicalTableMap.get(physicalDbByLogicDb.get(0));
                    result = checkLogicTableAndLogicField(logicDatabase, instance, physicalDbByLogicDb.get(0), phyTableListOfRealQuery, logicTableList, logicFieldList);
                    if (!result) {
                        // 校验 logic_table 元数据和 logic_field 元数据失败，就不继续校验接下来的元数据了，很有可能是不对的
                        return;
                    }
                }
            }
            // 校验实际的物理库表是否符合规范用法
            // https://note.pdd.net/doc/853740040664104960
            boolean logicTableValidUsageCheckResult = checkLogicTableValidUsage(logicDatabase, physicalDb2InstanceIdMap, physicalDb2PhysicalTableMap);
            if (!logicTableValidUsageCheckResult) {
                return;
            }
            log.info("checkLogicTableAndFieldSub end {} {}", logicDatabase.getLogicDbId(), logicDatabase);
        } catch (Exception e) {
            log.error("checkLogicTableAndFieldSub error, logicDbId: {}", logicDatabase.getLogicDbId(), e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_sub_check_exception")
                    .logicDbId(logicDatabase.getLogicDbId().toString())
                    .issue(String.format("v2 check exception of checkLogicTableAndFieldSub: %s", e.getMessage())));
        }
    }

    public boolean checkLogicTableValidUsage(LogicDatabase logicDatabase, Map<String, String> physicalDb2InstanceIdMap, Map<String, List<String>> physicalDb2PhysicalTableMap) {
        Map<String, Set<String>> physicalDb2LogicTableMap = new HashMap<>(16);
        Map<String, Map<String, Set<Integer>>> physicalDb2LogicTable2SequenceMap = new HashMap<>(16);

        // 先遍历收集好数据
        // 遍历物理库
        for (String physicalDb : physicalDb2PhysicalTableMap.keySet()) {
            String instanceId = physicalDb2InstanceIdMap.get(physicalDb);

            Set<String> logicTableSet = new HashSet<>(16);
            Map<String, Set<Integer>> logicTable2SequenceMap = new HashMap<>(16);
            // 遍历物理表
            for (String physicalTable : physicalDb2PhysicalTableMap.get(physicalDb)) {
                String logicTableName = null;
                Integer sequence = null;
                if (logicDatabase.getIsShard()) {
                    Pair<String, Integer> stringIntegerPair = StringExtractUtil.extractLogicNameAndSequenceByCharTraverse(physicalTable);
                    logicTableName = stringIntegerPair.getLeft();
                    sequence = stringIntegerPair.getRight();
                } else {
                    logicTableName = physicalTable;
                    sequence = -1;
                }

                //                if (sequence == -1 && logicTable2SequenceMap.containsKey(logicTableName)) {
                //                    // 已经有了逻辑表 logicTableName，发现又遇到一个
                //                    // 例如解析了 order_test_0, order_test_1 又来一个 order_test 那就不合法
                //                    recordInspectIssue(MetadataInspectResult.builder()
                //                            .code("v2_logic_table_field_repeat_logic_table")
                //                            .logicDbId(logicDatabase.getLogicDbId().toString())
                //                            .logicDbName(logicDatabase.getLogicDbName())
                //                            .instanceId(instanceId)
                //                            .issue(String.format("逻辑库 %s 存在重复的逻辑表，可能为分库分表和分库单表共存的情况，不规范，实例 %s 物理库 %s", logicDatabase.getLogicDbName(), instanceId, physicalDb))
                //                    );
                //                    return false;
                //                }

                Integer finalSequence = sequence;
                if (logicTable2SequenceMap.containsKey(logicTableName)) {
                    logicTable2SequenceMap.get(logicTableName).add(finalSequence);
                } else {
                    logicTable2SequenceMap.put(logicTableName, new HashSet<Integer>(16) {{
                        add(finalSequence);
                    }});
                }
                logicTableSet.add(logicTableName);
            }

            physicalDb2LogicTableMap.put(physicalDb, logicTableSet);
            physicalDb2LogicTable2SequenceMap.put(physicalDb, logicTable2SequenceMap);
        }

        // 然后校验这些元数据
        String firstPhysicalDb = null;
        Set<String> firstLogicTableSet = null;
        // 所有分库都有某个逻辑表
        for (String physicalDb : physicalDb2LogicTableMap.keySet()) {
            Set<String> logicTableSet = physicalDb2LogicTableMap.get(physicalDb);
            if (firstPhysicalDb == null) {
                firstPhysicalDb = physicalDb;
                firstLogicTableSet = logicTableSet;
            }
            //            } else {
            //                if (!logicTableSet.equals(firstLogicTableSet)) {
            //                    recordInspectIssue(MetadataInspectResult.builder()
            //                            .code("v2_logic_table_field_logic_table_not_in_every_db")
            //                            .logicDbId(logicDatabase.getLogicDbId().toString())
            //                            .logicDbName(logicDatabase.getLogicDbName())
            //                            .issue(String.format("不同物理库的逻辑表不同：physical db %s logic table set %s physical db %s logic table set %s", physicalDb, logicTableSet, firstPhysicalDb, firstLogicTableSet))
            //                    );
            //                    return false;
            //                }
            //            }
        }

        if (!logicDatabase.getIsShard()) {
            return true;
        }

        firstPhysicalDb = null;
        Map<String, Set<Integer>> firstLogicTable2Sequence = null;
        Map<String, Boolean> logicTable2IsEvery = new HashMap<>(16);
        // 第一遍遍历，确定好每个逻辑表是 every 还是 global
        for (String physicalDb : physicalDb2LogicTable2SequenceMap.keySet()) {
            Map<String, Set<Integer>> logicTable2SequenceMap = physicalDb2LogicTable2SequenceMap.get(physicalDb);
            if (firstPhysicalDb == null) {
                firstPhysicalDb = physicalDb;
                firstLogicTable2Sequence = logicTable2SequenceMap;
            } else {
                for (String logicTable : logicTable2SequenceMap.keySet()) {
                    Set<Integer> currentSequenceSet = logicTable2SequenceMap.get(logicTable);
                    Set<Integer> firstSequenceSet = firstLogicTable2Sequence.get(logicTable);
                    if (currentSequenceSet.equals(firstSequenceSet)) {
                        logicTable2IsEvery.put(logicTable, true);
                    } else {
                        logicTable2IsEvery.put(logicTable, false);
                    }
                }
                break;
            }
        }

        // 第二遍遍历，就校验序号是不是规范的 every/global
        for (String logicTable : logicTable2IsEvery.keySet()) {
            if (logicTable2IsEvery.get(logicTable)) {
                String lastPhysicalDb = null;
                Set<Integer> lastSequenceSet = null;
                for (String physicalDb : physicalDb2LogicTable2SequenceMap.keySet()) {
                    String instanceId = physicalDb2InstanceIdMap.get(physicalDb);
                    Map<String, Set<Integer>> logicTable2SequenceMap = physicalDb2LogicTable2SequenceMap.get(physicalDb);
                    Set<Integer> sequenceSet = logicTable2SequenceMap.get(logicTable);
                    if (lastSequenceSet != null) {
                        if (!lastSequenceSet.equals(sequenceSet)) {
                            recordInspectIssue(MetadataInspectResult.builder()
                                    .code("v2_logic_table_field_logic_table_sequence_not_meet_every_mode")
                                    .logicDbId(logicDatabase.getLogicDbId().toString())
                                    .logicDbName(logicDatabase.getLogicDbName())
                                    .instanceId(instanceId)
                                    .logicTableName(logicTable)
                                    .issue(String.format("逻辑表的序号不满足 every 规范，逻辑表 %s，物理库1 %s 序号1 %s 物理库2 %s 序号2 %s", logicTable, lastPhysicalDb, lastSequenceSet, physicalDb, sequenceSet))
                            );
                            return false;
                        }
                    }
                    lastPhysicalDb = physicalDb;
                    lastSequenceSet = sequenceSet;
                }
            } else {
                List<Integer> allSequenceList = new ArrayList<>(16);
                for (String physicalDb : physicalDb2LogicTable2SequenceMap.keySet()) {
                    Map<String, Set<Integer>> logicTable2SequenceMap = physicalDb2LogicTable2SequenceMap.get(physicalDb);
                    Set<Integer> sequenceSet = logicTable2SequenceMap.get(logicTable);
                    allSequenceList.addAll(sequenceSet);
                }
                allSequenceList.sort(Integer::compareTo);
                for (int i = 0; i < allSequenceList.size(); i++) {
                    if (i != allSequenceList.get(i)) {
                        recordInspectIssue(MetadataInspectResult.builder()
                                .code("v2_logic_table_field_logic_table_sequence_not_meet_global_mode")
                                .logicDbId(logicDatabase.getLogicDbId().toString())
                                .logicDbName(logicDatabase.getLogicDbName())
                                .logicTableName(logicTable)
                                .issue(String.format("逻辑表的序号不满足 global 规范，逻辑表 %s，序号 %s", logicTable, allSequenceList))
                        );
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public List<String> getLogicTableListByPhyTableList(List<String> phyTableList) {
        Set<String> logicTableNameSet = new HashSet<>(10);
        List<String> finalResult = new ArrayList<>(10);
        for (String phyTable : phyTableList) {
            Pair<String, Integer> nameAndSequence = StringExtractUtil.extractLogicNameAndSequenceByCharTraverse(phyTable);
            if (nameAndSequence.getRight().equals(-1)) {
                finalResult.add(phyTable);
            } else {
                String logicTable = nameAndSequence.getLeft();
                logicTableNameSet.add(logicTable);
            }
        }
        finalResult.addAll(new ArrayList<>(logicTableNameSet));
        return finalResult;
    }

    public void logicTableAndFieldFix(LogicMetadataCheckReq logicMetadataCheckReq) {
        log.info("logicTableAndFieldFix start {}", logicMetadataCheckReq);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_logic_table_field_check_start_msg")
                .issue(String.format("v2_check_start_msg %s", logicMetadataCheckReq)));
        LocalDateTime start = LocalDateTime.now().minusSeconds(2);
        try {
            List<LogicDatabase> logicDatabases;
            switch (logicMetadataCheckReq.getCheckType()) {
                case "all":
                    logicDatabases = logicDatabaseMapper.selectAll();
                    break;
                case "special":
                    List<Long> logicDbIds = logicDatabaseMapper.selectLogicDbIdsByUniqueDbNames(logicMetadataCheckReq.getUniqueDbNames());
                    logicDbIds.addAll(logicMetadataCheckReq.getLogicDbIds() == null ? new ArrayList<>() : logicMetadataCheckReq.getLogicDbIds());
                    logicDatabases = logicDatabaseMapper.selectByLogicDbIds(logicDbIds);
                    break;
                default:
                    log.error("logicTableAndFieldFix type {} not support", logicMetadataCheckReq.getCheckType());
                    return;
            }

            log.info("Total logicTableAndFieldFix to check: {}", logicDatabases.size());

            Set<Long> logicDbIdWhiteList = LeoUtils.getJsonProperty("mountain-v2-api.logic_db_id_check_white_list", new TypeReference<Set<Long>>() {
            });
            // 修正任务，串行执行，避免把 mountain 库负载打高
            for (int i = 0; i < logicDatabases.size(); i++) {
                log.info("logicTableAndFieldFix logicDbId {} start ({}/{})", logicDatabases.get(i).getLogicDbId(), i + 1, logicDatabases.size());
                LogicDatabase logicDatabase = logicDatabases.get(i);
                if (logicDbIdWhiteList.contains(logicDatabase.getLogicDbId())) {
                    log.info("logicDbId {} in white list, skip fix", logicDatabase.getLogicDbId());
                    continue;
                }
                fixLogicTableAndFieldSub(logicDatabase);
                log.info("logicTableAndFieldFix logicDbId {} end ({}/{})", logicDatabase.getLogicDbId(), i + 1, logicDatabases.size());
            }
        } catch (Exception e) {
            log.error("logicTableAndFieldFix error", e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_check_exception")
                    .issue(String.format("v2_check_exception of logicTableAndFieldFix %s", e.getMessage())));
        }
        log.info("logicTableAndFieldFix end {}", logicMetadataCheckReq);
        recordInspectIssue(MetadataInspectResult.builder()
                .code("v2_logic_table_field_check_end_msg")
                .issue(String.format("v2_check_end_msg %s", logicMetadataCheckReq)));
        checkLogicTableAndFieldFixResultAndSendInform(start, logicMetadataCheckReq);
    }

    private void fixLogicTableAndFieldSub(LogicDatabase logicDatabase) {
        try {
            // 先巡检一下逻辑库，如果逻辑库的元数据都不对，就不能修正下面的逻辑表和逻辑字段了
            boolean logicDatabaseCheckResult = checkLogicDatabaseSub(logicDatabase);
            if (!logicDatabaseCheckResult) {
                log.error("fixLogicTableAndFieldSub logicDatabaseCheckResult false, will not fix logic_table logic_field, logicDbId: {}", logicDatabase.getLogicDbId());
                return;
            }
            log.info("fixLogicTableAndFieldSub logicDatabaseCheckResult success, start parse LogicDatabaseLogicTableInfoDTO of {} {}", logicDatabase.getLogicDbId(), logicDatabase);

            log.info("fixLogicTableAndFieldSub start {} {}", logicDatabase.getLogicDbId(), logicDatabase);
            // v2 元数据
            Cluster cluster = clusterMapper.selectOneByClusterId(logicDatabase.getClusterId());
            List<PhysicalCluster> physicalClusterList = physicalClusterMapper.selectByClusterId(cluster.getClusterId());
            List<String> instanceIdList = phyClusterInstanceRelationMapper.selectInstanceIdsByPhysicalClusterId(physicalClusterList.get(0).getPhysicalClusterId());
            List<Instance> instanceList = instanceMapper.selectByInstanceIdList(instanceIdList);
            List<Instance> anotherInstanceList = new ArrayList<>();
            if (cluster.getType().equals(Cluster.ROLE_MULTI_ACTIVE)) {
                List<String> anotherInstanceIdList = phyClusterInstanceRelationMapper.selectInstanceIdsByPhysicalClusterId(physicalClusterList.get(1).getPhysicalClusterId());
                anotherInstanceList = instanceMapper.selectByInstanceIdList(anotherInstanceIdList);
            }
            List<InstanceDbInfo> instanceDbInfoList = instanceDbInfoMapper.selectInstanceDbInfoByLogicDbId(logicDatabase.getLogicDbId());

            // 【1】实时查询后解析到的元数据
            LdLtLfInfoDTO ldLtLfInfo = getLdLtLfInfoDTO(logicDatabase, cluster, physicalClusterList, instanceList, anotherInstanceList, instanceDbInfoList);

            // 【2】db 中实际存储的元数据
            List<LogicTable> logicTableList = logicTableMapper.selectByLogicDbId(logicDatabase.getLogicDbId());
            Map<Long, List<LogicField>> ltId2LfMap = new HashMap<>(16);
            Map<Long, List<InstanceTableInfo>> ltId2ItiListMap = new HashMap<>(16);
            // 每次 100 个 logic_table 查询，避免查询太猛把 mountain 库 cpu 打得太高
            for (int i = 0; i < logicTableList.size(); i += 100) {
                List<LogicTable> logicTableListBatch = logicTableList.subList(i, Math.min(i + 100, logicTableList.size()));
                List<Long> logicTableIdListBatch = logicTableListBatch.stream().map(LogicTable::getLogicTableId).collect(Collectors.toList());
                List<LogicField> logicFieldList = logicFieldMapper.selectByLogicTableIds(logicTableIdListBatch);
                for (LogicField logicField : logicFieldList) {
                    if (!ltId2LfMap.containsKey(logicField.getLogicTableId())) {
                        ltId2LfMap.put(logicField.getLogicTableId(), new ArrayList<>());
                    }
                    ltId2LfMap.get(logicField.getLogicTableId()).add(logicField);
                }

                List<InstanceTableInfo> itiList = instanceTableInfoMapper.selectByLogicTableIds(logicTableIdListBatch);
                for (InstanceTableInfo iti : itiList) {
                    if (!ltId2ItiListMap.containsKey(iti.getLogicTableId())) {
                        ltId2ItiListMap.put(iti.getLogicTableId(), new ArrayList<>());
                    }
                    ltId2ItiListMap.get(iti.getLogicTableId()).add(iti);
                }

                Thread.sleep(100);
            }

            LdLtLfInfoDTO ldLtLfInfoDTOOfDb = new LdLtLfInfoDTO(logicDatabase, logicTableList, ltId2LfMap, ltId2ItiListMap);

            // 开始对比和修正【1】【2】中的数据
            checkAndFixLdLtLfInfo(ldLtLfInfo, ldLtLfInfoDTOOfDb, logicDatabase, cluster, physicalClusterList, instanceList, anotherInstanceList);

            log.info("fixLogicTableAndFieldSub end {} {}", logicDatabase.getLogicDbId(), logicDatabase);
        } catch (Exception e) {
            log.error("fixLogicTableAndFieldSub error, logicDbId: {}", logicDatabase.getLogicDbId(), e);
            recordInspectIssue(MetadataInspectResult.builder()
                    .code("v2_logic_table_field_sub_check_exception")
                    .logicDbId(logicDatabase.getLogicDbId().toString())
                    .issue(String.format("v2 check exception of fixLogicTableAndFieldSub: %s", e.getMessage())));
        }
    }

    private LdLtLfInfoDTO getLdLtLfInfoDTO(LogicDatabase logicDatabase, Cluster cluster, List<PhysicalCluster> physicalClusterList,
                                           List<Instance> instanceList, List<Instance> anotherInstanceList, List<InstanceDbInfo> instanceDbInfoList) {
        // 万丈高楼平地起，函数只能靠自己
        if (logicDatabase.getIsShard()) {
            return getLdLtLfInfoDTOOfShard(logicDatabase, cluster, physicalClusterList, instanceList, anotherInstanceList, instanceDbInfoList);
        } else {
            return getLdLtLfInfoDTOOfSingle(logicDatabase, cluster, physicalClusterList, instanceList, anotherInstanceList, instanceDbInfoList);
        }
    }

    private LdLtLfInfoDTO getLdLtLfInfoDTOOfShard(LogicDatabase logicDatabase, Cluster cluster, List<PhysicalCluster> physicalClusterList,
                                                  List<Instance> instanceList, List<Instance> anotherInstanceList, List<InstanceDbInfo> instanceDbInfoList) {
        // 处理分库分表的逻辑
        LdLtLfInfoDTO ldLtI = new LdLtLfInfoDTO(logicDatabase);

        List<LdLtLfInfoDTO.LtLfInfoDTO> ltLfInfoDTOS = new ArrayList<>(10);

        // 维护出来各种 map 关系，含义见变量名称
        Map<String, List<InstanceDbInfo>> physicalDb2InstanceDbInfoMap = instanceDbInfoList.stream().collect(Collectors.groupingBy(InstanceDbInfo::getDbName));
        Map<String, String> physicalDb2InstanceIdMap = new HashMap<>(16);
        Map<String, List<PhysicalTableInfo>> logicTable2PhysicalTableInfoListMap = new HashMap<>(16);
        List<Instance> instanceAll = new ArrayList<>(instanceList);
        instanceAll.addAll(anotherInstanceList);
        for (Instance instance : instanceAll) {
            List<String> physicalDbByLogicDb = InformationSchemaUtil.getPhysicalDbByLogicDb(instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName());
            physicalDbByLogicDb.sort(String::compareTo);

            Pair<String, Integer> logicNameAndSequence = StringExtractUtil.extractLogicNameAndSequenceByCharTraverse(physicalDbByLogicDb.get(0));
            // 如果有多个物理分库，第一个物理分库是个单库，那么移除它
            // eg: mountain, mountain_1, mountain_10, mountain_100
            if (physicalDbByLogicDb.size() > 1 && logicNameAndSequence.getRight() == -1) {
                physicalDbByLogicDb = physicalDbByLogicDb.subList(1, physicalDbByLogicDb.size());
            } else if (physicalDbByLogicDb.size() > 1 && logicNameAndSequence.getRight() != -1) {
                // 特别地，下面这种需要结合 core_cdb_shard 判断（因为那个单库竟然以数字结尾），第一个物理分库的逻辑库名解析为 night_guard 然后匹配不到 shard，那么也移除它
                // eg: night_guard1, night_guard1_0, night_guard1_1, night_guard1_10
                CoreCdbShard coreCdbShard = coreCdbShardMapper.selectOneByInstanceIdAndLogicDbName(instance.getInstanceId(), logicNameAndSequence.getLeft());
                if (coreCdbShard == null) {
                    physicalDbByLogicDb = physicalDbByLogicDb.subList(1, physicalDbByLogicDb.size());
                }
            }

            // 保存下来映射关系
            for (String physicalDb : physicalDbByLogicDb) {
                // 多活的时候会出现覆盖，但是不影响，后面的逻辑只是用到了这个 map 的 size
                physicalDb2InstanceIdMap.put(physicalDb, instance.getInstanceId());

                List<String> physicalTableList = InformationSchemaUtil.getAllBusinessPhyTablesByIpPortAndPhyDatabase(instance.getIp(), instance.getPort(), physicalDb);

                for (String physicalTable : physicalTableList) {
                    PhysicalTableInfo physicalTableInfo = PhysicalTableInfo.parse(instance.getInstanceId(), instance.getIp(), instance.getPort(), physicalDb, physicalTable);

                    if (!logicTable2PhysicalTableInfoListMap.containsKey(physicalTableInfo.getLogicTableName())) {
                        logicTable2PhysicalTableInfoListMap.put(physicalTableInfo.getLogicTableName(), new ArrayList<>());
                    }
                    logicTable2PhysicalTableInfoListMap.get(physicalTableInfo.getLogicTableName()).add(physicalTableInfo);
                }
            }
        }

        for (Map.Entry<String, List<PhysicalTableInfo>> entry : logicTable2PhysicalTableInfoListMap.entrySet()) {
            String logicTableName = entry.getKey();
            List<PhysicalTableInfo> physicalTableInfoList = entry.getValue();
            List<Integer> sequenceList = physicalTableInfoList.stream().map(PhysicalTableInfo::getSequence).sorted(Integer::compareTo).collect(Collectors.toList());

            // 分库分表和分库单表共存，eg: -1 -1 -1 -1 0 1 2 3，拆分两部分处理
            if (sequenceList.size() > 1 && sequenceList.get(0) == -1 && sequenceList.get(sequenceList.size() - 1) != -1) {
                List<PhysicalTableInfo> singlePhysicalTableInfoList = physicalTableInfoList.stream().filter(x -> x.getSequence() == -1).collect(Collectors.toList());
                List<Integer> singleSequenceList = sequenceList.stream().filter(x -> x == -1).collect(Collectors.toList());
                getLtLfInfoDTOOfShard(logicDatabase, physicalDb2InstanceIdMap, logicTableName, physicalDb2InstanceDbInfoMap, singleSequenceList, singlePhysicalTableInfoList, ltLfInfoDTOS);

                List<PhysicalTableInfo> shardPhysicalTableInfoList = physicalTableInfoList.stream().filter(x -> x.getSequence() != -1).collect(Collectors.toList());
                List<Integer> shardSequenceList = sequenceList.stream().filter(x -> x != -1).collect(Collectors.toList());
                getLtLfInfoDTOOfShard(logicDatabase, physicalDb2InstanceIdMap, logicTableName, physicalDb2InstanceDbInfoMap, shardSequenceList, shardPhysicalTableInfoList, ltLfInfoDTOS);
            } else {
                getLtLfInfoDTOOfShard(logicDatabase, physicalDb2InstanceIdMap, logicTableName, physicalDb2InstanceDbInfoMap, sequenceList, physicalTableInfoList, ltLfInfoDTOS);
            }
        }

        ldLtI.setLogicTableList(ltLfInfoDTOS);
        return ldLtI;
    }

    private void getLtLfInfoDTOOfShard(LogicDatabase logicDatabase, Map<String, String> physicalDb2InstanceIdMap, String logicTableName, Map<String, List<InstanceDbInfo>> physicalDb2InstanceDbInfoMap, List<Integer> sequenceList, List<PhysicalTableInfo> physicalTableInfoList, List<LdLtLfInfoDTO.LtLfInfoDTO> ltLfInfoDTOS) {
        // 标准的分库分表，eg: 0 1 2 3 (test_table_0 test_table_1 test_table_2 test_table_3)
        if (sequenceList.size() > 1 && sequenceList.get(0) == 0 && sequenceList.size() % physicalDb2InstanceIdMap.size() == 0) {
            LdLtLfInfoDTO.LtLfInfoDTO ltLfInfoDTO = new LdLtLfInfoDTO.LtLfInfoDTO();
            ltLfInfoDTO.setLogicTableName(logicTableName);
            ltLfInfoDTO.setLogicDbId(logicDatabase.getLogicDbId());
            ltLfInfoDTO.setClusterId(logicDatabase.getClusterId());
            ltLfInfoDTO.setTableSuffixType(physicalDb2InstanceIdMap.size() > 1 && new HashSet<>(sequenceList).size() == sequenceList.size() ? 1 : 2);
            ltLfInfoDTO.setTableNum(sequenceList.size());
            ltLfInfoDTO.setStart(sequenceList.get(0));
            ltLfInfoDTO.setEnd(sequenceList.get(sequenceList.size() - 1));
            PhysicalTableInfo physicalTableInfo = physicalTableInfoList.get(0);
            int underlineFlag = physicalTableInfo.getPhysicalTableName().replaceAll(logicTableName, "").contains("_") ? 1 : 0;
            ltLfInfoDTO.setUnderlineFlag(underlineFlag);
            ltLfInfoDTO.setStep(sequenceList.get(1) - sequenceList.get(0));
            ltLfInfoDTO.setIsShard(true);
            String createTableSql = InformationSchemaUtil.getCreateTableSql(physicalTableInfo.getIp(), physicalTableInfo.getPort(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName());
            ltLfInfoDTO.setTableSql(createTableSql);
            PddLogicTable pddLogicTable = pddLogicMetadataService.getPddLogicTableByInstanceDatabaseTable(physicalTableInfo.getInstanceId(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName(), false);
            ltLfInfoDTO.setAuditCreatedAt(pddLogicTable != null ? pddLogicTable.getAuditCreatedAt() : "");
            ltLfInfoDTO.setAuditUpdatedAt(pddLogicTable != null ? pddLogicTable.getAuditUpdatedAt() : "");
            ltLfInfoDTO.setTableComment(SqlUtil.getTableCommentFromCreateTableSql(createTableSql));

            List<LogicField> logicFieldList = getLogicFieldList(physicalTableInfo.getInstanceId(), physicalTableInfo.getIp(), physicalTableInfo.getPort(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName());
            ltLfInfoDTO.setLogicFieldList(logicFieldList);

            List<InstanceTableInfo> instanceTableInfoList = getInstanceTableInfoList(physicalDb2InstanceDbInfoMap, physicalTableInfoList);
            ltLfInfoDTO.setInstanceTableInfoList(instanceTableInfoList);

            ltLfInfoDTOS.add(ltLfInfoDTO);
        }

        // 标准的分库单表，eg: -1 -1 -1 -1 (test_table test_table test_table test_table)
        if (sequenceList.size() > 1 && CollectionUtil.isAllOneValue(sequenceList, -1) && sequenceList.size() == physicalDb2InstanceIdMap.size()) {
            LdLtLfInfoDTO.LtLfInfoDTO ltLfInfoDTO = new LdLtLfInfoDTO.LtLfInfoDTO();
            ltLfInfoDTO.setLogicTableName(logicTableName);
            ltLfInfoDTO.setLogicDbId(logicDatabase.getLogicDbId());
            ltLfInfoDTO.setClusterId(logicDatabase.getClusterId());
            ltLfInfoDTO.setTableSuffixType(0);
            ltLfInfoDTO.setTableNum(sequenceList.size());
            ltLfInfoDTO.setStart(0);
            ltLfInfoDTO.setEnd(0);
            ltLfInfoDTO.setUnderlineFlag(0);
            ltLfInfoDTO.setStep(0);
            ltLfInfoDTO.setIsShard(true);
            PhysicalTableInfo physicalTableInfo = physicalTableInfoList.get(0);
            String createTableSql = InformationSchemaUtil.getCreateTableSql(physicalTableInfo.getIp(), physicalTableInfo.getPort(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName());
            ltLfInfoDTO.setTableSql(createTableSql);
            PddLogicTable pddLogicTable = pddLogicMetadataService.getPddLogicTableByInstanceDatabaseTable(physicalTableInfo.getInstanceId(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName(), false);
            ltLfInfoDTO.setAuditCreatedAt(pddLogicTable != null ? pddLogicTable.getAuditCreatedAt() : "");
            ltLfInfoDTO.setAuditUpdatedAt(pddLogicTable != null ? pddLogicTable.getAuditUpdatedAt() : "");
            ltLfInfoDTO.setTableComment(SqlUtil.getTableCommentFromCreateTableSql(createTableSql));

            List<LogicField> logicFieldList = getLogicFieldList(physicalTableInfo.getInstanceId(), physicalTableInfo.getIp(), physicalTableInfo.getPort(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName());
            ltLfInfoDTO.setLogicFieldList(logicFieldList);

            List<InstanceTableInfo> instanceTableInfoList = getInstanceTableInfoList(physicalDb2InstanceDbInfoMap, physicalTableInfoList);
            ltLfInfoDTO.setInstanceTableInfoList(instanceTableInfoList);

            ltLfInfoDTOS.add(ltLfInfoDTO);
        }

        // 特殊分库单表：单库shard中的单表、不在所有分库中，都当做 is_shard = false 的逻辑表处理
        if (sequenceList.size() == 1 || (sequenceList.size() % physicalDb2InstanceIdMap.size() != 0) || sequenceList.get(0) != 0) {
            for (PhysicalTableInfo physicalTableInfo : physicalTableInfoList) {
                LdLtLfInfoDTO.LtLfInfoDTO ltLfInfoDTO = new LdLtLfInfoDTO.LtLfInfoDTO();
                ltLfInfoDTO.setLogicTableName(physicalTableInfo.getPhysicalTableName());
                ltLfInfoDTO.setLogicDbId(logicDatabase.getLogicDbId());
                ltLfInfoDTO.setClusterId(logicDatabase.getClusterId());
                ltLfInfoDTO.setTableSuffixType(0);
                ltLfInfoDTO.setTableNum(1);
                ltLfInfoDTO.setStart(0);
                ltLfInfoDTO.setEnd(0);
                ltLfInfoDTO.setUnderlineFlag(0);
                ltLfInfoDTO.setStep(0);
                ltLfInfoDTO.setIsShard(false);
                String createTableSql = InformationSchemaUtil.getCreateTableSql(physicalTableInfo.getIp(), physicalTableInfo.getPort(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName());
                ltLfInfoDTO.setTableSql(createTableSql);
                PddLogicTable pddLogicTable = pddLogicMetadataService.getPddLogicTableByInstanceDatabaseTable(physicalTableInfo.getInstanceId(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName(), false);
                ltLfInfoDTO.setAuditCreatedAt(pddLogicTable != null ? pddLogicTable.getAuditCreatedAt() : "");
                ltLfInfoDTO.setAuditUpdatedAt(pddLogicTable != null ? pddLogicTable.getAuditUpdatedAt() : "");
                ltLfInfoDTO.setTableComment(SqlUtil.getTableCommentFromCreateTableSql(createTableSql));

                List<LogicField> logicFieldList = getLogicFieldList(physicalTableInfo.getInstanceId(), physicalTableInfo.getIp(), physicalTableInfo.getPort(), physicalTableInfo.getPhysicalDbName(), physicalTableInfo.getPhysicalTableName());
                ltLfInfoDTO.setLogicFieldList(logicFieldList);

                List<InstanceTableInfo> instanceTableInfoList = getInstanceTableInfoList(physicalDb2InstanceDbInfoMap, Collections.singletonList(physicalTableInfo));
                ltLfInfoDTO.setInstanceTableInfoList(instanceTableInfoList);

                ltLfInfoDTOS.add(ltLfInfoDTO);
            }
        }
    }

    private List<InstanceTableInfo> getInstanceTableInfoList(Map<String, List<InstanceDbInfo>> physicalDb2InstanceDbInfoMap, List<PhysicalTableInfo> physicalTableInfoList) {
        List<InstanceTableInfo> instanceTableInfoList = new ArrayList<>(10);
        for (PhysicalTableInfo physicalTableInfo : physicalTableInfoList) {
            if (!physicalDb2InstanceDbInfoMap.containsKey(physicalTableInfo.getPhysicalDbName())) {
                continue;
            }
            for (InstanceDbInfo instanceDbInfo : physicalDb2InstanceDbInfoMap.get(physicalTableInfo.getPhysicalDbName())) {
                if (!instanceDbInfo.getInstanceId().equals(physicalTableInfo.getInstanceId())) {
                    continue;
                }
                InstanceTableInfo instanceTableInfo = InstanceTableInfo.builder()
                        .instanceDbId(instanceDbInfo.getInstanceDbId())
                        // 校验时再根据库中是否有了这个逻辑表，决定使用库中的，还是重新生成新的
                        .logicTableId(0L)
                        .instanceId(physicalTableInfo.getInstanceId())
                        .dbName(physicalTableInfo.getPhysicalDbName())
                        .tableName(physicalTableInfo.getPhysicalTableName())
                        .tableVersion(1)
                        .build();
                instanceTableInfoList.add(instanceTableInfo);
            }
        }
        return instanceTableInfoList;
    }

    private LdLtLfInfoDTO getLdLtLfInfoDTOOfSingle(LogicDatabase logicDatabase, Cluster cluster, List<PhysicalCluster> physicalClusterList,
                                                   List<Instance> instanceList, List<Instance> anotherInstanceList, List<InstanceDbInfo> instanceDbInfoList) {
        // 处理单库的逻辑
        LdLtLfInfoDTO ldLtI = new LdLtLfInfoDTO(logicDatabase);

        Instance instance = instanceList.get(0);
        Map<String, InstanceDbInfo> insanceId2InstanceDbInfoMap = instanceDbInfoList.stream().collect(Collectors.toMap(InstanceDbInfo::getInstanceId, instanceDbInfo -> instanceDbInfo));

        List<String> tables = InformationSchemaUtil.getAllBusinessPhyTablesByIpPortAndPhyDatabase(instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName());
        List<LdLtLfInfoDTO.LtLfInfoDTO> ltLfInfoDTOS = new ArrayList<>(tables.size());
        for (String table : tables) {
            LdLtLfInfoDTO.LtLfInfoDTO ltLfInfoDTO = new LdLtLfInfoDTO.LtLfInfoDTO();
            ltLfInfoDTO.setLogicTableName(table);
            ltLfInfoDTO.setLogicDbId(logicDatabase.getLogicDbId());
            ltLfInfoDTO.setClusterId(cluster.getClusterId());
            ltLfInfoDTO.setTableSuffixType(0);
            ltLfInfoDTO.setTableNum(1);
            ltLfInfoDTO.setStart(0);
            ltLfInfoDTO.setEnd(0);
            ltLfInfoDTO.setUnderlineFlag(0);
            ltLfInfoDTO.setStep(0);
            ltLfInfoDTO.setIsShard(false);
            String createTableSql = InformationSchemaUtil.getCreateTableSql(instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName(), table);
            ltLfInfoDTO.setTableSql(createTableSql);
            PddLogicTable pddLogicTable = pddLogicMetadataService.getPddLogicTableByInstanceDatabaseTable(instance.getInstanceId(), logicDatabase.getLogicDbName(), table, false);
            ltLfInfoDTO.setAuditCreatedAt(pddLogicTable != null ? pddLogicTable.getAuditCreatedAt() : "");
            ltLfInfoDTO.setAuditUpdatedAt(pddLogicTable != null ? pddLogicTable.getAuditUpdatedAt() : "");
            ltLfInfoDTO.setTableComment(SqlUtil.getTableCommentFromCreateTableSql(createTableSql));

            List<LogicField> logicFieldList = getLogicFieldList(instance.getInstanceId(), instance.getIp(), instance.getPort(), logicDatabase.getLogicDbName(), table);
            ltLfInfoDTO.setLogicFieldList(logicFieldList);

            List<InstanceTableInfo> instanceTableInfoList = new ArrayList<>();
            instanceTableInfoList.add(InstanceTableInfo.builder()
                    .instanceDbId(insanceId2InstanceDbInfoMap.get(instance.getInstanceId()).getInstanceDbId())
                    // 校验时再根据库中是否有了这个逻辑表，决定使用库中的，还是重新生成新的
                    .logicTableId(0L)
                    .instanceId(instance.getInstanceId())
                    .dbName(logicDatabase.getLogicDbName())
                    .tableName(table)
                    .tableVersion(1)
                    .build());
            if (anotherInstanceList != null && !anotherInstanceList.isEmpty()) {
                instanceTableInfoList.add(InstanceTableInfo.builder()
                        .instanceDbId(insanceId2InstanceDbInfoMap.get(anotherInstanceList.get(0).getInstanceId()).getInstanceDbId())
                        // 校验时再根据库中是否有了这个逻辑表，决定使用库中的，还是重新生成新的
                        .logicTableId(0L)
                        .instanceId(anotherInstanceList.get(0).getInstanceId())
                        .dbName(logicDatabase.getLogicDbName())
                        .tableName(table)
                        .tableVersion(1)
                        .build());
            }
            ltLfInfoDTO.setInstanceTableInfoList(instanceTableInfoList);

            ltLfInfoDTOS.add(ltLfInfoDTO);
        }

        ldLtI.setLogicTableList(ltLfInfoDTOS);
        return ldLtI;
    }

    private List<LogicField> getLogicFieldList(String instanceId, String ip, int port, String physicalDb, String physicalTable) {
        List<Columns> columnsList = InformationSchemaUtil.getSortedColumnsListByPhysical(ip, port, physicalDb, physicalTable);

        List<LogicField> logicFieldList = new ArrayList<>(columnsList.size());
        for (Columns columns : columnsList) {
            CdbField cdbField = cdbFieldMapper.selectByCdbIdAndDbnameAndTbnameAndFieldname(instanceId, physicalDb, physicalTable, columns.getColumnName());
            CoreCdbEncryption coreCdbEncryption = coreCdbEncryptionMapper.selectByCdbIdAndDbnameAndTbnameAndField(instanceId, physicalDb, physicalTable, columns.getColumnName());

            logicFieldList.add(LogicField.builder()
                    .logicFieldName(columns.getColumnName())
                    // 校验时再根据库中是否有了这个逻辑表，决定使用库中的，还是重新生成新的
                    .logicTableId(0L)
                    .fieldType(columns.getColumnType())
                    .fieldComment(columns.getColumnComment())
                    .level(cdbField == null ? 0 : cdbField.getLevel().intValue())
                    .securityKey(coreCdbEncryption == null ? "" : coreCdbEncryption.getSecuritykey())
                    .securityPlatform(coreCdbEncryption == null ? "" : coreCdbEncryption.getBusiness())
                    .build());
        }

        return logicFieldList;
    }

    private void checkAndFixLdLtLfInfo(LdLtLfInfoDTO ldLtLfInfo, LdLtLfInfoDTO ldLtLfInfoOfDb, LogicDatabase logicDatabase, Cluster cluster, List<PhysicalCluster> physicalClusterList, List<Instance> instanceList, List<Instance> anotherInstanceList) {
        log.info("begin checkAndFixLdLtLfInfo logic_db_id {} real query {} db {}", logicDatabase.getLogicDbId(), ldLtLfInfo.getLogicTableList().size(), ldLtLfInfoOfDb.getLogicTableList().size());
        // 1、遍历实际查询到的逻辑表，db 中缺少的进行补全，和 db 中不一致的进行修正
        for (LdLtLfInfoDTO.LtLfInfoDTO ltInfo : ldLtLfInfo.getLogicTableList()) {
            boolean contains = false;
            for (LdLtLfInfoDTO.LtLfInfoDTO ltInfoOfDb : ldLtLfInfoOfDb.getLogicTableList()) {
                // 根据唯一键找到了对应的逻辑表，检查其他信息是否不一致，存在不一致的话 update 修正
                // uk_logic_db_id_logic_table_name_is_shard_start_end
                // 两个的 logic_db_id 肯定是相等的
                if (ltInfo.getLogicTableName().equals(ltInfoOfDb.getLogicTableName())
                        && ltInfo.getIsShard().equals(ltInfoOfDb.getIsShard())
                        && ltInfo.getStart().equals(ltInfoOfDb.getStart())
                        && ltInfo.getEnd().equals(ltInfoOfDb.getEnd())) {
                    contains = true;

                    // 使用库中的已有的 logic_table_id 赋值到 ltInfo 中，后面校验修正 instance_table_info 时使用
                    ltInfo.setLogicTableId(ltInfoOfDb.getLogicTableId());
                    for (InstanceTableInfo iti : ltInfo.getInstanceTableInfoList()) {
                        iti.setLogicTableId(ltInfoOfDb.getLogicTableId());
                    }
                    for (LogicField lf : ltInfo.getLogicFieldList()) {
                        lf.setLogicTableId(ltInfoOfDb.getLogicTableId());
                    }

                    if (!ltInfo.getTableSuffixType().equals(ltInfoOfDb.getTableSuffixType())
                            || !ltInfo.getTableNum().equals(ltInfoOfDb.getTableNum())
                            || !ltInfo.getUnderlineFlag().equals(ltInfoOfDb.getUnderlineFlag())
                            || !ltInfo.getStep().equals(ltInfoOfDb.getStep())
                            || !ltInfo.getTableSql().equals(ltInfoOfDb.getTableSql())
                            || !ltInfo.getTableComment().equals(ltInfoOfDb.getTableComment())
                            || !ltInfo.getAuditCreatedAt().equals(ltInfoOfDb.getAuditCreatedAt())
                            || !ltInfo.getAuditUpdatedAt().equals(ltInfoOfDb.getAuditUpdatedAt())
                    ) {
                        log.warn("update logic table {}/{} in logic db {}/{} has inconsistent metadata, db metadata: {}, real query metadata: {}",
                                ltInfoOfDb.getLogicTableId(), ltInfoOfDb.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), ltInfoOfDb, ltInfo);
                        logicTableMapper.updateMainFieldByLogicTableId(ltInfoOfDb.getLogicTableId(), ltInfo.getTableSuffixType(), ltInfo.getTableNum(),
                                ltInfo.getUnderlineFlag(), ltInfo.getStep(), ltInfo.getTableSql(), ltInfo.getTableComment(),
                                ltInfo.getAuditCreatedAt(), ltInfo.getAuditUpdatedAt());
                    }
                    checkAndFixLfInfo(ltInfo.getLogicFieldList(), ltInfoOfDb.getLogicFieldList(), logicDatabase, ltInfoOfDb, cluster, physicalClusterList, instanceList, anotherInstanceList);
                    checkAndFixInstanceTableInfo(ltInfo.getInstanceTableInfoList(), ltInfoOfDb.getInstanceTableInfoList(), logicDatabase, ltInfoOfDb, cluster, physicalClusterList, instanceList, anotherInstanceList);
                }
            }
            // 生成新的 logic_table logic_field instance_table_info 元数据
            if (!contains) {
                completeLogicTableLogicFieldAndInstanceTableInfo(ltInfo, logicDatabase, cluster, physicalClusterList, instanceList, anotherInstanceList);
            }
        }

        // 2、遍历 db 中查询到的逻辑表，在实际查询到的逻辑表中不存在进行删除
        for (LdLtLfInfoDTO.LtLfInfoDTO ltInfoOfDb : ldLtLfInfoOfDb.getLogicTableList()) {
            boolean contains = false;
            for (LdLtLfInfoDTO.LtLfInfoDTO ltInfo : ldLtLfInfo.getLogicTableList()) {
                if (ltInfo.getLogicTableName().equals(ltInfoOfDb.getLogicTableName())
                        && ltInfo.getIsShard().equals(ltInfoOfDb.getIsShard())
                        && ltInfo.getStart().equals(ltInfoOfDb.getStart())
                        && ltInfo.getEnd().equals(ltInfoOfDb.getEnd())) {
                    contains = true;
                    break;
                }
            }
            if (!contains) {
                log.warn("delete logic table {}/{} in logic db {}/{} not found in real query metadata {}, will delete it",
                        ltInfoOfDb.getLogicTableId(), ltInfoOfDb.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), ldLtLfInfoOfDb);
                logicTableMapper.deleteByLogicTableId(ltInfoOfDb.getLogicTableId());
                logicFieldMapper.deleteByLogicTableId(ltInfoOfDb.getLogicTableId());
                instanceTableInfoMapper.deleteByLogicTableId(ltInfoOfDb.getLogicTableId());
            }
        }
    }

    private void checkAndFixLfInfo(List<LogicField> logicFieldList, List<LogicField> logicFieldListOfDb, LogicDatabase logicDatabase, LogicTable logicTable, Cluster cluster, List<PhysicalCluster> physicalClusterList, List<Instance> instanceList, List<Instance> anotherInstanceList) {
        // 【1】遍历实际查询到的逻辑字段，db 中缺少的进行补全，和 db 中不一致的进行修正
        for (LogicField logicField : logicFieldList) {
            boolean contains = false;
            for (LogicField logicFieldOfDb : logicFieldListOfDb) {
                // 根据唯一键找到了对应的逻辑字段，检查其他信息是否不一致，存在不一致的话 update 修正
                // uk_logic_table_id_logic_field_name
                if (logicField.getLogicTableId().equals(logicFieldOfDb.getLogicTableId())
                        && logicField.getLogicFieldName().equals(logicFieldOfDb.getLogicFieldName())) {
                    contains = true;
                    if (!logicField.getFieldType().equals(logicFieldOfDb.getFieldType())
                            || !logicField.getFieldComment().equals(logicFieldOfDb.getFieldComment())
                            || !logicField.getLevel().equals(logicFieldOfDb.getLevel())
                            || !logicField.getSecurityKey().equals(logicFieldOfDb.getSecurityKey())
                            || !logicField.getSecurityPlatform().equals(logicFieldOfDb.getSecurityPlatform())) {
                        log.warn("update logic field {}/{} in logic table {}/{} in logic db {}/{} has inconsistent metadata, db metadata: {}, real query metadata: {}",
                                logicFieldOfDb.getLogicFieldId(), logicFieldOfDb.getLogicFieldName(), logicTable.getLogicTableId(), logicTable.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), logicFieldOfDb, logicField);
                        logicFieldMapper.updateMainFieldsByLogicFieldId(logicFieldOfDb.getLogicFieldId(),
                                logicField.getFieldType(), logicField.getFieldComment(), logicField.getLevel(), logicField.getSecurityKey(), logicField.getSecurityPlatform());
                    }
                }
            }
            // 生成新的逻辑字段元数据
            if (!contains) {
                log.warn("insert logic field {}/{} in logic table {}/{} in logic db {}/{}, real query metadata {}",
                        logicField.getLogicFieldId(), logicField.getLogicFieldName(), logicTable.getLogicTableId(), logicTable.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), logicField);
                logicFieldMapper.insertMainFieldWithOnDuplicateUpdate(LogicField.builder()
                        .logicFieldId(logicFieldService.generateNewLogicFieldId())
                        .logicFieldName(logicField.getLogicFieldName())
                        .logicTableId(logicTable.getLogicTableId())
                        .version(1)
                        .fieldType(logicField.getFieldType())
                        .fieldComment(logicField.getFieldComment())
                        .level(logicField.getLevel())
                        .securityKey(logicField.getSecurityKey())
                        .securityPlatform(logicField.getSecurityPlatform())
                        .isDeleted(false)
                        .build());
            }
        }

        // 【2】 遍历 db 中查询到的逻辑字段，在实际查询到的逻辑字段中不存在进行删除
        for (LogicField logicFieldOfDb : logicFieldListOfDb) {
            boolean contains = false;
            for (LogicField logicField : logicFieldList) {
                if (logicField.getLogicTableId().equals(logicFieldOfDb.getLogicTableId())
                        && logicField.getLogicFieldName().equals(logicFieldOfDb.getLogicFieldName())) {
                    contains = true;
                    break;
                }
            }
            if (!contains) {
                log.warn("delete logic field {}/{} in logic table {}/{} int logic db {}/ {} not found in real query metadata {}, will delete it",
                        logicFieldOfDb.getLogicFieldId(), logicFieldOfDb.getLogicFieldName(), logicTable.getLogicTableId(), logicTable.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), logicFieldOfDb);
                logicFieldMapper.deleteByLogicFieldId(logicFieldOfDb.getLogicFieldId());
            }
        }
    }

    private void checkAndFixInstanceTableInfo(List<InstanceTableInfo> itiList, List<InstanceTableInfo> itiListOfDb, LogicDatabase logicDatabase, LogicTable logicTable, Cluster cluster, List<PhysicalCluster> physicalClusterList, List<Instance> instanceList, List<Instance> anotherInstanceList) {
        // 【1】遍历实际查询到的实例表信息，db 中缺少的进行补全，和 db 中不一致的进行修正
        for (InstanceTableInfo instanceTableInfo : itiList) {
            boolean contains = false;
            for (InstanceTableInfo instanceTableInfoOfDb : itiListOfDb) {
                // 根据唯一键找到了对应的实例表信息，检查其他信息是否不一致，存在不一致的话 update 修正
                // `uk_table_name_instance` (`logic_table_id`,`instance_id`,`db_name`,`table_name`),
                if (instanceTableInfo.getLogicTableId().equals(instanceTableInfoOfDb.getLogicTableId())
                        && instanceTableInfo.getInstanceId().equals(instanceTableInfoOfDb.getInstanceId())
                        && instanceTableInfo.getDbName().equals(instanceTableInfoOfDb.getDbName())
                        && instanceTableInfo.getTableName().equals(instanceTableInfoOfDb.getTableName())) {
                    contains = true;
                    if (!logicTable.getVersion().equals(instanceTableInfoOfDb.getTableVersion())
                            || !instanceTableInfo.getInstanceDbId().equals(instanceTableInfoOfDb.getInstanceDbId())) {
                        log.warn("update instance table info in logic table {}/{} in logic db {}/{} has inconsistent metadata, db metadata: {}, real query metadata: {}",
                                logicTable.getLogicTableId(), logicTable.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), instanceTableInfoOfDb, instanceTableInfo);
                        instanceTableInfoMapper.updateInstanceDbIdAndTableVersionByUK(InstanceTableInfo.builder()
                                .instanceId(instanceTableInfoOfDb.getInstanceId())
                                .logicTableId(instanceTableInfoOfDb.getLogicTableId())
                                .dbName(instanceTableInfoOfDb.getDbName())
                                .tableName(instanceTableInfoOfDb.getTableName())
                                .instanceDbId(instanceTableInfo.getInstanceDbId())
                                .tableVersion(logicTable.getVersion())
                                .build());
                    }
                }
            }
            // 生成新的实例表信息元数据
            if (!contains) {
                log.warn("insert instance table info in logic table {}/{} in logic db {}/{}, real query metadata {}",
                        logicTable.getLogicTableId(), logicTable.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), instanceTableInfo);
                instanceTableInfoMapper.insertOnDuplicateUpdate(InstanceTableInfo.builder()
                        .instanceDbId(instanceTableInfo.getInstanceDbId())
                        .instanceId(instanceTableInfo.getInstanceId())
                        .logicTableId(instanceTableInfo.getLogicTableId())
                        .dbName(instanceTableInfo.getDbName())
                        .tableName(instanceTableInfo.getTableName())
                        .tableVersion(instanceTableInfo.getTableVersion())
                        .isDeleted(false)
                        .build());
            }
        }

        // 【2】 遍历 db 中查询到的实例表信息，在实际查询到的实例表信息中不存在进行删除
        // `uk_table_name_instance` (`logic_table_id`,`instance_id`,`db_name`,`table_name`),
        Set<String> ukSet = new HashSet<>(itiListOfDb.size());
        for (InstanceTableInfo instanceTableInfoOfDb : itiListOfDb) {
            // 库中的 instance_table_info 如果有重复的，增加清理逻辑
            String ukInstanceTableInfo = String.format("%s_%s_%s_%s", instanceTableInfoOfDb.getLogicTableId(), instanceTableInfoOfDb.getInstanceId(), instanceTableInfoOfDb.getDbName(), instanceTableInfoOfDb.getTableName());
            boolean contains = false;
            for (InstanceTableInfo instanceTableInfo : itiList) {
                if (instanceTableInfo.getLogicTableId().equals(instanceTableInfoOfDb.getLogicTableId())
                        && instanceTableInfo.getInstanceId().equals(instanceTableInfoOfDb.getInstanceId())
                        && instanceTableInfo.getDbName().equals(instanceTableInfoOfDb.getDbName())
                        && instanceTableInfo.getTableName().equals(instanceTableInfoOfDb.getTableName())) {
                    contains = true;
                    break;
                }
            }
            if (!contains || ukSet.contains(ukInstanceTableInfo)) {
                log.warn("delete instance table info in logic table {}/{} int logic db {}/{} not found in real query metadata {}, will delete it",
                        logicTable.getLogicTableId(), logicTable.getLogicTableName(), logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), instanceTableInfoOfDb);
                instanceTableInfoMapper.deleteById(instanceTableInfoOfDb.getId());
            }
            ukSet.add(ukInstanceTableInfo);
        }
    }

    private void completeLogicTableLogicFieldAndInstanceTableInfo(LdLtLfInfoDTO.LtLfInfoDTO ltInfo, LogicDatabase logicDatabase, Cluster cluster, List<PhysicalCluster> physicalClusterList, List<Instance> instanceList, List<Instance> anotherInstanceList) {
        // 重新生成新的 logic_table_id
        Long newLogicTableId = logicTableService.generateNewLogicTableId();
        String uniqueTableName = logicTableService.generateNewUniqueLogicTableName(ltInfo.getLogicTableName());
        log.warn("insert logic table {}/{} in logic db {}/{} metadata {}", newLogicTableId, uniqueTableName, logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName(), ltInfo);
        logicTableMapper.insertMainFieldOnDuplicateKeyUpdate(LogicTable.builder()
                .logicTableId(newLogicTableId)
                .uniqueTableName(uniqueTableName)
                .logicDbId(logicDatabase.getLogicDbId())
                .clusterId(cluster.getClusterId())
                .logicTableName(ltInfo.getLogicTableName())
                .isShard(ltInfo.getIsShard())
                .version(1)
                .start(ltInfo.getStart())
                .end(ltInfo.getEnd())
                .tableSuffixType(ltInfo.getTableSuffixType())
                .tableNum(ltInfo.getTableNum())
                .underlineFlag(ltInfo.getUnderlineFlag())
                .step(ltInfo.getStep())
                .tableSql(ltInfo.getTableSql())
                .tableComment(ltInfo.getTableComment())
                .auditCreatedAt(ltInfo.getAuditCreatedAt())
                .auditUpdatedAt(ltInfo.getAuditUpdatedAt())
                .isDeleted(false)
                .build());
        log.warn("insert logic field {} in logic table {}/{} in logic db {}/{}",
                ltInfo.getLogicFieldList().size(), newLogicTableId, uniqueTableName, logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName());
        for (LogicField logicField : ltInfo.getLogicFieldList()) {
            logicFieldMapper.insertMainFieldWithOnDuplicateUpdate(LogicField.builder()
                    .logicFieldId(logicFieldService.generateNewLogicFieldId())
                    .logicFieldName(logicField.getLogicFieldName())
                    .logicTableId(newLogicTableId)
                    .version(1)
                    .fieldType(logicField.getFieldType())
                    .fieldComment(logicField.getFieldComment())
                    .level(logicField.getLevel())
                    .securityKey(logicField.getSecurityKey())
                    .securityPlatform(logicField.getSecurityPlatform())
                    .isDeleted(false)
                    .build());
        }
        log.warn("insert instance table info {} in logic table {}/{} in logic db {}/{}",
                ltInfo.getInstanceTableInfoList().size(), newLogicTableId, uniqueTableName, logicDatabase.getLogicDbId(), logicDatabase.getLogicDbName());
        for (InstanceTableInfo instanceTableInfo : ltInfo.getInstanceTableInfoList()) {
            instanceTableInfoMapper.insertOnDuplicateUpdate(InstanceTableInfo.builder()
                    .instanceDbId(instanceTableInfo.getInstanceDbId())
                    .instanceId(instanceTableInfo.getInstanceId())
                    .logicTableId(newLogicTableId)
                    .dbName(instanceTableInfo.getDbName())
                    .tableName(instanceTableInfo.getTableName())
                    .tableVersion(1)
                    .isDeleted(false)
                    .build());
        }
    }


    private void checkLogicTableAndFieldFixResultAndSendInform(LocalDateTime start, LogicMetadataCheckReq logicMetadataCheckReq) {
        LocalDateTime end = LocalDateTime.now().plusSeconds(2);
        String content = String.format("逻辑表和逻辑字段修正任务完成\n运行时间：[%s] ~ [%s]\n任务发起信息：check_type: %s; logic_db_ids: %s; unique_db_names: %s;", start.format(Time.FORMATTER), end.format(Time.FORMATTER), logicMetadataCheckReq.getCheckType(), logicMetadataCheckReq.getLogicDbIds(), logicMetadataCheckReq.getUniqueDbNames());
        informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
        }), "mountain-v2 元数据逻辑表和逻辑字段修正完成通知", content);
    }

    /**
     * keychain 关联逻辑库匹配
     */
    public void keychainLogicDbMatch(KeychainLogicDbMatchReq req) {
        log.info("keychainLogicDbMatch start req: {}", req);
        LocalDateTime start = LocalDateTime.now().minusSeconds(2);

        try {
            List<Keychain> keychainList;
            switch (req.getMatchType()) {
                case "all":
                    keychainList = keychainMapper.selectAll();
                    break;
                case "special":
                    keychainList = keychainMapper.selectByKeychainNames(req.getKeychainNames());
                    break;
                default:
                    log.error("keychainLogicDbMatch type {} not support", req.getMatchType());
                    return;
            }

            log.info("total keychains to match: {}", keychainList.size());

            // 使用并发处理 keychain 匹配
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (Keychain keychain : keychainList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        Long logicDbId = findLogicDbIdForKeychain(keychain);

                        if (logicDbId != null) {
                            int updateResult = keychainMapper.updateLogicDbIdByKeychain(keychain.getKeychain(), logicDbId);
                            if (updateResult > 0) {
                                log.info("successfully updated keychain {} with logic_db_id {}", keychain.getKeychain(), logicDbId);
                            } else {
                                log.error("failed to update keychain {} with logic_db_id {}", keychain.getKeychain(), logicDbId);
                            }
                        } else {
                            log.error("no matching logic_db_id found for keychain {}", keychain.getKeychain());
                        }
                    } catch (Exception e) {
                        log.error("error processing keychain {}", keychain.getKeychain(), e);
                    }
                }, META_DATA_CHECK_THREAD_POOL);

                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            LocalDateTime end = LocalDateTime.now();
            log.info("keychainLogicDbMatch completed in {} seconds", java.time.Duration.between(start, end).getSeconds());

            // 发送完成通知
            String content = String.format("keychain 关联逻辑库匹配完成，处理了 %d 个 keychain", keychainList.size());
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
            }), "mountain-v2 匹配 keychain 关联的逻辑库完成通知", content);
        } catch (Exception e) {
            log.error("keychainLogicDbMatch error", e);
            String content = String.format("keychain 关联逻辑库匹配异常，错误信息：%s", e.getMessage());
            informService.sendMsgToDbaInform(LeoUtils.getJsonProperty("mountain-v2-api.logic_database_check_notify_users", new TypeReference<List<String>>() {
            }), "mountain-v2 匹配 keychain 关联逻辑库异常通知", content);
        }
    }

    /**
     * 为 keychain 查找关联的逻辑库 ID
     */
    private Long findLogicDbIdForKeychain(Keychain keychain) {
        try {
            // keychain类别：keychain-普通Keychain、moreMaster-多活切换Keychain、crossKeychain-自建多活切换Keychain
            switch (keychain.getKeychainType()) {
                case "keychain":
                    boolean isShard = keychain.getType().equals("shard");
                    Set<Long> logicDbIdSet = new HashSet<>(16);

                    JSONObject dbInfosJO = JSONObject.parseObject(keychain.getDbInfos());
                    for (String key : dbInfosJO.keySet()) {
                        JSONArray dsList = dbInfosJO.getJSONArray(key);
                        for (int i = 0; i < dsList.size(); i++) {
                            JSONObject ds = dsList.getJSONObject(i);
                            if (ds.getBoolean("isMaster")) {
                                Instance instance = instanceMapper.selectOneByIpPort(ds.getString("ip"), ds.getIntValue("port"));
                                if (instance != null) {
                                    List<InstanceDbInfo> instanceDbInfoList = instanceDbInfoMapper.selectByInstanceIdAndDbName(instance.getInstanceId(), ds.getString("database"));
                                    if (instanceDbInfoList != null && !instanceDbInfoList.isEmpty()) {
                                        for (InstanceDbInfo instanceDbInfo : instanceDbInfoList) {
                                            logicDbIdSet.add(instanceDbInfo.getLogicDbId());
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (logicDbIdSet.size() == 1) {
                        LogicDatabase logicDatabase = logicDatabaseMapper.selectOneByLogicDbId(logicDbIdSet.iterator().next());
                        if (logicDatabase != null && logicDatabase.getIsShard().equals(isShard)) {
                            return logicDatabase.getLogicDbId();
                        } else {
                            log.error("no matching logic_database found for keychain {} is_shard {}, logic_db_id set size 1 {} logic_database {}", keychain.getKeychain(), isShard, logicDbIdSet, logicDatabase);
                            return null;
                        }
                    } else {
                        log.error("no matching logic_database found for keychain {} is_shard {}, logic_db_id set size not equal 1 {}", keychain.getKeychain(), isShard, logicDbIdSet);
                        return null;
                    }
                case "moreMaster":
                    HeboClusterKeychain heboClusterKeychain = heboClusterKeychainMapper.selectByKeychain(keychain.getKeychain());
                    if (heboClusterKeychain == null) {
                        log.error("no matching hebo_cluster_keychain found for keychain {}", keychain.getKeychain());
                        return null;
                    }
                    LogicDatabase logicDatabase = logicDatabaseMapper.selectOneByClusterId(Long.valueOf(heboClusterKeychain.getClusterId()));
                    if (logicDatabase == null) {
                        log.error("no matching logic_database found for keychain {} cluster {}", keychain.getKeychain(), heboClusterKeychain.getClusterId());
                        return null;
                    }
                    return logicDatabase.getLogicDbId();
                case "crossKeychain":
                    log.error("crossKeychain not support yet {}", keychain.getKeychain());
                    break;
                default:
                    return null;
            }
            return null;
        } catch (Exception e) {
            log.error("error processing keychain {}", keychain.getKeychain(), e);
            return null;
        }
    }

}
