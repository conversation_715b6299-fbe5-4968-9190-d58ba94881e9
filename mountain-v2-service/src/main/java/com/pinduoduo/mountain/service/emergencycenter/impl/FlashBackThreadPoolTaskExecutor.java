/**
 * <AUTHOR>
 * @date 2025/3/24
 * @description
 */

package com.pinduoduo.mountain.service.emergencycenter.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class FlashBackThreadPoolTaskExecutor {

    @Value("${mountain-v2-api.flashback.pool_size}")
    private Integer flashBackPoolSize;

    @Bean
    public ThreadPoolTaskExecutor flashbackThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        thread will remain at most 5 mins
        executor.setKeepAliveSeconds(5 * 60);
        executor.setAwaitTerminationSeconds(5 * 60);
        executor.setMaxPoolSize(flashBackPoolSize);
        executor.setThreadNamePrefix("FlashbackAsyncThread-");
        executor.setCorePoolSize(flashBackPoolSize);
        executor.initialize();
        return executor;
    }
}
