package com.pinduoduo.mountain.service.operationmanage.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Cluster;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.PhysicalCluster;
import com.pinduoduo.mountain.service.metadata.dto.InstanceNewBaseInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("逻辑库表返回的实例信息页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LogicDatabaseInstanceInfoDTO {
    @ApiModelProperty(value = "逻辑库 id，点击详情时传过去", required = true)
    private Long logicDbId;
    @ApiModelProperty(value = "逻辑库名", required = true)
    private String logicDbName;
    @ApiModelProperty(value = "全局唯一逻辑库名", required = true)
    private String uniqueDbName;

    @ApiModelProperty(value = "业务线", required = true)
    private String businessName;
    @ApiModelProperty(value = "服务名", required = true)
    private String serviceName;

    @ApiModelProperty(value = "逻辑库的集群信息，集群和逻辑库的关系变为 1:N 后，这里的数组一定是一个了，详情见 https://note.pdd.net/doc/787051061355503616", required = true)
    private List<ClusterSummary> clusterSummaryList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ClusterSummary {
        @ApiModelProperty(value = "集群 id", required = true)
        private Long clusterId;
        @ApiModelProperty(value = "集群名", required = true)
        private String clusterName;

        @ApiModelProperty(value = "集群的物理集群/物理资源信息", required = true)
        private List<PhysicalClusterSummary> physicalClusterSummaryList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PhysicalClusterSummary {
        @ApiModelProperty(value = "物理集群 id", required = true)
        private Long physicalClusterId;
        @ApiModelProperty(value = "物理集群名", required = true)
        private String physicalClusterName;
        @ApiModelProperty(value = "实例列表", required = true)
        private List<InstanceNewBaseInfoDTO> instanceNewBaseInfoList;

    }
}
