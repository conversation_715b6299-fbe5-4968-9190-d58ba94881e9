package com.pinduoduo.mountain.service.emergencycenter.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FlashbackTaskReq {
    @ApiModelProperty(value = "sql type, should be in ['insert','update','delete']")
    private List<String> sqlType;
    private String ip;
    private String port;
    @ApiModelProperty(value = "database name")
    private String dbname;
    @ApiModelProperty(value = "table name list")
    private List<String> tbname;
    @ApiModelProperty(value = "flashback start time yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;
    @ApiModelProperty(value = "flashback end time yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;

    private String sessionId;

    private String matchSql;


    private String executeHost;


}
