package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Columns;
import com.pinduoduo.mountain.repository.mysql.informationschema.sqlprovider.ColumnsSqlProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
@Transactional(rollbackFor = Exception.class)
public interface ColumnsMapper {

    /**
     * 查询表的字段信息
     *
     * @param tableSchema 数据库名
     * @param tableName   表名
     * @return 字段信息列表
     */
    @Select("SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = #{tableSchema} AND TABLE_NAME = #{tableName} ORDER BY ORDINAL_POSITION")
    List<Columns> selectByTableSchemaAndTableName(String tableSchema, String tableName);

    /**
     * 根据表名和列名获取字段信息
     *
     * @param tableSchema 数据库名
     * @param tableName   表名
     * @param columnName  列名
     * @return 字段信息
     */
    @Select("SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = #{tableSchema} AND TABLE_NAME = #{tableName} AND COLUMN_NAME = #{columnName} LIMIT 1")
    Columns selectByTableSchemaAndTableNameAndColumnName(String tableSchema, String tableName, String columnName);

    /**
     * 根据表名获取show columns信息
     *
     * @param tableName 表名
     * @return show columns信息
     */
    @Select("SHOW COLUMNS FROM `${tableName}`")
    List<HashMap<String, Object>> getShowColumnsByTableName(String tableName);

    /**
     * 获取自增属性的列的名称
     *
     * @param schema 数据库名
     * @param table  表名
     * @return 自增属性的列名称
     */
    @Select("SELECT column_name FROM INFORMATION_SCHEMA.columns WHERE (extra LIKE UPPER('auto_increment%') OR extra LIKE 'auto_increment%') AND TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table}")
    List<HashMap<String, String>> getAutoIncrementColumns(String schema, String table);

    @Select("SELECT * FROM INFORMATION_SCHEMA.columns WHERE (EXTRA LIKE 'AUTO_INCREMENT%' OR EXTRA LIKE 'auto_increment%') AND TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table}")
    List<Columns> getAutoIncrementColumnInfos(String schema, String table);

    /**
     * 获取主键列的名称
     *
     * @param schema 数据库名
     * @param table  表名
     * @return 自增属性的列名称
     */
    @Select("SELECT column_name FROM INFORMATION_SCHEMA.columns WHERE column_key='PRI' AND TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table}")
    List<String> getPRIColumns(String schema, String table);

    /**
     * 获取更新时间列的extra信息
     *
     * @param schema 库名
     * @param table  表名
     * @return extra信息
     */
    @Select("SELECT extra FROM INFORMATION_SCHEMA.columns WHERE TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table} AND upper(column_name)=upper('#{columnName}')")
    List<String> getUpdateColumnExtraInfo(String schema, String table, String columnName);

    /**
     * 查询表的字段信息，用一些其他条件
     *
     * @param tableSchema    数据库名
     * @param tableName      表名
     * @param otherCondition 条件名称
     * @return 索引信息列表
     * @see ColumnsSqlProvider#selectByTableSchemaAndTableNameAndSomeConditionProvider(String, String, String)
     */
    @SelectProvider(type = ColumnsSqlProvider.class, method = "selectByTableSchemaAndTableNameAndSomeConditionProvider")
    List<Columns> selectByTableSchemaAndTableNameAndSomeCondition(String tableSchema, String tableName, String otherCondition);
}
