package com.pinduoduo.mountain.repository.mysql.informationschema.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Partitions {
    private String tableCatalog;
    private String tableSchema;
    private String tableName;
    private String partitionName;
    private String subpartitionName;
    private long partitionOrdinalPosition;
    private long subpartitionOrdinalPosition;
    private String partitionMethod;
    private String subpartitionMethod;
    private String partitionExpression;
    private String subpartitionExpression;
    private String partitionDescription;
    private long tableRows;
    private long avgRowLength;
    private long dataLength;
    private long maxDataLength;
    private long indexLength;
    private long dataFree;
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
    private java.sql.Timestamp checkTime;
    private long checksum;
    private String partitionComment;
    private String nodegroup;
    private String tablespaceName;
}
