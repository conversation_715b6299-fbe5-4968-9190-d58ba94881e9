package com.pinduoduo.mountain.repository.redis;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Order(3)
@Slf4j
@Configuration
public class RedisDao {

    private final RedissonClient redissonClient;

    public RedisDao(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * delete key
     */
    public void delete(String key) {
        redissonClient.getKeys().delete(key);
    }

    /**
     * string 类型
     */
    public void writeToString(String key, String value) {
        redissonClient.getBucket(key).set(value);
    }

    public String readFromString(String key) {
        Object value = redissonClient.getBucket(key).get();
        if (value == null) {
            return null;
        }
        return (String) value;
    }

    /**
     * hash 类型
     */
    public void writeToHash(String key, Map<String, Object> map) {
        redissonClient.getMap(key).clear();
        redissonClient.getMap(key).putAll(map);
    }

    public void writeToHashByField(String key, String field, String value) {
        redissonClient.getMap(key).put(field, value);
    }

    public Map<String, Object> readFromHash(String key) {
        Map<Object, Object> o2o = redissonClient.getMap(key).readAllMap();
        if (o2o == null) {
            return null;
        }

        Map<String, Object> s2o = new HashMap<>(o2o.size());
        o2o.forEach((k, v) -> s2o.put((String) k, v));
        return s2o;
    }

    public String readFromHashByField(String key, String field) {
        return (String) redissonClient.getMap(key).get(field);
    }

    /**
     * list
     */
    public <T> void writeToList(String key, List<T> value) {
        redissonClient.getList(key).clear();
        redissonClient.getList(key).addAll(value);
    }

    public void addToList(String key, String value) {
        redissonClient.getList(key).add(value);
    }

    public List<String> readFromList(String key) {
        List<Object> objectList = redissonClient.getList(key).readAll();
        if (objectList == null) {
            return null;
        }

        List<String> stringList = new ArrayList<>(objectList.size());
        for (Object object : objectList) {
            stringList.add((String) object);
        }
        return stringList;
    }
}
