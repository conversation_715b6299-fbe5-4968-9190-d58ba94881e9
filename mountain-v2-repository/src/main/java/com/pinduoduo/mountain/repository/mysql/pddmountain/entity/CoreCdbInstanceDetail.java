package com.pinduoduo.mountain.repository.mysql.pddmountain.entity;

import lombok.Data;

import java.time.LocalDateTime;

/** date: 2024-09-29 */
@Data
public class CoreCdbInstanceDetail {
	private Long id;
	private Integer wanStatus;
	private String zone;
	private Integer initFlag;
	private Integer memory;
	private Integer status;
	private Integer vpcId;
	private String pddId;
	private Integer volume;
	private Integer autoRenew;
	private Integer protectMode;
	private String roGroups;
	private Integer subnetId;
	private Integer instanceType;
	private Integer projectId;
	private String region;
	private LocalDateTime deadlineTime;
	private Integer deployMode;
	private Integer taskStatus;
	private String deviceType;
	private String engineVersion;
	private String instanceName;
	private String drInfo;
	private String wanDomain;
	private Integer wanPort;
	private Integer payType;
	private LocalDateTime createTime;
	private String physicalId;
	private String roVipInfo;
	private String slaveInfo;
	private String vip;
	private Integer vport;
	private Integer cdbError;
	private String uniqVpcId;
	private String uniqSubnetId;
	private String masterInfo;
	private LocalDateTime createdAt;
	private LocalDateTime updatedAt;
	private String deviceClass;
	private Integer cpu;
	private String deployGroupId;
	private String type;

	private String uniqSubnetIds;
}
