package com.pinduoduo.mountain.repository.mysql.informationschema.sqlprovider;

import com.pinduoduo.mountain.repository.mysql.informationschema.mapper.StatisticsMapper;

/**
 * <AUTHOR>
 */
public class StatisticsSqlProvider {

    /**
     * @see StatisticsMapper#selectBySchemaAndTable
     */
    public String selectBySchemaAndTableBySomeCondition(String otherCondition) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table} ");

        if (otherCondition != null && !otherCondition.isEmpty()) {
            sql.append(otherCondition);
        }
        return sql.toString();
    }
}
