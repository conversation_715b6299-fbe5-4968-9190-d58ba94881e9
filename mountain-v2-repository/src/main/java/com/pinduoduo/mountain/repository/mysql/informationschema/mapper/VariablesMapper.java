package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Variables;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface VariablesMapper {

    /**
     * 根据variableName获取GLOBAL VARIABLES信息
     *
     * @param variableName variableName
     * @return GLOBAL VARIABLES信息
     */
    @Select("SHOW GLOBAL VARIABLES LIKE #{variableName}")
    List<HashMap<String, String>> getGlobalVariablesByName(String variableName);

    @Select("SHOW GLOBAL VARIABLES")
    List<HashMap<String, Object>> getInstanceAllGlobalVariables();

    /**
     * 根据 variableNames 获取 Global 级别对应的值
     *
     * @param variableNamesInSql 形如 'sql_mode','innodb_large_prefix','innodb_default_row_format' 的字符串
     * @return List<Variables>
     */
    @Select("SHOW GLOBAL VARIABLES WHERE VARIABLE_NAME IN (${variableNamesInSql})")
    @Results(id = "VariablesMap", value = {
            @Result(property = "variableName", column = "Variable_name"),
            @Result(property = "value", column = "Value")
    })
    List<Variables> showGlobalVariablesByNames(String variableNamesInSql);

    /**
     * 根据 variableName 获取 Global 级别对应的值
     *
     * @param variableName variableName 变量名
     * @return Variables
     */
    @Select("SHOW VARIABLES WHERE VARIABLE_NAME = #{variableName}")
    @ResultMap("VariablesMap")
    Variables showGlobalVariableByName(String variableName);

}
