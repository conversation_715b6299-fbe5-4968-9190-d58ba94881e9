package com.pinduoduo.mountain.repository.mysql.informationschema.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Columns {
    private String tableCatalog;
    private String tableSchema;
    private String tableName;
    private String columnName;
    private Integer ordinalPosition;
    private String columnDefault;
    private String isNullable;
    @ApiModelProperty("数据类型，例如 tinyint varchar")
    private String dataType;
    @ApiModelProperty("字符型列的最大字符数")
    private Long characterMaximumLength;
    @ApiModelProperty("字符型列的最大字节数")
    private Long characterOctetLength;
    @ApiModelProperty("数值型列的精度")
    private Integer numericPrecision;
    @ApiModelProperty("数值型列的小数位数")
    private Integer numericScale;
    private Integer datetimePrecision;
    private String characterSetName;
    private String collationName;
    @ApiModelProperty("数据类型，例如 tinyint(4) varchar(64)")
    private String columnType;
    private String columnKey;
    private String extra;
    private String privileges;
    private String columnComment;
    private String generationExpression;
}
