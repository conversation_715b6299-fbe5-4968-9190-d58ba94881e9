package com.pinduoduo.mountain.repository.mysql.informationschema.sqlprovider;

import com.pinduoduo.mountain.repository.mysql.informationschema.mapper.TablesMapper;

/**
 * <AUTHOR>
 */
public class TablesSqlProvider {

    /**
     * @see TablesMapper#selectPageAllTableOfPhysical
     */
    public String selectPageAllTableOfPhysical(String physicalDb, String orderBy, String sortType, String searchVal) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA = #{physicalDb} ");

        sql.append(" AND TABLE_TYPE = 'BASE TABLE' ");

        if (searchVal != null && !searchVal.isEmpty()) {
            sql.append(" AND TABLE_NAME LIKE CONCAT('%', #{searchVal}, '%') ");
        }

        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ").append(orderBy).append(" ");
            if ("desc".equalsIgnoreCase(sortType)) {
                sql.append(" DESC ");
            }
        }

        return sql.toString();
    }

    /**
     * @see TablesMapper#selectPageAllTableOfLogic
     */
    public String selectPageAllTableOfLogic(String logicDb, String orderBy, String sortType, String searchVal) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA REGEXP CONCAT('^', #{logicDb}, '[-_]?[0-9]*$') ");

        sql.append(" AND TABLE_TYPE = 'BASE TABLE' ");

        if (searchVal != null && !searchVal.isEmpty()) {
            sql.append(" AND TABLE_NAME LIKE CONCAT('%', #{searchVal}, '%') ");
        }

        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ").append(orderBy).append(" ");
            if ("desc".equalsIgnoreCase(sortType)) {
                sql.append(" DESC ");
            }
        }

        return sql.toString();
    }


    /**
     * @see TablesMapper#selectPageBusinessTableOfPhysical
     */
    public String selectPageBusinessTableOfPhysical(String physicalDb, String orderBy, String sortType, String searchVal) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA = #{physicalDb} ");

        sql.append(" AND TABLE_TYPE = 'BASE TABLE' " +
                " AND TABLE_NAME NOT LIKE 'taishan\\_%' " +
                " AND TABLE_NAME NOT LIKE '\\_%\\_del' " +
                " AND TABLE_NAME NOT LIKE '\\_%\\_del\\_bak' " +
                " AND TABLE_NAME NOT LIKE 'zz\\_\\_%\\_gho' " +
                " AND TABLE_NAME NOT LIKE 'zz\\_\\_%\\_del' " +
                " AND TABLE_NAME NOT LIKE '\\_%\\_ghc' " +
                " AND TABLE_NAME NOT IN ('zebra_distributed_sequence','taishan_zebra_distributed_sequence', 'zebra_distributed_sequence_metadata', 'taishan_zebra_distributed_sequence_metadata') ");

        if (searchVal != null && !searchVal.isEmpty()) {
            sql.append(" AND TABLE_NAME LIKE CONCAT('%', #{searchVal}, '%') ");
        }

        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ").append(orderBy).append(" ");
            if ("desc".equalsIgnoreCase(sortType)) {
                sql.append(" DESC ");
            }
        }

        return sql.toString();
    }

    /**
     * @see TablesMapper#selectPageBusinessTableOfLogic
     */
    public String selectPageBusinessTableOfLogic(String logicDb, String orderBy, String sortType, String searchVal) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA REGEXP CONCAT('^', #{logicDb}, '[-_]?[0-9]*$') ");

        sql.append(" AND TABLE_TYPE = 'BASE TABLE' " +
                " AND TABLE_NAME NOT LIKE 'taishan\\_%' " +
                " AND TABLE_NAME NOT LIKE '\\_%\\_del' " +
                " AND TABLE_NAME NOT LIKE '\\_%\\_del\\_bak' " +
                " AND TABLE_NAME NOT LIKE 'zz\\_\\_%\\_gho' " +
                " AND TABLE_NAME NOT LIKE 'zz\\_\\_%\\_del' " +
                " AND TABLE_NAME NOT LIKE '\\_%\\_ghc' " +
                " AND TABLE_NAME NOT IN ('zebra_distributed_sequence','taishan_zebra_distributed_sequence', 'zebra_distributed_sequence_metadata', 'taishan_zebra_distributed_sequence_metadata') ");

        if (searchVal != null && !searchVal.isEmpty()) {
            sql.append(" AND TABLE_NAME LIKE CONCAT('%', #{searchVal}, '%') ");
        }

        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ").append(orderBy).append(" ");
            if ("desc".equalsIgnoreCase(sortType)) {
                sql.append(" DESC ");
            }
        }

        return sql.toString();
    }

    /**
     * @see TablesMapper#selectPageTempTableOfPhysical
     */
    public String selectPageTempTableOfPhysical(String physicalDb, String orderBy, String sortType, String searchVal) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA = #{physicalDb} ");

        sql.append(" AND TABLE_TYPE = 'BASE TABLE' " +
                " AND ( TABLE_NAME LIKE '\\_%\\_del' " +
                " OR TABLE_NAME LIKE '\\_%\\_del\\_bak' " +
                " OR TABLE_NAME LIKE 'zz\\_\\_%\\_gho' " +
                " OR TABLE_NAME LIKE 'zz\\_\\_%\\_del' " +
                " OR TABLE_NAME LIKE '\\_%\\_ghc' )");

        if (searchVal != null && !searchVal.isEmpty()) {
            sql.append(" AND TABLE_NAME LIKE CONCAT('%', #{searchVal}, '%') ");
        }

        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ").append(orderBy).append(" ");
            if ("desc".equalsIgnoreCase(sortType)) {
                sql.append(" DESC ");
            }
        }

        return sql.toString();
    }

    /**
     * @see TablesMapper#selectPageTempTableOfLogic
     */
    public String selectPageTempTableOfLogic(String logicDb, String orderBy, String sortType, String searchVal) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA REGEXP CONCAT('^', #{logicDb}, '[-_]?[0-9]*$') ");

        sql.append(" AND TABLE_TYPE = 'BASE TABLE' " +
                " AND ( TABLE_NAME LIKE '\\_%\\_del' " +
                " OR TABLE_NAME LIKE '\\_%\\_del\\_bak' " +
                " OR TABLE_NAME LIKE 'zz\\_\\_%\\_gho' " +
                " OR TABLE_NAME LIKE 'zz\\_\\_%\\_del' " +
                " OR TABLE_NAME LIKE '\\_%\\_ghc' )");

        if (searchVal != null && !searchVal.isEmpty()) {
            sql.append(" AND TABLE_NAME LIKE CONCAT('%', #{searchVal}, '%') ");
        }

        if (orderBy != null && !orderBy.isEmpty()) {
            sql.append(" ORDER BY ").append(orderBy).append(" ");
            if ("desc".equalsIgnoreCase(sortType)) {
                sql.append(" DESC ");
            }
        }

        return sql.toString();
    }
}
