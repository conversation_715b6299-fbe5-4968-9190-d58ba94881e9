package com.pinduoduo.mountain.repository.mysql.mountainfailover.mapper;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.Cluster;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.HeboCluster;
import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.HeboClusterKeychain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface HeboClusterKeychainMapper {

    @Select("SELECT * FROM hebo_cluster_keychain WHERE cluster_id = #{clusterId}")
    List<HeboClusterKeychain> selectByClusterId(Long clusterId);

    @Select("select h.cluster_name as cluster_name from hebo_cluster h inner join hebo_cluster_keychain k on h.cluster_id=k.cluster_id where k.keychain= #{keychain} limit 1")
    String selectHeboClusterByKeychain(String keychain);

    @Select("select c.* from hebo_cluster_keychain k inner join hebo_cluster c on k.cluster_id = c.cluster_id where k.keychain= #{keychain} limit 1")
    HeboCluster selectPhyClusterByKeychain(String keychain);

    @Select("SELECT * FROM hebo_cluster_keychain WHERE keychain = #{keychain}")
    HeboClusterKeychain selectByKeychain(String keychain);
}
