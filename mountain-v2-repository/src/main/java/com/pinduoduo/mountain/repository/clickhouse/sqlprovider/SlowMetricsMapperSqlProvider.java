package com.pinduoduo.mountain.repository.clickhouse.sqlprovider;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;


public class SlowMetricsMapperSqlProvider {
    public String getSql(String instId, Date start, Date end, String period) {
        String sql = " select count (1) as value, toString(%s(_time)) as time from slow_metrics " +
                " where server = '%s' " +
                " and _time >= '%s' " +
                " and _time < '%s' " +
                " group by time order by time";

        return String.format(sql, period, instId, DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(start), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(end));
    }


    public String slowQueryDetail(String instId, Date start, Date end) {
        String sql_str = " select " +
                " sql," +
                " crc32," +
                " count (1) all_count, " +
                " round(sum(total_time), 3) all_total_time, " +
                " round(sum(query_time), 3) all_query_time, " +
                " round(sum(lock_time), 3) all_lock_time, " +
                " round(avg(total_time), 3) avg_total_time, " +
                " sum(rows_examined) all_rows_examined, " +
                " sum(rows_send) all_rows_send, " +
                " round(sum(rows_send) / sum( if (rows_examined = 0,1, rows_examined))* 100, 2) send_examined_percent" +
                " FROM slow_metrics " +
                " where server = '%s' " +
                " and _time>= '%s' " +
                " and _time< '%s' " +
                " and total_time is not null " +
                " GROUP BY crc32, sql ORDER BY all_count DESC " +
                " LIMIT 0, 300; ";
        return String.format(sql_str, instId, DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(start), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(end));
    }

    public String sqlDetail(String instanceId, Long fingerprint, Date start, Date end) {
        String sql = "select " +
                "   server as instance_id, " +
                "   database as dbname," +
                "   sql," +
                "   if(like(log,'%%select%%'), log, sql) as sample" +
                " from slow_metrics " +
                " where server='%s' and " +
                "   crc32=%s and " +
                "   log is not null  and " +
                "   sql is not null and  " +
                "   _time>'%s' and _time<'%s' limit 1";
        return String.format(sql, instanceId, fingerprint,
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(start), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(end));
    }

}
