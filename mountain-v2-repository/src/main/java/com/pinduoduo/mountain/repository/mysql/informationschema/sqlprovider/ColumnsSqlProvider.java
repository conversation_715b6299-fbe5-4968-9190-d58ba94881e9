package com.pinduoduo.mountain.repository.mysql.informationschema.sqlprovider;

import com.pinduoduo.mountain.repository.mysql.informationschema.mapper.ColumnsMapper;

/**
 * <AUTHOR>
 */
public class ColumnsSqlProvider {

    /**
     * @see ColumnsMapper#selectByTableSchemaAndTableNameAndSomeCondition
     */
    public String selectByTableSchemaAndTableNameAndSomeConditionProvider(String tableSchema, String tableName, String otherCondition) {
        StringBuilder sql = new StringBuilder("SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = #{tableSchema} AND TABLE_NAME = #{tableName} ");

        if (otherCondition != null && !otherCondition.isEmpty()) {
            sql.append(otherCondition).append("ORDER BY COLUMN_NAME DESC");
        }
        return sql.toString();
    }
}
