package com.pinduoduo.mountain.repository.clickhouse.mountain_backend;

import com.pinduoduo.mountain.repository.clickhouse.entity.ProcessListSqlDetail;
import com.pinduoduo.mountain.repository.clickhouse.sqlprovider.CoreProcessListCollectMapperSqlProvider;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
@Mapper
@Transactional(rollbackFor = Exception.class)
public interface CoreProcessListCollectMapper {
    @SelectProvider(value = CoreProcessListCollectMapperSqlProvider.class, method = "selectProcessListTs")
    List<TsDataPoint> selectProcessListTs(String srcIp, String srcPort, String period, Date beginTime, Date endTime);

    @SelectProvider(value = CoreProcessListCollectMapperSqlProvider.class, method = "selectProcessListTotalCnt")
    Long selectProcessListTotalCnt(String srcIp, String srcPort, Date beginTime, Date endTime);


    @SelectProvider(value = CoreProcessListCollectMapperSqlProvider.class, method = "selectProcessListStats")
    List<TsDataPoint> selectProcessListStats(String srcIp, String srcPort, Date beginTime, Date endTime, Long offset, Integer cnt);

    @SelectProvider(value = CoreProcessListCollectMapperSqlProvider.class, method = "selectProcessListSqlDetail")
    ProcessListSqlDetail selectProcessListSqlDetail(String srcIp, String srcPort, Long fingerprint);


    @SelectProvider(value = CoreProcessListCollectMapperSqlProvider.class, method = "getProcessListCollectTotal")
    Long getProcessListCollectTotal(String srcIp, String srcPort, Date beginTime, Date endTime);

    @SelectProvider(value = CoreProcessListCollectMapperSqlProvider.class, method = "getProcessListData")
     List<ProcessListSqlDetail> getProcessListData(String srcIp, String srcPort, Date beginTime, Date endTime, Long offset, Integer cnt);
}
