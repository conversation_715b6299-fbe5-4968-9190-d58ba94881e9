package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
@Transactional(rollbackFor = Exception.class)
public interface ProcesslistMapper {

    /**
     * 查询访问数据库的 ip
     *
     * @param excludeUser 排除的用户
     * @return List<String> host 列表
     */
    @Select("SELECT host FROM information_schema.PROCESSLIST WHERE `user` NOT IN (${excludeUser})")
    List<String> selectHostByExcludeUser(String excludeUser);

    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    @Select("select h2 from ( select user as u1 , substring_index(host,\":\",1) as h2 from information_schema.processlist where 1=1 and user like 'dev_%' ) as t where 1=1 group by u1,h2")
    List<String> selectIpByExcludeUser();

    /**
     * 查询实例中处于 metadata lock 或 Waiting for table level lock 状态的快照个数
     *
     * @return 持有锁的连接数
     */
    @Select("SELECT COUNT(*) FROM information_schema.processlist WHERE (state LIKE '%metadata lock%' OR state LIKE '%Waiting for table level lock%') AND id != CONNECTION_ID()")
    int countLockMDLProcesslist();

    /**
     * 查询实例中非以下的活跃会话
     */
    @Select("SELECT COUNT(*) FROM information_schema.processlist WHERE " +
            "command<>'sleep' AND command<>'Binlog Dump' AND command<>'Binlog Dump GTID' " +
            "AND `user`<>'tencentroot' AND `user`<>'system user' AND `user`<>'repl' AND `user`<>'dbms'" +
            "AND id != CONNECTION_ID()")
    int countActiveThreadsRunningProcesslist();

}
