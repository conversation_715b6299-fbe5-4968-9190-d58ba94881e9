package com.pinduoduo.mountain.repository.clickhouse.pdb_log_repository;


import com.pinduoduo.mountain.repository.clickhouse.entity.ProcessListSqlDetail;
import com.pinduoduo.mountain.repository.clickhouse.entity.SlowQueryDetail;
import com.pinduoduo.mountain.repository.clickhouse.sqlprovider.SlowMetricsMapperSqlProvider;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
@Mapper
@Transactional(rollbackFor = Exception.class)
public interface SlowMetricsMapper {
    @SelectProvider(type = SlowMetricsMapperSqlProvider.class, method = "getSql")
    List<TsDataPoint> query(String instId, Date start, Date end, String period);

    @SelectProvider(type = SlowMetricsMapperSqlProvider.class, method = "slowQueryDetail")
    List<SlowQueryDetail> slowQueryDetail(String instId, Date start, Date end);

    @SelectProvider(type = SlowMetricsMapperSqlProvider.class, method = "sqlDetail")
    ProcessListSqlDetail sqlDetail(String instanceId, Long fingerprint, Date start, Date end);
}
