package com.pinduoduo.mountain.repository.mysql.mountainfailover.mapper;

import com.pinduoduo.mountain.repository.mysql.mountainfailover.entity.HeboClusterResourceGroupOfMF;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface HeboClusterResourceGroupMapperOfMF {

    @Select("SELECT * FROM hebo_cluster_resource_group WHERE cluster_id = #{clusterId}")
    List<HeboClusterResourceGroupOfMF> selectByClusterId(Integer clusterId);
}
