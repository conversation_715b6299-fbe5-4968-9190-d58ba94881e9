package com.pinduoduo.mountain.repository.mysql.informationschema.entity;

import lombok.Data;

import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class Tables {
    private String tableCatalog;
    private String tableSchema;
    private String tableName;
    private String tableType;
    private String engine;
    private Long version;
    private String rowFormat;
    private Long tableRows;
    private Long avgRowLength;
    private Long dataLength;
    private Long maxDataLength;
    private Long indexLength;
    private Long dataFree;
    private BigInteger autoIncrement;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime checkTime;
    private String tableCollation;
    private Long checksum;
    private String createOptions;
    private String tableComment;
}
