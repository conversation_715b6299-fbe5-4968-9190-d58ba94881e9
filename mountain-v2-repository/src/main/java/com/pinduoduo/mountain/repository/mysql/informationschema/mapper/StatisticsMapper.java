package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Statistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface StatisticsMapper {

    /**
     * 根据库名和表名查询索引信息
     *
     * @param schema 数据库名
     * @param table  表名
     * @return 索引信息列表
     */
    @Select("SELECT * FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table}")
    List<Statistics> selectBySchemaAndTable(String schema, String table);

    /**
     * 根据库名和表名和索引名查询索引信息
     *
     * @param schema 数据库名
     * @param table  表名
     * @param index  索引名
     * @return 索引信息列表
     */
    @Select("SELECT * FROM information_schema.STATISTICS WHERE TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table} AND INDEX_NAME = #{index} ORDER BY SEQ_IN_INDEX")
    List<Statistics> selectBySchemaAndTableAndIndexOrderBySeqInIndex(String schema, String table, String index);

    /**
     * 根据库名和表名获取唯一键的列信息
     *
     * @param schema 库名
     * @param table  表名
     * @return 唯一键的列信息
     */
    @Select("SELECT index_name, GROUP_CONCAT(column_name ORDER BY seq_in_index SEPARATOR ',') AS unique_name FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table} AND non_unique=0 GROUP BY index_name having index_name!= unique_name")
    List<HashMap<String, String>> getUniqueKeyColumns(String schema, String table);

    /**
     * 获取更新时间列且有索引的列信息
     *
     * @param schema 库名
     * @param table  表名
     * @return index_name
     */
    @Select("SELECT index_name FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = #{schema} AND TABLE_NAME = #{table} AND upper(column_name)=upper(#{columnName}) AND seq_in_index=1")
    List<String> getUpdateColumnHaveIndexColumns(String schema, String table, String columnName);
}
