package com.pinduoduo.mountain.repository.mysql;

/**
 * <AUTHOR>
 */
public class DataSourceRouter {
    private static final ThreadLocal<String> DATA_SOURCE_KEY = new ThreadLocal<>();

    /**
     * 设置当前线程使用的数据源
     */
    public static void setDataSourceKey(String dataSource) {
        DATA_SOURCE_KEY.set(dataSource);
    }

    /**
     * 获取当前线程使用的数据源
     */
    public static String getDataSourceKey() {
        return DATA_SOURCE_KEY.get();
    }

    /**
     * 清除当前线程使用的数据源
     */
    public static void clearDataSourceKey() {
        DATA_SOURCE_KEY.remove();
    }
}
