package com.pinduoduo.mountain.repository.redis;

import com.pinduoduo.mountain.common.config.Keychain;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 */
@Order(2)
@Configuration
public class RedisConfiguration {

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.setCodec(new StringCodec());
        config.useSingleServer()
                .setAddress(Keychain.REDIS_URL)
                .setPassword(Keychain.REDIS_PASSWORD)
                .setDatabase(0);

        return Redisson.create(config);
    }

}
