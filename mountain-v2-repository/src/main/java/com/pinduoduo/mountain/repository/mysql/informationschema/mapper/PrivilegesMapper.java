package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface PrivilegesMapper {

    /**
     * 查询指定用户的MySQL权限信息
     *
     * @param username 用户名
     * @return 权限列表，例如 ["SELECT", "INSERT", "UPDATE", "DELETE"]
     */
    @Select("SHOW GRANTS FOR #{username}@'%'")
    List<String> getUserPrivileges(String username);
}
