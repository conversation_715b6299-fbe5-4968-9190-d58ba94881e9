package com.pinduoduo.mountain.repository.clickhouse;

import com.pinduoduo.mountain.common.bean.LeoConfiguration;
import com.pinduoduo.mountain.common.config.DataSourceName;
import com.pinduoduo.mountain.common.constant.DataSourceConstant;
import com.pinduoduo.mountain.repository.util.DaoCommon;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * clickhouse 数据源配置
 * 依赖 {@link LeoConfiguration} 先初始化之后才能进行初始化
 *
 * <AUTHOR>
 */
@Configuration
@Order(2)
@EnableTransactionManagement
@ComponentScan(value = "com.pinduoduo.mountain.repository.clickhouse")
@Slf4j
public class DataSourceConfigurationClickHouse {

    // ####################################################################################
    // #################################### DataSource ####################################
    // ####################################################################################

    //@Bean(name = "pdbLogRepositoryDS1")
    //public DataSource pdbLogRepositoryDS1() {
    //    HikariConfig config = DataSourceName.PDB_LOG_REPOSITORY_DSN1_VAM1_CONFIG.getHikariConfig(DataSourceConstant.CLICKHOUSE);
    //    return new HikariDataSource(config);
    //}
    //
    //@Bean(name = "pdbLogRepositoryDS2")
    //public DataSource pdbLogRepositoryDS2() {
    //    HikariConfig config = DataSourceName.PDB_LOG_REPOSITORY_DSN2_IEM3_CONFIG.getHikariConfig(DataSourceConstant.CLICKHOUSE);
    //    return new HikariDataSource(config);
    //}

    //@Bean(name = "pdbLogRepositoryDS")
    //public DataSource pdbLogRepositoryDS(@Qualifier("pdbLogRepositoryDS1") DataSource pdbLogRepositoryDS1,
    //                                     @Qualifier("pdbLogRepositoryDS2") DataSource pdbLogRepositoryDS2) {
    //    DynamicDataSource dynamicDataSource = new DynamicDataSource();
    //    dynamicDataSource.setDefaultTargetDataSource(pdbLogRepositoryDS1);
    //    dynamicDataSource.setTargetDataSources(new HashMap<Object, Object>(4) {{
    //        put("slowSqlDsn1Vam1DS", pdbLogRepositoryDS1);
    //        put("slowSqlDsn2Iem3DS", pdbLogRepositoryDS2);
    //    }});
    //    return dynamicDataSource;
    //}

    @Bean(name = "pdbLogRepositoryDS")
    public DataSource getDefaultDataSource() {
        HikariConfig config = DataSourceName.PDB_LOG_REPOSITORY_DSN_CONFIG.getHikariConfig(DataSourceConstant.CLICKHOUSE);
        return new HikariDataSource(config);
    }

    //@Bean(name = "slowSqlDsn1Vam1DS")
    //public DataSource slowSqlDsn1Vam1Config() {
    //    HikariConfig config = DataSourceName.SLOW_SQL_DSN1_VAM1_CONFIG.getHikariConfig(DataSourceConstant.CLICKHOUSE);
    //    return new HikariDataSource(config);
    //}
    //
    //@Bean(name = "slowSqlDsn2Iem3DS")
    //public DataSource slowSqlDsn2Iem3Config() {
    //    HikariConfig config = DataSourceName.SLOW_SQL_DSN2_IEM3_CONFIG.getHikariConfig(DataSourceConstant.CLICKHOUSE);
    //    return new HikariDataSource(config);
    //}
    //
    //@Bean(name = "slowSqlDynamicDS")
    //public DataSource slowSqlDynamicDS(@Qualifier("slowSqlDsn1Vam1DS") DataSource slowSqlDsn1Vam1DS,
    //                                   @Qualifier("slowSqlDsn2Iem3DS") DataSource slowSqlDsn2Iem3DS) {
    //    DynamicDataSource dynamicDataSource = new DynamicDataSource();
    //    dynamicDataSource.setDefaultTargetDataSource(slowSqlDsn1Vam1DS);
    //    dynamicDataSource.setTargetDataSources(new HashMap<Object, Object>(4) {{
    //        put("slowSqlDsn1Vam1DS", slowSqlDsn1Vam1DS);
    //        put("slowSqlDsn2Iem3DS", slowSqlDsn2Iem3DS);
    //    }});
    //    return dynamicDataSource;
    //}

    @Bean(name = "slowSqlDS")
    public DataSource slowSqlConfig() {
        HikariConfig config = DataSourceName.SLOW_SQL_DSN_CONFIG.getHikariConfig(DataSourceConstant.CLICKHOUSE);
        return new HikariDataSource(config);
    }

    // ###############################################################################################
    // #################################### SqlSessionFactoryBean ####################################
    // ###############################################################################################

    @Bean(name = "pdbLogRepositorySF")
    public SqlSessionFactoryBean defaultSqlSessionFactoryBean(@Qualifier("pdbLogRepositoryDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.CLICKHOUSE, false);
    }

    @Bean(name = "slowSqlSF")
    public SqlSessionFactoryBean cdbMonitorDynamicSqlSessionFactoryBean(@Qualifier("slowSqlDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.CLICKHOUSE, false);
    }

    // ###############################################################################################
    // #################################### MapperScannerConfigurer ##################################
    // ###############################################################################################

    @Bean(name = "pdbLogRepositoryMSC")
    public MapperScannerConfigurer defaultMapperScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setBasePackage("com.pinduoduo.mountain.repository.clickhouse.pdb_log_repository");
        mapperScannerConfigurer.setSqlSessionFactoryBeanName("pdbLogRepositorySF");
        return mapperScannerConfigurer;
    }

    @Bean(name = "slowSqlMSC")
    public MapperScannerConfigurer slowSqlDynamicMSC() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setBasePackage("com.pinduoduo.mountain.repository.clickhouse.mountain_backend");
        mapperScannerConfigurer.setSqlSessionFactoryBeanName("slowSqlSF");
        return mapperScannerConfigurer;
    }

    // ####################################################################################################
    // #################################### PlatformTransactionManager ####################################
    // ####################################################################################################

    @Bean(name = "pdbLogRepositoryPTM")
    public PlatformTransactionManager getMountainTransactionManager(
            @Qualifier("pdbLogRepositoryDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "slowSqlPTM")
    public PlatformTransactionManager slowSqlDynamicPTM(
            @Qualifier("slowSqlDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}