package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Explain;
import com.pinduoduo.mountain.repository.mysql.informationschema.entity.ShowCreateTable;
import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Tables;
import com.pinduoduo.mountain.repository.mysql.informationschema.sqlprovider.TablesSqlProvider;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
@Transactional(rollbackFor = Exception.class)
public interface TablesMapper {

    /**
     * 查询所有的业务表
     *
     * @param database 数据库名
     * @return 表名列表
     */
    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = #{database} AND TABLE_TYPE = 'BASE TABLE'" +
            " AND TABLE_NAME NOT LIKE 'taishan\\_%'" +
            " AND TABLE_NAME NOT LIKE '\\_%\\_del'" +
            " AND TABLE_NAME NOT LIKE '\\_%\\_del\\_bak'" +
            " AND TABLE_NAME NOT LIKE 'zz\\_\\_%\\_gho'" +
            " AND TABLE_NAME NOT LIKE 'zz\\_\\_%\\_del'" +
            " AND TABLE_NAME NOT LIKE '\\_%\\_ghc'" +
            " AND TABLE_NAME NOT IN ('zebra_distributed_sequence','taishan_zebra_distributed_sequence', 'zebra_distributed_sequence_metadata', 'taishan_zebra_distributed_sequence_metadata');")
    List<String> selectBusinessTableListByDatabase(String database);


    /**
     * 查询所有的表，上面是查找所有的业务表，不太一样
     *
     * @return 表名列表
     */
    @Select("SHOW TABLES;")
    List<String> selectAllTableListByDatabase();

    /**
     * 根据逻辑表查询一个物理库中的所有的物理表
     *
     * @param database       数据库名
     * @param logicTableName 逻辑表名
     * @return 表名列表
     */
    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = #{database} AND TABLE_NAME REGEXP CONCAT('^', #{logicTableName}, '[-_]?[0-9]*$')")
    List<String> selectTableListByPhysicalDatabaseLogicTableName(String database, String logicTableName);

    /**
     * 根据物理库和逻辑表查询一个物理库中的所有的物理表和对应的 taishan 表，匹配物理表的方式为 ...[0-9]+
     *
     * @param database       数据库名
     * @param logicTableName 逻辑表名
     * @return 表名列表
     */
    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = #{database} AND (TABLE_NAME REGEXP CONCAT('^', #{logicTableName}, '[-_]?[0-9]+$') OR TABLE_NAME REGEXP CONCAT('^taishan_', #{logicTableName}, '[-_]?[0-9]+$'))")
    List<String> selectTableListAndTaishanListByPhyDatabaseAndLogicTableNameOfDrop(String database, String logicTableName);

    /**
     * 根据物理库和逻辑表查询一个物理库中的所有的物理表和对应的 taishan 表，匹配物理表的方式为 ...[0-9]*
     *
     * @param database       数据库名
     * @param logicTableName 逻辑表名
     * @return 表名列表
     */
    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = #{database} AND (TABLE_NAME REGEXP CONCAT('^', #{logicTableName}, '[-_]?[0-9]*$') OR TABLE_NAME REGEXP CONCAT('^taishan_', #{logicTableName}, '[-_]?[0-9]*$'))")
    List<String> selectTableListAndTaishanListByPhyDatabaseAndLogicTableNameOfAlter(String database, String logicTableName);

    /**
     * 根据逻辑表查询一个物理库中的一个抽样物理表
     *
     * @param database       数据库名
     * @param logicTableName 逻辑表名
     * @return 表名列表
     */
    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = #{database} AND TABLE_NAME REGEXP CONCAT('^', #{logicTableName}, '[-_]?[0-9]*$') LIMIT 1")
    String selectOneTableByPhysicalDatabaseLogicTableName(String database, String logicTableName);

    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = #{database} AND TABLE_NAME REGEXP CONCAT('^', #{logicTableName}, '[-_]?[0-9]*$')")
    List<String> selectPhyTableListByPhysicalDatabaseLogicTableName(String database, String logicTableName);

    /**
     * 根据逻辑库和逻辑表查询所有物理表的表名
     *
     * @param logicDatabase 逻辑库名
     * @param logicTable    逻辑表名
     * @return 表名列表
     */
    @Select("SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA REGEXP CONCAT('^', #{logicDatabase}, '[-_]?[0-9]*$') AND TABLE_NAME REGEXP CONCAT('^', #{logicTable}, '[-_]?[0-9]*$')")
    List<String> selectTableListByLogicDatabaseAndLogicTable(String logicDatabase, String logicTable);

    /**
     * 根据逻辑库和逻辑表查询所有物理表的表信息
     *
     * @param database 逻辑库名
     * @param table    逻辑表名
     * @return 表信息列表
     */
    @Select("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA REGEXP CONCAT('^', #{database}, '[-_]?[0-9]*$') AND TABLE_NAME REGEXP CONCAT('^', #{table}, '[-_]?[0-9]*$')")
    List<Tables> selectByLogicDatabaseNameLogicTableName(String database, String table);

    @Select("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA = #{database} AND TABLE_NAME REGEXP CONCAT('^', #{table}, '[-_]?[0-9]*$')")
    List<Tables> selectByPhysicalDatabaseNameLogicTableName(String database, String table);

    /**
     * 根据物理库和物理表查询指定物理表的表信息
     *
     * @param database 逻辑库名
     * @param table    逻辑表名
     * @return 表信息列表
     */
    @Select("SELECT * FROM information_schema.TABLES WHERE TABLE_SCHEMA = #{database} AND TABLE_NAME = #{table} LIMIT 1")
    Tables selectByPhysicalDatabaseNamePhysicalTableName(String database, String table);

    /**
     * 查询一个表的建表语句
     *
     * @param database 数据库名
     * @param table    表名
     * @return 建表语句
     */
    @Select("SHOW CREATE TABLE `${database}`.`${table}`")
    @Results({
            @Result(property = "table", column = "Table"),
            @Result(property = "createTable", column = "Create Table")
    })
    ShowCreateTable showCreateTable(String database, String table);

    /**
     * @param tableName
     * @return 精确匹配的表名
     */
    @Select("SHOW TABLES LIKE #{tableName}")
    String showTablesExactMatchTableName(String tableName);

    /**
     * @param tableName
     * @return 模糊匹配的表名
     */
    @Select("SHOW TABLES LIKE '%#{tableName}%'")
    List<String> showTablesLikeTableName(String tableName);

    /**
     * @return 返回客户端ip，用户，和访问DB
     */
    @Select("select distinct substring_index(host, ':', 1) host, `user`, db from PROCESSLIST where `user` not in ('mountain_user', 'system user', 'mountain_ro', 'dbadmin', 'event_scheduler', 'unauthenticated user', '_root', 'tencentroot', 'mysqlsync', '_dbrs_user', 'dbha_user', 'repl')")
    List<Map<String, String>> selectClientList();

    @Select("explain ${sql}")
    List<Explain> showExplain(String sql);

    /**
     * @see TablesSqlProvider#selectPageAllTableOfPhysical
     */
    @SelectProvider(type = TablesSqlProvider.class, method = "selectPageAllTableOfPhysical")
    List<Tables> selectPageAllTableOfPhysical(String physicalDb, String orderBy, String sortType, String searchVal);

    /**
     * @see TablesSqlProvider#selectPageAllTableOfLogic
     */
    @SelectProvider(type = TablesSqlProvider.class, method = "selectPageAllTableOfLogic")
    List<Tables> selectPageAllTableOfLogic(String logicDb, String orderBy, String sortType, String searchVal);


    /**
     * @see TablesSqlProvider#selectPageBusinessTableOfPhysical
     */
    @SelectProvider(type = TablesSqlProvider.class, method = "selectPageBusinessTableOfPhysical")
    List<Tables> selectPageBusinessTableOfPhysical(String physicalDb, String orderBy, String sortType, String searchVal);

    /**
     * @see TablesSqlProvider#selectPageBusinessTableOfLogic
     */
    @SelectProvider(type = TablesSqlProvider.class, method = "selectPageBusinessTableOfLogic")
    List<Tables> selectPageBusinessTableOfLogic(String logicDb, String orderBy, String sortType, String searchVal);

    /**
     * @see TablesSqlProvider#selectPageTempTableOfLogic
     */
    @SelectProvider(type = TablesSqlProvider.class, method = "selectPageTempTableOfLogic")
    List<Tables> selectPageTempTableOfLogic(String logicDb, String orderBy, String sortType, String searchVal);

    /**
     * @see TablesSqlProvider#selectPageTempTableOfPhysical
     */
    @SelectProvider(type = TablesSqlProvider.class, method = "selectPageTempTableOfPhysical")
    List<Tables> selectPageTempTableOfPhysical(String physicalDb, String orderBy, String sortType, String searchVal);
}
