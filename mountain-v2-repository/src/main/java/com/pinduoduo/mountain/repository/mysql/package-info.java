/**
 * mysql 数据访问层
 * 一些注意事项：
 * <p>
 * 1. 注解中的 sql 语句，末尾尽量不要加分号，否则使用分页插件时会报错，虽然不一定会用分页插件，但是不加分号也不会有什么问题，所以尽量不要加分号
 * 例如： @Select("SELECT * FROM instance WHERE instance_id = #{instanceId}") 而不是 @Select("SELECT * FROM instance WHERE instance_id = #{instanceId};")
 * <p>
 * 2. 注解中的 sql 语句，一般情况下推荐使用 #{xxx} 形式，而不是 ${xxx} 形式，#{xxx} 形式会对 sql 进行预编译，防止 sql 注入，而 ${xxx} 是直接将 xxx 拼接到 sql 中
 */
package com.pinduoduo.mountain.repository.mysql;