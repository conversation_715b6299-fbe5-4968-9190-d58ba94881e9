package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
public interface SchemataMapper {
    /**
     * 查询所有的业务库
     *
     * @return 库名列表
     */
    @Select("SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME NOT IN ('sys', 'mysql', 'information_schema', 'performance_schema', 'dbmonitor', '__dbtrash_recyclebin'," +
            " 'retl', '__cdb_recycle_bin__', 'baidu_dba', '__tencentdb__')" +
            " AND SCHEMA_NAME NOT LIKE '_cdbcksum%'")
    List<String> selectBusinessDatabaseList();

    /**
     * 根据逻辑库查询所有的物理库，* 正则匹配，会匹配到不以数字结尾的物理库
     *
     * @param logicDbName 逻辑库名
     * @return 库名列表
     */
    @Select("SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME REGEXP CONCAT('^', #{logicDbName}, '[-_]?[0-9]*$')")
    List<String> selectDatabaseListByLogicDbNameOfStar(String logicDbName);

    /**
     * 根据逻辑库查询所有的物理库，+ 正则匹配，只匹配到以数字结尾的物理库
     *
     * @param logicDbName 逻辑库名
     * @return 库名列表
     */
    @Select("SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME REGEXP CONCAT('^', #{logicDbName}, '[-_]?[0-9]+$')")
    List<String> selectDatabaseListByLogicDbNameOfPlus(String logicDbName);

    /**
     * 根据逻辑库查询抽样的一个物理库
     *
     * @param logicDbName 逻辑库名
     * @return 库名
     */
    @Select("SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME REGEXP CONCAT('^', #{logicDbName}, '[-_]?[0-9]*$') LIMIT 1")
    String selectOneDatabaseByLogicDbName(String logicDbName);

    /**
     * 创建数据库
     *
     * @param database 数据库名
     */
    @Select("CREATE DATABASE IF NOT EXISTS `${database}`")
    void createDatabase(String database);
}
