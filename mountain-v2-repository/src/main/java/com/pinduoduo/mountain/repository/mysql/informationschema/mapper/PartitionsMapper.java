package com.pinduoduo.mountain.repository.mysql.informationschema.mapper;

import com.pinduoduo.mountain.repository.mysql.informationschema.entity.Partitions;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Mapper
@Transactional(rollbackFor = Exception.class)
public interface PartitionsMapper {

    /**
     * 查询表的分区信息
     */
    @Select("SELECT * FROM information_schema.PARTITIONS WHERE table_schema = #{tableSchema} AND table_name = #{tableName}")
    List<Partitions> selectByTableSchemaAndTableName(String tableSchema, String tableName);

}
