package com.pinduoduo.mountain.repository.mysql;

import com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer;
import com.dianping.zebra.zebrette.ZebretteDataSourceFactory;
import com.pinduoduo.mountain.common.bean.LeoConfiguration;
import com.pinduoduo.mountain.common.config.DataSourceName;
import com.pinduoduo.mountain.common.config.Keychain;
import com.pinduoduo.mountain.common.constant.DataSourceConstant;
import com.pinduoduo.mountain.common.model.platform.DataSourceNameConfig;
import com.pinduoduo.mountain.repository.util.DaoCommon;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.HashMap;

/**
 * mysql 数据源配置
 * 依赖 {@link LeoConfiguration} 先初始化之后才能进行初始化
 * 一个 DataSource 对应 mysql 中一个实例的库，需要添加新的 mysql 数据源（数据库）的步骤：
 * 1、创建 DataSource 的 bean
 * 2、创建 SqlSessionFactoryBean 的 bean
 * 3、创建 ZebraMapperScannerConfigurer bean，<i><b><u>这里会指定 mapper 方法的包路径，这里如果定义错了的话，mapper 方法就无法正确查询对应的库</b></i></u>
 * 4、创建 PlatformTransactionManager 的 bean
 * 5、在 com.pinduoduo.mountain.repository.mysql 包下创建对应库的 package，定义 entity 和 mapper，编写 mapper 方法即可
 *
 * <AUTHOR>
 * @see com.pinduoduo.mountain.common.bean.LeoConfiguration
 */
@Configuration
@Order(2)
@EnableTransactionManagement
@ComponentScan(value = "com.pinduoduo.mountain.repository.mysql")
@Slf4j
public class DataSourceConfigurationMySql {

    // ####################################################################################
    // #################################### DataSource ####################################
    // ####################################################################################

    /**
     * mountain 集群+逻辑元数据新数据库
     */
    @Primary
    @Bean(name = "mountainDS")
    public DataSource getMountainDataSource() {
        return getDataSource(DataSourceName.MOUNTAIN_DSN_CONFIG, "getMountainDataSource by dsn failed, use keychain, exception: ", Keychain.MOUNTAIN_KEYCHAIN);
    }

    @Bean(name = "pddRiver")
    public DataSource getPddRiverDataSource() {
        return getDataSource(DataSourceName.PDD_RIVER_DSN_CONFIG, "getPddRiverDataSource by dsn failed, use keychain, exception: ", Keychain.PDD_RIVER_KEYCHAIN);
    }

    @Bean(name = "pddMountain")
    public DataSource getPddMountainDataSource() {
        return getDataSource(DataSourceName.PDD_MOUNTAIN_DSN_CONFIG, "getPddMountainDataSource by dsn failed, use keychain, exception: ", Keychain.PDD_MOUNTAIN_KEYCHAIN);
    }

    @Bean(name = "cdbMonitorDS")
    public DataSource getCdbMonitorDataSource() {
        return getDataSource(DataSourceName.CDBMONITOR_DSN_CONFIG, "getCdbMonitorDataSource by dsn failed, use keychain, exception: ", Keychain.CDBMONITOR_KEYCHAIN);
    }

    @Bean(name = "cdbMonitorOfRtDS")
    public DataSource getCdbMonitorOfRtDataSource() {
        return getDataSource(DataSourceName.CDBMONITOROFRT_DSN_CONFIG, "getCdbMonitorOfRtDataSource by dsn failed, use keychain, exception: ", "");
    }

    @Bean(name = "dbstatisticsDS")
    public DataSource getDbStatisticsDataSource() {
        return getDataSource(DataSourceName.DBSTATISTICS_DSN_CONFIG, "getDbStatisticsDataSource by dsn failed, use keychain, exception: ", Keychain.DBSTATISTICS_KEYCHAIN);
    }

    @Bean(name = "archiveNewDS")
    public DataSource getArchiveNewDataSource() {
        return getDataSource(DataSourceName.ARCHIVE_NEW_DSN_CONFIG, "getArchiveNewDataSource by dsn failed, use keychain, exception: ", Keychain.ARCHIVE_NEW_KEYCHAIN);
    }

    @Bean(name = "mountainFailoverDS")
    public DataSource getMountainFailoverDataSource() {
        return getDataSource(DataSourceName.MOUNTAIN_FAILOVER_DSN_CONFIG, "getMountainFailoverDataSource by dsn failed, use keychain, exception: ", Keychain.MOUNTAIN_FAILOVER_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor0DS")
    public DataSource getPfsMonitor0DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_0_DSN_CONFIG, "getPfsMonitor0DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_0_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor1DS")
    public DataSource getPfsMonitor1DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_1_DSN_CONFIG, "getPfsMonitor1DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_1_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor2DS")
    public DataSource getPfsMonitor2DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_2_DSN_CONFIG, "getPfsMonitor2DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_2_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor3DS")
    public DataSource getPfsMonitor3DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_3_DSN_CONFIG, "getPfsMonitor3DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_3_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor4DS")
    public DataSource getPfsMonitor4DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_4_DSN_CONFIG, "getPfsMonitor4DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_4_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor5DS")
    public DataSource getPfsMonitor5DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_5_DSN_CONFIG, "getPfsMonitor5DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_5_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor6DS")
    public DataSource getPfsMonitor6DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_6_DSN_CONFIG, "getPfsMonitor6DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_6_KEYCHAIN);
    }

    @Bean(name = "pfsMonitor7DS")
    public DataSource getPfsMonitor7DataSource() {
        return getDataSource(DataSourceName.PFSMONITOR_7_DSN_CONFIG, "getPfsMonitor7DataSource by dsn failed, use keychain, exception: ", Keychain.PFSMONITOR_7_KEYCHAIN);
    }

    /**
     * pfsmonitordb_0~7 个分库并不是严格意义上的分库，只能使用这种动态数据源的方式来控制访问的分库，不能使用 zebra 的 ShardDataSource
     */
    @Bean(name = "pfsDynamicDS")
    public DataSource pfsDynamicDataSource(
            @Qualifier("pfsMonitor0DS") DataSource pfsMonitor0Ds, @Qualifier("pfsMonitor1DS") DataSource pfsMonitor1Ds,
            @Qualifier("pfsMonitor2DS") DataSource pfsMonitor2Ds, @Qualifier("pfsMonitor3DS") DataSource pfsMonitor3Ds,
            @Qualifier("pfsMonitor4DS") DataSource pfsMonitor4Ds, @Qualifier("pfsMonitor5DS") DataSource pfsMonitor5Ds,
            @Qualifier("pfsMonitor6DS") DataSource pfsMonitor6Ds, @Qualifier("pfsMonitor7DS") DataSource pfsMonitor7Ds
    ) {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setDefaultTargetDataSource(pfsMonitor0Ds);
        dynamicDataSource.setTargetDataSources(new HashMap<Object, Object>(8) {{
            put("pfsMonitor0DS", pfsMonitor0Ds);
            put("pfsMonitor1DS", pfsMonitor1Ds);
            put("pfsMonitor2DS", pfsMonitor2Ds);
            put("pfsMonitor3DS", pfsMonitor3Ds);
            put("pfsMonitor4DS", pfsMonitor4Ds);
            put("pfsMonitor5DS", pfsMonitor5Ds);
            put("pfsMonitor6DS", pfsMonitor6Ds);
            put("pfsMonitor7DS", pfsMonitor7Ds);
        }});
        return dynamicDataSource;
    }

    @Bean(name = "ghostDbDS")
    public DataSource ghostDbDataSource() {
        return getDataSource(DataSourceName.GHOST_DB_DSN_CONFIG, "ghostDbDataSource by dsn failed, use keychain ,exception: ", Keychain.GHOST_DB_KEYCHAIN);
    }

    @Bean(name = "ddlLogDS")
    public DataSource ddlLogDataSource() {
        return getDataSource(DataSourceName.DDL_LOG_DSN_CONFIG, "ddlLogDataSource by dsn failed, use keychain ,exception: ", Keychain.DDL_LOG_KEYCHAIN);
    }

    @Bean(name = "longTransactionDS")
    public DataSource getLongTransactionDataSource() {
        return getDataSource(DataSourceName.LONG_TRANSACTION_DSN_CONFIG, "getLongTransactionDataSource by dsn failed, use keychain, exception: ", "");
    }

    // ###############################################################################################
    // #################################### SqlSessionFactoryBean ####################################
    // ###############################################################################################

    @Primary
    @Bean(name = "mountainSF")
    public SqlSessionFactoryBean mountainSqlSessionFactoryBean(@Qualifier("mountainDS") DataSource dataSource) throws IOException {
        SqlSessionFactoryBean sqlSessionFactoryBean = DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
        sqlSessionFactoryBean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:sqlmap/*.xml")
        );
        return sqlSessionFactoryBean;
    }

    @Bean(name = "pddRiverSF")
    public SqlSessionFactoryBean pddRiverSqlSessionFactoryBean(@Qualifier("pddRiver") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "pddMountainSF")
    public SqlSessionFactoryBean pddMountainSqlSessionFactoryBean(@Qualifier("pddMountain") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "cdbMonitorSF")
    public SqlSessionFactoryBean cdbMonitorSqlSessionFactoryBean(@Qualifier("cdbMonitorDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "cdbMonitorOfRtSF")
    public SqlSessionFactoryBean cdbMonitorOfRtSqlSessionFactoryBean(@Qualifier("cdbMonitorOfRtDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "dbstatisticsSF")
    public SqlSessionFactoryBean dbStatisticsSqlSessionFactoryBean(
            @Qualifier("dbstatisticsDS") DataSource dataSource
    ) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "archiveNewSF")
    public SqlSessionFactoryBean archiveNewSqlSessionFactoryBean(@Qualifier("archiveNewDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "mountainFailoverSF")
    public SqlSessionFactoryBean mountainFailoverSqlSessionFactoryBean(@Qualifier("mountainFailoverDS") DataSource dataSource) throws IOException {
        SqlSessionFactoryBean sqlSessionFactoryBean = DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
        return sqlSessionFactoryBean;
    }

    @Bean(name = "pfsDynamicSF")
    public SqlSessionFactoryBean pfsMonitorSqlSessionFactoryBean(@Qualifier("pfsDynamicDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "ghostDbSF")
    public SqlSessionFactoryBean ghostDbSqlSessionFactoryBean(@Qualifier("ghostDbDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "ddlLogSF")
    public SqlSessionFactoryBean ddlLogSqlSessionFactoryBean(@Qualifier("ddlLogDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    @Bean(name = "longTransactionSF")
    public SqlSessionFactoryBean getLongTransactionSqlSessionFactoryBean(@Qualifier("longTransactionDS") DataSource dataSource) {
        return DaoCommon.getSqlSessionFactoryBean(dataSource, DataSourceConstant.MYSQL, false);
    }

    // ######################################################################################################
    // #################################### ZebraMapperScannerConfigurer ####################################
    // ######################################################################################################

    /**
     * 这个 Bean 的作用是将 CAT 上 SQL 打点聚合到接口粒度
     * 如果没有这个 Bean，CAT 上 SQL 打点将变为原始 SQL 粒度，超过 200 个 SQL 就很难展示了
     * 添加这个 Bean 以后，不能使用 Mybatis 的 @MapperScan 注解，否则 Bean 将失效
     * 该 Bean 只支持原生 Mybatis，不支持 Tkmybatis 或者 Mybatis-plus 之类的增强插件
     */
    @Primary
    @Bean
    public ZebraMapperScannerConfigurer mountainZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("mountainSF", "com.pinduoduo.mountain.repository.mysql.mountain");
    }

    @Bean
    public ZebraMapperScannerConfigurer pddRiverZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("pddRiverSF", "com.pinduoduo.mountain.repository.mysql.pddriver");
    }

    @Bean
    public ZebraMapperScannerConfigurer pddMountainZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("pddMountainSF", "com.pinduoduo.mountain.repository.mysql.pddmountain");
    }

    @Bean
    public ZebraMapperScannerConfigurer cdbMonitorZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("cdbMonitorSF", "com.pinduoduo.mountain.repository.mysql.cdbmonitor");
    }

    @Bean
    public ZebraMapperScannerConfigurer cdbMonitorOfRtZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("cdbMonitorOfRtSF", "com.pinduoduo.mountain.repository.mysql.cdbmonitorofrt");
    }

    @Bean
    public ZebraMapperScannerConfigurer dbStatisticsZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("dbstatisticsSF", "com.pinduoduo.mountain.repository.mysql.dbstatistics");
    }

    @Bean
    public ZebraMapperScannerConfigurer archiveNewZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("archiveNewSF", "com.pinduoduo.mountain.repository.mysql.archivenew");
    }

    @Bean
    public ZebraMapperScannerConfigurer mountainFailoverZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("mountainFailoverSF", "com.pinduoduo.mountain.repository.mysql.mountainfailover");
    }

    @Bean
    public ZebraMapperScannerConfigurer pfsMonitorZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("pfsDynamicSF", "com.pinduoduo.mountain.repository.mysql.pfsmonitor");
    }

    @Bean
    public ZebraMapperScannerConfigurer ghostDbMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("ghostDbSF", "com.pinduoduo.mountain.repository.mysql.ghostdb");
    }

    @Bean
    public ZebraMapperScannerConfigurer ddlLogZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("ddlLogSF", "com.pinduoduo.mountain.repository.mysql.ddllog");
    }

    @Bean
    public ZebraMapperScannerConfigurer longTransactionZebraMapperScannerConfigurer() {
        return getZebraMapperScannerConfigurer("longTransactionSF", "com.pinduoduo.mountain.repository.mysql.longtransaction");
    }

    // ####################################################################################################
    // #################################### PlatformTransactionManager ####################################
    // ####################################################################################################

    @Primary
    @Bean(name = "mountainPTM")
    public PlatformTransactionManager getMountainTransactionManager(@Qualifier("mountainDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "pddRiverPTM")
    public PlatformTransactionManager getPddRiverTransactionManager(@Qualifier("pddRiver") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "pddMountainPTM")
    public PlatformTransactionManager getPddMountainTransactionManager(@Qualifier("pddMountain") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cdbMonitorPTM")
    public PlatformTransactionManager getCdbMonitorTransactionManager(
            @Qualifier("cdbMonitorDS") DataSource dataSource
    ) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "cdbMonitorOfRtPTM")
    public PlatformTransactionManager getCdbMonitorOfRtTransactionManager(
            @Qualifier("cdbMonitorOfRtDS") DataSource dataSource
    ) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "dbStatisticsPTM")
    public PlatformTransactionManager getDbStatisticsTransactionManager(
            @Qualifier("dbstatisticsDS") DataSource dataSource
    ) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "archiveNewPTM")
    public PlatformTransactionManager getArchiveNewTransactionManager(
            @Qualifier("archiveNewDS") DataSource dataSource
    ) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "mountainFailoverPTM")
    public PlatformTransactionManager getMountainFailoverTransactionManager(
            @Qualifier("mountainFailoverDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "pfsDynamicPTM")
    public PlatformTransactionManager getPfsMonitorTransactionManager(
            @Qualifier("pfsDynamicDS") DataSource dataSource
    ) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "ghostDbPTM")
    public PlatformTransactionManager ghostDbTransactionManager(@Qualifier("ghostDbDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "ddlLogPTM")
    public PlatformTransactionManager getDdlLogTransactionManager(@Qualifier("ddlLogDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "longTransactionPTM")
    public PlatformTransactionManager getLongTransactionTransactionManager(@Qualifier("longTransactionDS") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    // #######################################################################################
    // #################################### 其他 UTIL 方法 ####################################
    // #######################################################################################

    private ZebraMapperScannerConfigurer getZebraMapperScannerConfigurer(String beanName, String packageName) {
        ZebraMapperScannerConfigurer configurer = new ZebraMapperScannerConfigurer();
        configurer.setSqlSessionFactoryBeanName(beanName);
        configurer.setBasePackage(packageName);
        log.info(String.format("getZebraMapperScannerConfigurer: [%s] [%s]", beanName, packageName));
        return configurer;
    }

    private DataSource getDataSource(DataSourceNameConfig dataSourceNameConfig, String errorMsg, String keychain) {
        try {
            HikariConfig config = dataSourceNameConfig.getHikariConfig(DataSourceConstant.MYSQL);
            return new HikariDataSource(config);
        } catch (Exception e) {
            log.error(errorMsg, e);
            return ZebretteDataSourceFactory.getDataSource(keychain);
        }
    }

}

