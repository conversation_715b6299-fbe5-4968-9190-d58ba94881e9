package com.pinduoduo.mountain.repository.clickhouse.sqlprovider;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;

public class CoreProcessListCollectMapperSqlProvider {
    public String selectProcessListTs(String srcIp, String srcPort, String period, Date beginTime, Date endTime) {
        String sql = " select count(1) as value, toString(%s(collect_create_time)) as time " +
                " from core_processlist_collect_local_new" +
                " where source_ip = '%s' " +
                "  and source_port = '%s' " +
                "  and collect_create_time >= '%s' " +
                "  and collect_create_time < '%s' " +
                "  and user <> 'dbmonitor_user' " +
                "  group by time order by time";
        return String.format(sql, period, srcIp, srcPort,
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(beginTime),
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(endTime));
    }

    public String selectProcessListTotalCnt(String srcIp, String srcPort, Date beginTime, Date endTime) {
        String sql = "select count(*) as totalcnt from ( " +
                " select collect_create_time as ct, count(collect_create_time) as cnt " +
                " from core_processlist_collect_local_new " +
                " where source_ip = '%s' " +
                " and source_port = '%s' " +
                " and collect_create_time >= '%s' " +
                " and collect_create_time < '%s' " +
                " group by collect_create_time ) t";
        return String.format(sql, srcIp, srcPort,
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(beginTime),
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(endTime));
    }


    public String selectProcessListStats(String srcIp, String srcPort, Date beginTime, Date endTime, Long offset, Integer cnt) {
        String sql = " select collect_create_time as time, count(collect_create_time) as value " +
                " from core_processlist_collect_local_new " +
                " where source_ip = '%s' " +
                " and source_port = '%s' " +
                " and collect_create_time >= '%s' " +
                " and collect_create_time < '%s' " +
                " group by collect_create_time " +
                " order by collect_create_time desc limit %s, %s";
        return String.format(sql, srcIp, srcPort,
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(beginTime),
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(endTime),
                offset, cnt);
    }

    public String selectProcessListSqlDetail(String srcIp, String srcPort, Long fingerprint) {
        String sql = " select source_ip,db,INFO,source_sql " +
                " from core_processlist_collect_local_new " +
                " where source_ip= '%s' and source_port= '%s' and crc32= '%s' limit 1;";
        return String.format(sql, srcIp, srcPort, fingerprint);
    }

    public String getProcessListCollectTotal(String srcIp, String srcPort, Date beginTime, Date endTime) {
        String sql = " select count(*) as cnt " +
                " from core_processlist_collect_local_new " +
                " where source_ip = '%s' " +
                " and source_port = '%s' " +
                " and collect_create_time >= '%s' " +
                " and collect_create_time < '%s' ";
        return String.format(sql, srcIp, srcPort,
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(beginTime),
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(endTime));
    }

    public String getProcessListData(String srcIp, String srcPort, Date beginTime, Date endTime, Long offset, Integer cnt) {
        String sql = "select source_sql,pid,instance_name,create_time,host,user,db,COMMAND,TIME,STATE,crc32 " +
                " from core_processlist_collect_local_new" +
                " where source_ip = '%s' " +
                " and source_port = '%s' " +
                " and collect_create_time >='%s' " +
                " and collect_create_time <'%s' " +
                " order by create_time asc " +
                " limit %s, %s ";
        return String.format(sql, srcIp, srcPort,
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(beginTime),
                DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(endTime),
                offset, cnt);
    }
}
