package com.pinduoduo.mountain.api.controller.operationmanage;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.MysqldParameterConfig;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.ParameterModifyLog;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.ClusterMapper;
import com.pinduoduo.mountain.service.metadata.dto.InstanceNewBaseInfoDTO;
import com.pinduoduo.mountain.service.operationmanage.dto.InstanceVariableValueDTO;
import com.pinduoduo.mountain.service.operationmanage.dto.LogicDatabaseInstanceInfoDTO;
import com.pinduoduo.mountain.service.operationmanage.dto.MysqlVariablesModifyDTO;
import com.pinduoduo.mountain.service.operationmanage.impl.MysqlParameterManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("mysql实例参数管理相关接口")
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v2/mysqlParameterManage")
public class MysqlParameterManageController {
    private final ClusterMapper clusterMapper;

    private final MysqlParameterManageService mysqlParameterManageService;

    public MysqlParameterManageController(ClusterMapper clusterMapper, MysqlParameterManageService mysqlParameterManageService) {
        this.clusterMapper = clusterMapper;
        this.mysqlParameterManageService = mysqlParameterManageService;
    }


    @GetMapping("/instanceListFromClusterTopo")
    @ApiOperation("从逻辑库/集群维度根据筛选的字段获取实例列表")
    public BaseResponse<PageResult<LogicDatabaseInstanceInfoDTO>> getInstanceListFromCluster(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("业务线中文名称") @RequestParam(value = "business", required = false, defaultValue = "") String business,
            @ApiParam("服务名") @RequestParam(value = "service", required = false, defaultValue = "") String service,
            @ApiParam("搜索关键字，搜索字段 logic_db_name") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal
    ) {
        try {
            PageResult<LogicDatabaseInstanceInfoDTO> result = mysqlParameterManageService.getLogicDatabaseInstanceList(username, env, pageNum, pageSize, business, service, searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("instanceListFromClusterTopo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/instanceList")
    @ApiOperation("直接从实例维度查询实例信息")
    public BaseResponse<PageResult<InstanceNewBaseInfoDTO>> getInstanceListFromInstance(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("业务线中文名称") @RequestParam(value = "business", required = false, defaultValue = "") String business,
            @ApiParam("服务名") @RequestParam(value = "service", required = false, defaultValue = "") String service,
            @ApiParam("云环境：腾讯云：cdb,百度云:bdb,自建云：pdb,多多云：isv") @RequestParam(value = "cloud", required = false, defaultValue = "") String cloud,
            @ApiParam("region：ap-beijing/ap-shanghai/ap-guangzhou") @RequestParam(value = "region", required = false, defaultValue = "") String region,
            @ApiParam(value = "搜索关键词，支持传实例id/ip/实例名", required = true) @RequestParam(value = "searchValue",required = false,defaultValue = "") String searchValue
    ) {
        try {
            PageResult<InstanceNewBaseInfoDTO> result = mysqlParameterManageService.getSearchInstances(searchValue,env,business,service,cloud,region,pageNum,pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getInstanceListFromInstance error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/instanceParameterList")
    @ApiOperation("获取实例的参数列表")
    public BaseResponse<List<MysqldParameterConfig>> instanceParameterList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例id", required = true) @RequestParam(value = "instanceId") String instanceId
    ) {
        try {
            List<MysqldParameterConfig> result = mysqlParameterManageService.getInstanceParameter(instanceId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("instanceParameterList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/getParamListByVersionAndReboot")
    @ApiOperation("获取某个版本的MySQL实例的参数列表")
    public BaseResponse<List<MysqldParameterConfig>> getParameterListByVersionAndReboot(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "dbVersion", required = true) @RequestParam(value = "dbVersion",defaultValue = "5.7") String dbVersion,
            @ApiParam(value = "needReboot", required = true) @RequestParam(value = "needReboot",defaultValue = "0") String needReboot
    ) {
        try {
            List<MysqldParameterConfig> result = mysqlParameterManageService.getParameterListByVersionAndReboot(dbVersion, needReboot);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getParameterListByVersionAndReboot error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/getParameterValue")
    @ApiOperation("获取某一批实例的某个参数的值")
    public BaseResponse<List<InstanceVariableValueDTO>> getParameterValue(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "instanceIds", required = true) @RequestParam(value = "instanceIds",required = true) String instanceIds,
            @ApiParam(value = "parameterName", required = true) @RequestParam(value = "parameterName",required = true) String parameterName
    ) {
        try {
            List<InstanceVariableValueDTO> result = mysqlParameterManageService.getInstanceListVariableValue(instanceIds, parameterName);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getParameterValue error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping ("/getModifyHistory")
    @ApiOperation("获取某些实例的参数修改历史")
    public BaseResponse<List<ParameterModifyLog>> getModifyHistory(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例id", required = true) @RequestBody List<String> instanceIdList
    ) {
        try {
            List<ParameterModifyLog> result = mysqlParameterManageService.getModifyHistory(instanceIdList);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getModifyHistory error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping ("/modifyInstanceParameter")
    @ApiOperation("同步方式修改单实例参数")
    public BaseResponse<String> modifyInstanceParameter(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "同步方式修改实例参数请求体，单实例传一个", required = true) @Validated @RequestBody MysqlVariablesModifyDTO mysqlVariablesModifyDTO
    ) {
        try {
            Pair<Boolean, String> result =  mysqlParameterManageService.modifyInstanceListVariables(mysqlVariablesModifyDTO,username);
            if(result.getLeft()){
                return BaseResponse.success("修改成功！");
            }else{
                return BaseResponse.fail(result.getRight());
            }
        } catch (Exception e) {
            log.error("modifyInstanceParameter error", e);
            return BaseResponse.fail(e);
        }
    }

}
