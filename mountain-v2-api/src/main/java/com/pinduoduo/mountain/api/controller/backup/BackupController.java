package com.pinduoduo.mountain.api.controller.backup;

import com.pinduoduo.mountain.api.dto.backup.BackupConfigDTO;
import com.pinduoduo.mountain.api.dto.backup.BackupDetailDTO;
import com.pinduoduo.mountain.api.dto.backup.LogicDatabaseBackupConfigDTO;
import com.pinduoduo.mountain.api.dto.backup.LogicDatabaseBackupDetailDTO;
import com.pinduoduo.mountain.api.service.backup.BackupService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("备份相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/backup")
public class BackupController {

    private final BackupService backupService;

    public BackupController(BackupService backupService) {
        this.backupService = backupService;
    }

    @GetMapping("/instanceBackupConfig")
    @ApiOperation("获取实例的备份策略")
    public BaseResponse<BackupConfigDTO> instanceBackupConfig(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username,
            @ApiParam(value = "实例 id", required = true) @RequestParam("pddId") String pddId
    ) {
        try {
            BackupConfigDTO result = backupService.instanceBackupConfig(pddId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("instanceBackupConfig error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/instanceBackupDetail")
    @ApiOperation("获取实例的备份详情")
    public BaseResponse<List<BackupDetailDTO>> instanceBackupDetail(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username,
            @ApiParam(value = "实例 id", required = true) @RequestParam("pddId") String pddId
    ) {
        try {
            List<BackupDetailDTO> result = backupService.instanceBackupDetail(pddId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("instanceBackupDetail error", e);
            return BaseResponse.fail(e);
        }
    }


    @ApiOperation("获取逻辑库的备份策略")
    @GetMapping("/logicDatabaseBackupConfig")
    public BaseResponse<List<LogicDatabaseBackupConfigDTO>> logicDatabaseBackupConfig(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username,
            @ApiParam(value = "逻辑库唯一 ID", required = true) @RequestParam("logicDbId") long logicDbId
    ) {
        try {
            List<LogicDatabaseBackupConfigDTO> result = backupService.logicDatabaseBackupConfig(logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicDatabaseBackupConfig error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取逻辑库的备份详情")
    @GetMapping("/logicDatabaseBackupDetail")
    public BaseResponse<List<LogicDatabaseBackupDetailDTO>> logicDatabaseBackupDetail(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username,
            @ApiParam(value = "逻辑库唯一 ID", required = true) @RequestParam("logicDbId") long logicDbId,
            @ApiParam(value = "开始时间", required = true) @RequestParam(value = "startTime") String startTime,
            @ApiParam(value = "结束时间", required = true) @RequestParam(value = "endTime") String endTime
    ) {
        try {
            List<LogicDatabaseBackupDetailDTO> result = backupService.logicDatabaseBackupDetail(logicDbId, startTime, endTime);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicDatabaseBackupConfig error", e);
            return BaseResponse.fail(e);
        }
    }
}
