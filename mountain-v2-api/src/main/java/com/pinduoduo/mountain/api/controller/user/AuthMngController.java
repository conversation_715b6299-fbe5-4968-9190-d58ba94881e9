package com.pinduoduo.mountain.api.controller.user;


import com.pinduoduo.mountain.api.controller.monitor.GenericResponse;
import com.pinduoduo.mountain.service.authmng.AuthMngService;
import com.pinduoduo.mountain.service.authmng.conf.AuthResourceType;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

@Api("权限相关操作")
@RestController
@RequestMapping("/api/v2/auth")
@Slf4j
public class AuthMngController {
    @Autowired
    private AuthMngService authMngService;

    @GetMapping("/permission/has")
    public GenericResponse defaultVersion(
            @RequestParam("userId") String userId,
            @RequestParam("resourceType") String resourceType,
            @RequestParam("resourceId") String resourceId,
            @RequestParam("operationName") String operationName,
            @RequestParam("env") String env
    ) {
        try {
            return new GenericResponse(true, authMngService.hasPermissionsInVerbose(userId, AuthResourceType.getAuthResourceType(resourceType), Arrays.asList(resourceId), operationName, env));
        } catch (Exception e) {
            log.error("defaultVersion,stack={}", e);
            return new GenericResponse(false, e.getMessage());
        }
    }
}
