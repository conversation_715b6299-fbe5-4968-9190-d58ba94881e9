package com.pinduoduo.mountain.api.controller.monitor.common;

import com.yiran.arch.leo.util.LeoUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.eclipse.jetty.util.ajax.JSON;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class TornadoServiceImpl implements TornadoService {
    private String appId;
    private String appKey;

    @PostConstruct
    public void init() {
        appId = LeoUtils.getStringProperty("mountain-v2-api.tornado_appkey");
        appKey = LeoUtils.getStringProperty("mountain-v2-api.tornado_appsecret");
    }

    @Override
    public Pair<Boolean, String> killTx(String instanceId, List<String> threadIds) {
        try {
            String host = LeoUtils.getStringProperty("mountain-v2-api.tornado_longtx_kill");
            long ts = System.currentTimeMillis() / TimeUnit.SECONDS.toMillis(1);
            String sig = DigestUtils.md5Hex(Long.toString(ts) + appKey);
            HttpPost httpPost = new HttpPost(host);
            httpPost.setHeader("sign", sig);
            httpPost.setHeader("logId", Long.toString(ts));
            httpPost.setHeader("appkey", appId);
            httpPost.setHeader("Content-Type", "application/json");

            Map<String, Object> params = new HashMap<>();
            params.put("instance_id", instanceId);

            List<Map<String, String>> threadid_list = new LinkedList<>();
            for (String threadId : threadIds) {
                Map<String, String> threadIdMap = new HashMap<>();
                threadIdMap.put("id", threadId);
                threadid_list.add(threadIdMap);
            }
            params.put("threadid", threadid_list);

            StringEntity entity = new StringEntity(JSON.toString(params));
            httpPost.setEntity(entity);

            CloseableHttpClient client = HttpClientBuilder.create().build();
            HttpResponse response = client.execute(new HttpHost(host), httpPost);
            String res = EntityUtils.toString(response.getEntity());

            return new ImmutablePair<>(true, res);

        } catch (Exception e) {
            return new ImmutablePair<>(false, e.getMessage());
        }
    }
}
