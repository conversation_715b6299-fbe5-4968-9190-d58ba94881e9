package com.pinduoduo.mountain.api.controller.tfapi;

import com.pinduoduo.mountain.api.dto.tfapi.TFStepReq;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.util.JSONUtils;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO;
import com.pinduoduo.mountain.service.monitor.GenericResponse;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFJob;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFStep;
import com.pinduoduo.mountain.service.teamflow.execute.TFJobService;
import com.pinduoduo.mountain.service.teamflow.execute.TFLoggerService;
import com.pinduoduo.mountain.service.teamflow.execute.TFStepService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

@Slf4j
@RestController
@RequestMapping("/api/v2/tf/job/step")
public class TFStepController {
	@Resource
	private TFStepService tfStepService;
	@Resource
	private TFJobService tfJobService;
	@Resource
	private TFLoggerService tfLoggerService;

	@GetMapping("/list")
	BaseResponse<List<TFStep>> list(@RequestParam("workId") Long workId,
			@RequestParam(value = "log", defaultValue = "true") Boolean withLog) {
		TFJob job = tfJobService.getJob(workId);
		if (job == null) {
			return BaseResponse.fail("任务(Job)还没有生成，请等待审批完成！");
		}
		List<TFStep> steps = tfStepService.getSteps(workId, null, 0, 10000);
		log.info("jobId={},stepSize={}", workId, CollectionUtils.size(steps));

		if (withLog) {
			List<TFStepLogDO> logs = tfLoggerService.getLogsByJobId(workId);

			Map<Long, List<TFStepLogDO>> logMap = logs.stream().collect(Collectors
					.groupingBy(TFStepLogDO::getStepId, Collectors.collectingAndThen(Collectors.toList(), list -> {
						// Output logs in reverse order by id
						list.sort(Comparator.comparing(TFStepLogDO::getId).reversed());
						return list;
					})));

			for (TFStep step : steps) {
				List<TFStepLogDO> logList = logMap.get(step.getStepId());
				if (CollectionUtils.isNotEmpty(logList)) {
					step.setLog(logList.stream().map(TFStepLogDO::getMsg).collect(Collectors.joining("\n")));
				}
			}
		}

		return BaseResponse.success(steps);
	}

	@GetMapping("/logs")
	BaseResponse<List<String>> logs(@RequestParam("workId") Long workId, @RequestParam("stepId") Long stepId,
	  @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
	  @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize) {
		List<String> logs = tfLoggerService.getLogs(workId, stepId, pageIndex, pageSize);
		log.info("logs,stepId={},logSize={}", stepId, CollectionUtils.size(logs));
		return BaseResponse.success(logs);
	}

	@GetMapping("/logs/cnt")
	BaseResponse<Long> logsCnt(@RequestParam("workId") Long workId, @RequestParam("stepId") Long stepId) {
		Long cnt = tfLoggerService.getLogsCnt(workId, stepId);
		log.info("logsCnt,stepId={},logSize={}", stepId, cnt);
		return BaseResponse.success(cnt);
	}

	@PostMapping("/retry")
	BaseResponse<String> retry(@RequestBody TFStepReq stepReq,@RequestHeader(HEADER_GATEWAY_USERNAME) String username) {
		try {
			Long workId = stepReq.getWorkId();
			Long stepId = stepReq.getStepId();
			Pair<Boolean, String> r = tfStepService.retryStep(workId, stepId, username);
			log.info("retry:stepId={},r={}", stepId, JSONUtils.toJSONString(r));
			return BaseResponse.success(r.getRight());
		} catch (Exception e) {
			return BaseResponse.fail(e.getMessage());
		}
	}

	@PostMapping("/skip")
	BaseResponse<String> skip(@RequestBody TFStepReq stepReq,@RequestHeader(HEADER_GATEWAY_USERNAME) String username) {
		try {
			Long workId = stepReq.getWorkId();
			Long stepId = stepReq.getStepId();
			Pair<Boolean, String> r = tfStepService.skipStep(workId, stepId, username);
			log.info("skip:jobId={},r={}", stepId, JSONUtils.toJSONString(r));
			return BaseResponse.success(r.getRight());
		} catch (Exception e) {
			return BaseResponse.fail(e.getMessage());
		}
	}
}
