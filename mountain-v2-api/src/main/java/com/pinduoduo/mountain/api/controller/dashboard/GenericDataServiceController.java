package com.pinduoduo.mountain.api.controller.dashboard;


import com.alibaba.fastjson.JSON;
import com.pinduoduo.mountain.api.controller.dashboard.service.GenericDataService;
import com.pinduoduo.mountain.api.controller.dashboard.service.GenericQueryParam;
import com.pinduoduo.mountain.api.controller.monitor.GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/lowcode")
@Slf4j
public class GenericDataServiceController {
    @Autowired
    private GenericDataService genericDataService;

    @PostMapping("/query")
    GenericResponse query(@RequestBody GenericQueryParam param) {
        try {
            log.info("lowcode-query:{}", JSON.toJSONString(param));
            return new GenericResponse(true, genericDataService.get(param));
        } catch (Exception e) {
            log.info("lowcode-query:param={},e={}", JSON.toJSONString(param), e.getStackTrace());
            return new GenericResponse(false, e.getMessage());
        }
    }
}
