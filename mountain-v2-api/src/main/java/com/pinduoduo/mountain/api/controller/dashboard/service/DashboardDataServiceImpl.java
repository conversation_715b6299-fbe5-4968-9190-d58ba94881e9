package com.pinduoduo.mountain.api.controller.dashboard.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;

@Service
public class DashboardDataServiceImpl implements DashboardDataService {
    @Override
    public Integer getInteger(String metricName, String userId) {
        if (StringUtils.equals(metricName, "Integer_logic_database_cnt")) {

        }
        return null;
    }

    @Override
    public Map<String, Integer> getStats(String metricName, String userId) {
        if (StringUtils.equals(metricName, "stats_physical_database_cnt")) {
        }
        return Collections.emptyMap();
    }
}
