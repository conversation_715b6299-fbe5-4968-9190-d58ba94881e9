package com.pinduoduo.mountain.api.controller.review;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.service.review.ReviewHandler;
import com.pinduoduo.mountain.service.review.bo.OperationReviewRequestBO;
import com.pinduoduo.mountain.service.review.constant.OperationReviewStatus;
import com.pinduoduo.mountain.service.review.dto.OperationReviewDTO;
import com.pinduoduo.mountain.service.review.dto.OperationReviewResultDTO;
import com.pinduoduo.mountain.service.review.bo.OperationReviewBO;
import com.pinduoduo.mountain.service.review.request.OperationReviewListReq;
import com.pinduoduo.mountain.service.review.request.OperationReviewRequestReq;
import com.pinduoduo.mountain.service.review.request.OperationReviewReviewReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * 通用复核API
 */
@Api("common review api")
@Slf4j
@RestController
@RequestMapping("/api/v2/operation/review")
public class OperationReviewController {

    private final ReviewHandler reviewHandler;

    public OperationReviewController(@Qualifier("defaultReviewHandler") ReviewHandler reviewHandler) {
        this.reviewHandler = reviewHandler;
    }

    @PostMapping("/listReviews")
    public BaseResponse<List<OperationReviewDTO>> list(@RequestBody @Validated OperationReviewListReq operationReviewListReq) {
        List<OperationReviewDTO> list = this.reviewHandler.listReviews(operationReviewListReq.getResourceType(),operationReviewListReq.getResourceKeys());
        return BaseResponse.success(list);
    }

    @GetMapping("/{resourceType}/{resourceKey}")
    public BaseResponse<OperationReviewDTO> get(@PathVariable("resourceType") String resourceType,@PathVariable("resourceKey") String resourceKey){
        OperationReviewDTO review = this.reviewHandler.retrieveByUk(resourceType,resourceKey);
        if (review == null) {
            return BaseResponse.fail("not found operation review task!");
        }
        return BaseResponse.success(review);
    }

    @GetMapping("/{taskId}")
    public BaseResponse<OperationReviewDTO> get(@PathVariable("taskId") @Validated @NotEmpty @NotBlank String taskId) {
        OperationReviewDTO review = this.reviewHandler.retrieveByTaskId(taskId);
        if(review == null) {
            return BaseResponse.fail("not found operation review task!");
        }
        return BaseResponse.success(review);
    }


    @PostMapping("/{taskId}/approve")
     public BaseResponse<OperationReviewResultDTO> approve(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @PathVariable("taskId") @Validated @NotEmpty @NotBlank String taskId, @RequestBody OperationReviewReviewReq req) {
        OperationReviewBO reviewReq = new OperationReviewBO();
        reviewReq.setTaskId(taskId);
        reviewReq.setUsername(username);
        reviewReq.setStatus(OperationReviewStatus.REVIEWED.getStatus());
        reviewReq.setNote(req.getNote());
        OperationReviewResultDTO review = this.reviewHandler.review(reviewReq);
        if (review.getResult()){
            return BaseResponse.success(review);
        }
        return BaseResponse.fail(review.getMessage());
    }

    @PostMapping("/{taskId}/reject")
     public BaseResponse<OperationReviewResultDTO> reject(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @PathVariable("taskId") @Validated @NotEmpty @NotBlank String taskId,@RequestBody OperationReviewReviewReq req) {
        OperationReviewBO reviewReq = new OperationReviewBO();
        reviewReq.setUsername(username);
        reviewReq.setTaskId(taskId);
        reviewReq.setStatus(OperationReviewStatus.REJECTED.getStatus());
        reviewReq.setNote(req.getNote());
        OperationReviewResultDTO review = this.reviewHandler.review(reviewReq);
        if (review.getResult()){
            return BaseResponse.success(review);
        }
        return BaseResponse.fail(review.getMessage());
    }



    /**
     * request for review
     *
     * @return
     */
    @PostMapping("/")
    public BaseResponse<OperationReviewResultDTO> submit(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @ApiParam(value = "operation review request body", required = true) @Validated @RequestBody OperationReviewRequestReq req){
        OperationReviewRequestBO reviewRequestBO = new OperationReviewRequestBO();
        reviewRequestBO.setUsername(username);
        reviewRequestBO.setResourceType(req.getResourceType());
        reviewRequestBO.setResourceKey(req.getResourceKey());
        reviewRequestBO.setApplyReason(req.getApplyReason());
        reviewRequestBO.setData(req.getData());
        OperationReviewResultDTO request = this.reviewHandler.request(reviewRequestBO);
        if (request.getResult()){
            return BaseResponse.success(request);
        }
        return BaseResponse.fail(request.getMessage());
    }

    @DeleteMapping("{uid}/remove")
    public BaseResponse<OperationReviewDTO> remove(@PathVariable("uid") String uid){
        OperationReviewDTO dto = this.reviewHandler.removeByUid(uid);
        if (dto == null) {
            return BaseResponse.fail("not found operation review task: "+uid);
        }
        return BaseResponse.success(dto);
    }







}
