package com.pinduoduo.mountain.api.controller.dashboard;

import com.alibaba.fastjson.JSON;
import com.pinduoduo.mountain.api.controller.dashboard.service.GenericDataService;
import com.pinduoduo.mountain.api.controller.dashboard.service.GenericQueryParam;
import com.pinduoduo.mountain.api.controller.dashboard.service.UserBizService;
import com.pinduoduo.mountain.api.controller.monitor.GenericResponse;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.SystemConfigTbl;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.SystemConfigTblMapper;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v2/dashboard")
public class DashboardController {
    @Autowired
    private SystemConfigTblMapper systemConfigTblMapper;
    @Autowired
    private UserBizService userBizService;
    @Autowired
    private GenericDataService genericDataService;


    @GetMapping("/query/biz")
    public GenericResponse getBiz(@RequestParam("userId") String userId) {
        try {
            return new GenericResponse(true, userBizService.getAccountBiz(userId));
        } catch (Exception e) {
            log.error(String.format("getBiz: userId: %s", userId), e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/query/metric")
    public GenericResponse queryMetric(@RequestParam("name") String metric, @RequestParam("userId") String userId) {
        try {
            String key = String.format("dashboard.metrics.%s", metric);
            SystemConfigTbl tbl = systemConfigTblMapper.selectSystemConfigByKeyName(key);
            if (tbl == null || StringUtils.isEmpty(tbl.getKeyValue())) {
                return new GenericResponse(false, "无效指标：" + metric);
            }
            List<Dashboard> res = new LinkedList<>();
            String v = tbl.getKeyValue();
            List<Dashboard> subMetrics = JSON.parseArray(v, Dashboard.class);
            Map<String, Dashboard> subMetricMaps = subMetrics.stream().collect(Collectors.toMap(Dashboard::getMetricName, Function.identity()));

            List<String> metrics = subMetrics.stream().map(Dashboard::getMetricName).collect(Collectors.toList());
            GenericQueryParam param = new GenericQueryParam();
            param.setMetricNames(metrics);
            param.setUserId(userId);
            Map<String, Triple<Boolean, String, Object>> generalData = genericDataService.get(param);
            for (String k : generalData.keySet()) {
                Triple<Boolean, String, Object> data = generalData.get(k);
                Dashboard dashboard = subMetricMaps.get(k);
                if (data.getLeft()) {
                    if (data.getRight() instanceof Map) {
                        Map<String, Long> map = (Map<String, Long>) data.getRight();
                        for (String kk : map.keySet()) {
                            Dashboard cur = JSON.parseObject(JSON.toJSONString(dashboard), Dashboard.class);
                            cur.setValue(map.get(kk));
                            cur.setName(kk);
                            res.add(cur);
                        }
                    } else {
                        Dashboard cur = JSON.parseObject(JSON.toJSONString(dashboard), Dashboard.class);
                        cur.setValue(data.getRight());
                        res.add(cur);
                    }
                }
            }

            Map<String, Dashboard> resMap = res.stream().collect(Collectors.toMap(Dashboard::getName, Function.identity()));
            List<String> orders = JSON.parseArray(LeoUtils.getStringProperty("mountain-v2-api." + key), String.class);
            List<Dashboard> results = new LinkedList<>();
            for (String k : orders) {
                results.add(resMap.get(k));
            }
            return new GenericResponse(true, results);

        } catch (Exception e) {
            log.error(String.format("queryMetric: name: %s userId: %s", metric, userId), e);
            return new GenericResponse(false, e.getMessage());
        }
    }
}
