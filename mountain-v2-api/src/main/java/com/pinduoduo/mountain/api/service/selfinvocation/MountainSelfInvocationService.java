package com.pinduoduo.mountain.api.service.selfinvocation;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pinduoduo.mountain.api.service.order.DdlOperateService;
import com.pinduoduo.mountain.api.util.ThreadPool;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.OrderDdlSub;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.OrderSql;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.OrderDdlSubMapper;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.OrderSqlMapper;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.pinduoduo.mountain.common.constant.PlatformConstant.ENV_PROD;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MountainSelfInvocationService {

    private final OrderSqlMapper orderSqlMapper;

    private final DdlOperateService ddlOperateService;
    private final OrderDdlSubMapper orderDdlSubMapper;

    public MountainSelfInvocationService(@Qualifier("orderSqlMapper") OrderSqlMapper orderSqlMapper, DdlOperateService ddlOperateService, @Qualifier("orderDdlSubMapper") OrderDdlSubMapper orderDdlSubMapper) {
        this.orderSqlMapper = orderSqlMapper;
        this.ddlOperateService = ddlOperateService;
        this.orderDdlSubMapper = orderDdlSubMapper;
    }

    public Boolean transferDdlTask(String fromIp, Long workId) {
        OrderSql orderSql = orderSqlMapper.selectByWorkId(workId);
        OrderSql.DdlConfig ddlConfig = StringUtil.jsonDeserialize(orderSql.getDdlConfig(), new TypeReference<OrderSql.DdlConfig>() {
        });

        List<OrderDdlSub> ddlSubListWaitDispatch = orderDdlSubMapper.selectByWorkIdAndStepAndStatus(workId, ddlConfig.getStep(), OrderSql.STATUS_WAITING_DISPATCH);
        if (ddlSubListWaitDispatch.isEmpty()) {
            log.info("workId {} transferDdlTask STATUS_WAITING_DISPATCH ddlSubList is empty from {}", workId, fromIp);
            List<OrderDdlSub> ddlSubListExecuting = orderDdlSubMapper.selectByWorkIdAndStepAnd2Status(workId, ddlConfig.getStep(), OrderSql.STATUS_THROTTLED, OrderSql.STATUS_EXECUTING);
            if (!ddlSubListExecuting.isEmpty() && (orderSql.getEnv().equals(ENV_PROD) || LeoUtils.getBooleanProperty("mountain-v2-api.ddl_order_monitor_test_env", false))) {
                // 这个工单还有 executing 和 throttled 的 ddl，拉起监控线程
                log.info("workId {} transferDdlTask STATUS_WAITING_DISPATCH STATUS_EXECUTING ddlSubList {} not empty, begin monitor", workId, ddlSubListExecuting.size());
                ThreadPool.EXECUTOR_SERVICE.execute(() -> ddlOperateService.monitorTaijiAndReplicationDelay(orderSql));
                ThreadPool.EXECUTOR_SERVICE.execute(() -> ddlOperateService.monitorInstanceLoad(orderSql));
                ThreadPool.EXECUTOR_SERVICE.execute(() -> ddlOperateService.monitorInstanceVolume(orderSql));
            }
        } else {
            // 这个工单还有待调度的 ddl，拉起一个调度线程
            log.info("workId {} transferDdlTask STATUS_WAITING_DISPATCH ddlSubList {} not empty, begin dispatch from {}", workId, ddlSubListWaitDispatch.size(), fromIp);
            Map<String, List<OrderDdlSub>> groupByInstance;
            if (orderSql.getIsMultiActive()) {
                groupByInstance = ddlSubListWaitDispatch.stream().collect(Collectors.groupingBy(OrderDdlSub::getPlanAddr));
            } else {
                groupByInstance = ddlSubListWaitDispatch.stream().collect(Collectors.groupingBy(OrderDdlSub::getInstanceId));
            }
            ThreadPool.EXECUTOR_SERVICE.submit(() -> ddlOperateService.executeCommon(orderSql, ddlConfig.getStep(), groupByInstance, true, "mountain-self-transfer"));
        }
        return true;
    }
}
