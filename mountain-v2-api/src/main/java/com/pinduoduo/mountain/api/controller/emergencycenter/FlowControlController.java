package com.pinduoduo.mountain.api.controller.emergencycenter;

import com.pinduoduo.mountain.api.dto.emergencycenter.MountainWeightAndFlowConfigDTO;
import com.pinduoduo.mountain.api.dto.emergencycenter.NewWeightAndFlowConfigDTO;
import com.pinduoduo.mountain.api.dto.emergencycenter.WeightAndFlowConfigDTO;
import com.pinduoduo.mountain.api.service.emergencycenter.FlowControlService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@Api("读流量配比功能相关接口")
@RequestMapping("/api/v2/flowControl")
public class FlowControlController {
    private final FlowControlService flowControlService;


    public FlowControlController(FlowControlService flowControlService) {
        this.flowControlService = flowControlService;
    }

    @ApiOperation("获取当前Keychain的粗粒度流量配比")
    @GetMapping("/getOldFlowConfig")
    public BaseResponse<WeightAndFlowConfigDTO> getOldFlowConfigByKeychain(
            @ApiParam(value = "Keychain名", required = true) @RequestParam(value = "keychain") String keychain
    ) {
        try {
            WeightAndFlowConfigDTO result = this.flowControlService.getOldWeightAndFlowByKeychain(keychain);
            log.info(String.format("当前Keychain的流量配比为：%s", result));
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getOldFlowConfigByKeychain error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取Keychain是否使用细粒度流量配比（新配置）")
    @GetMapping("/useNewConfig")
    public BaseResponse<Boolean> useNewConfig(
            @ApiParam(value = "Keychain名", required = true) @RequestParam(value = "keychain") String keychain
    ) {
        try {

            Boolean result = this.flowControlService.judgeKeychainUseNewConfig(keychain);
            log.info(String.format("Keychain %s是否使用细粒度配比：%s",keychain, result));
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("useNewConfig error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取Keychain的细粒度流量配比")
    @GetMapping("/getNewFlowConfig")
    public BaseResponse<NewWeightAndFlowConfigDTO> getNewFlowConfig(
            @ApiParam(value = "Keychain名", required = true) @RequestParam(value = "keychain") String keychain
    ) {
        try {
            NewWeightAndFlowConfigDTO result = this.flowControlService.getNewWeightAndFlowByKeychain(keychain);
            log.info(String.format("当前Keychain的流量配比为：%s", result));
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getFlowConfigByKeychain error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取Keychain的读流量配比,自动判断粗细粒度并转化为Mountain的格式")
    @GetMapping("/getFlowConfig")
    public BaseResponse<MountainWeightAndFlowConfigDTO> getMountainFlowConfig(
            @ApiParam(value = "Keychain名", required = true) @RequestParam(value = "keychain") String keychain
    ) {
        try {
            MountainWeightAndFlowConfigDTO result = this.flowControlService.getWeightAndFlowByKeychain(keychain);
            log.info(String.format("当前Keychain的流量配比为：%s", result));
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getMountainFlowConfig error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("更新Keychain的读流量配比,自动判断粗细粒度并转化为Mountain的格式")
    @PostMapping("/updateFlowConfig")
    public BaseResponse<String> updateOldKeychainFlowConfig(
            @ApiParam(value = "请求体参数", required = true)
            @Validated @RequestBody MountainWeightAndFlowConfigDTO req) {
        try {
            Boolean result = this.flowControlService.updateWeightAndFlow(req);
            if (result){
                return BaseResponse.success("更新成功！");
            }else{
                return BaseResponse.success("更新失败!请联系开发同学看看。");
            }
        } catch (Exception e) {
            log.error("getMountainFlowConfig error", e);
            return BaseResponse.fail(e);
        }
    }

}
