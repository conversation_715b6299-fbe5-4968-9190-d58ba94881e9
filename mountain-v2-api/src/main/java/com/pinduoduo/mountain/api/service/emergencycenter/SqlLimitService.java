package com.pinduoduo.mountain.api.service.emergencycenter;

import com.alibaba.fastjson.JSON;
import com.pinduoduo.mountain.common.model.keychain.TableShardConfig;
import com.pinduoduo.mountain.common.thirdparty.blbl.response.WhereFieldsResponse;
import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONArray;
import org.json.JSONObject;
import com.dianping.zebra.sql.SqlIdGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.pinduoduo.arch.leo.AppEnvUtil;
import com.pinduoduo.mountain.api.dto.emergencycenter.SQLFlowLimitConfig;
import com.pinduoduo.mountain.api.model.request.emergencycenter.KeychainReq;
import com.pinduoduo.mountain.api.model.request.emergencycenter.KeychainFlowLimitConfigReq;
import com.pinduoduo.mountain.common.constant.PlatformConstant;
import com.pinduoduo.mountain.common.dto.keychain.KeychainDTO;
import com.pinduoduo.mountain.common.thirdparty.zebrette.response.ZebretteBaseResponse;
import com.pinduoduo.mountain.common.util.HttpUtil;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.KeychainMapper;
import com.pinduoduo.mountain.repository.util.InformationSchemaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SqlLimitService {

    @Value("${mountain-api.zebrette_baseurl}")
    private String zebraBaseUrlProd;

    @Value("${mountain-api.zebrette_baseurl_fft}")
    private String zebraBaseUrlFFT;

    @Value("${mountain-api.zebrette_token}")
    private String token;

    @Value("${mountain-v2-api.blbl_url}")
    private String blblUrl;

    private final String localEnv = AppEnvUtil.getPddEnv();
    private final List<String> testEnv = Arrays.asList("tsh","tsh1","ffttest","ctsh1");
    private final KeychainMapper keychainMapper;

    public SqlLimitService(KeychainMapper keychainMapper) {
        this.keychainMapper = keychainMapper;
    }


    public List<SQLFlowLimitConfig> getAllSqlLimitRulesByKeychain(String keychain){
        String path = "/openapi/sql/flow_limit/list?leoKey=" + keychain;

        HashMap<String, String> headers = null;

        // 如果是本地调试，需要把headers加上，htj走srv域名 网关会带上认证消息
        if(localEnv.equals(PlatformConstant.ENV_DEV)||localEnv.equals(PlatformConstant.ENV_TESTING)){
            headers = new HashMap<String, String>(1) {{
                put("appid", "mountain-api");
            }};
        }

        String baseEnvUrl = getZebraBasicUrl(keychain);
        log.info(String.format("getAllSqlLimitRulesByKeychain request: %s", baseEnvUrl.replace("/api/v2","") + path));
        String respJson = HttpUtil.get(baseEnvUrl.replace("/api/v2","") + path, headers, null);

        log.info(String.format("getAllSqlLimitRulesByKeychain response: %s", respJson));

        TypeReference<ZebretteBaseResponse<List<SQLFlowLimitConfig>>> typeReference = new TypeReference<ZebretteBaseResponse<List<SQLFlowLimitConfig>>>() {};
        ZebretteBaseResponse<List<SQLFlowLimitConfig>> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("getAllSqlLimitRulesByKeychain response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("getAllSqlLimitRulesByKeychain Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();

    }

    public String calculateSqlIdV2(String sql){
        String sqlIdV2 = SqlIdGenerator.generate(sql);
        log.info("SqlLimitService calculateSqlIdV2的SQL："+sql+"  结果："+sqlIdV2);
        return sqlIdV2;
    }

    public HashMap<String,String> calculateSqlIdV2andPreparingSql(String sql){
        String sqlIdV2 = SqlIdGenerator.generate(sql);
        String preparingSql =  SqlIdGenerator.getSqlFingerprint(sql);
        log.info("SqlLimitService calculateSqlIdV2的SQL："+sql+"  结果："+sqlIdV2);
        HashMap<String,String> result = new HashMap<>();
        result.put("sqlIdV2",sqlIdV2);
        result.put("preparingSql",preparingSql);
        return result;
    }



    public List<SQLFlowLimitConfig> addOrUpdateKeychainSqlLimitRule(KeychainFlowLimitConfigReq keychainFlowLimitConfigReq){
        String path = "/openapi/sql/flow_limit/addOrUpdate";

        HashMap<String, String> headers = null;

        // 如果是本地调试，需要把headers加上，htj走srv域名 网关会带上认证消息
        if(localEnv.equals(PlatformConstant.ENV_DEV)||localEnv.equals(PlatformConstant.ENV_TESTING)){
            headers = new HashMap<String, String>(1) {{
                put("appid", "mountain-api");
            }};
        }

        String baseEnvUrl = getZebraBasicUrl(keychainFlowLimitConfigReq.getKeychain());

        SQLFlowLimitConfig sqlFlowLimitConfig = keychainFlowLimitConfigReq.getSqlFlowLimitConfig();
        if(sqlFlowLimitConfig.getId()==null){
            String[] keychainSplit = keychainFlowLimitConfigReq.getKeychain().split("\\.");
            sqlFlowLimitConfig.setId(StringUtil.calculateMd5(keychainFlowLimitConfigReq));
            keychainFlowLimitConfigReq.setSqlFlowLimitConfig(sqlFlowLimitConfig);
        }

        String requestBodyJson = StringUtil.jsonSerialize(keychainFlowLimitConfigReq);
        log.info(String.format("addKeychainSqlLimitRule request: %s，postBody：%s", baseEnvUrl.replace("/api/v2","") + path,requestBodyJson));
        String respJson = HttpUtil.postWithError(baseEnvUrl.replace("/api/v2","") + path, headers, requestBodyJson);

        log.info(String.format("addKeychainSqlLimitRule response: %s", respJson));

        TypeReference<ZebretteBaseResponse<List<SQLFlowLimitConfig>>> typeReference = new TypeReference<ZebretteBaseResponse<List<SQLFlowLimitConfig>>>() {};
        ZebretteBaseResponse<List<SQLFlowLimitConfig>> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("addKeychainSqlLimitRule response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("addKeychainSqlLimitRule Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();

    }

    public List<SQLFlowLimitConfig> deleteKeychainSqlLimitRule(KeychainFlowLimitConfigReq keychainFlowLimitConfigReq){
        String path = "/openapi/sql/flow_limit/delete";

        HashMap<String, String> headers = null;

        // 如果是本地调试，需要把headers加上，htj走srv域名 网关会带上认证消息
        if(localEnv.equals(PlatformConstant.ENV_DEV)||localEnv.equals(PlatformConstant.ENV_TESTING)){
            headers = new HashMap<String, String>(1) {{
                put("appid", "mountain-api");
            }};

        }
        String baseEnvUrl = getZebraBasicUrl(keychainFlowLimitConfigReq.getKeychain());

        log.info(String.format("要删除的限流规则为：%s", keychainFlowLimitConfigReq));

        String requestBodyJson = StringUtil.jsonSerialize(keychainFlowLimitConfigReq);
        log.info(String.format("deleteKeychainSqlLimitRule request: %s，postBody：%s", baseEnvUrl.replace("/api/v2","") + path,requestBodyJson));
        String respJson = HttpUtil.postWithError(baseEnvUrl.replace("/api/v2","") + path, headers, requestBodyJson);

        log.info(String.format("deleteKeychainSqlLimitRule response: %s", respJson));

        TypeReference<ZebretteBaseResponse<List<SQLFlowLimitConfig>>> typeReference = new TypeReference<ZebretteBaseResponse<List<SQLFlowLimitConfig>>>() {};
        ZebretteBaseResponse<List<SQLFlowLimitConfig>> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("deleteKeychainSqlLimitRule response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("deleteKeychainSqlLimitRule Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();

    }

    public Boolean disableAllKeychainSqlLimitRule(KeychainReq request){
        String path = "/openapi/sql/flow_limit/disableAll";

        HashMap<String, String> headers = null;

        // 如果是本地调试，需要把headers加上，htj走srv域名 网关会带上认证消息
        if(localEnv.equals(PlatformConstant.ENV_DEV)||localEnv.equals(PlatformConstant.ENV_TESTING)){
            headers = new HashMap<String, String>(1) {{
                put("appid", "mountain-api");
            }};
        }

        String baseEnvUrl = getZebraBasicUrl(request.getKeychain());

        String requestBodyJson = StringUtil.jsonSerialize(request);
        log.info(String.format("disableAllKeychainSqlLimitRule request: %s，postBody：%s", baseEnvUrl.replace("/api/v2","") + path,requestBodyJson));
        String respJson = HttpUtil.postWithError(baseEnvUrl.replace("/api/v2","") + path, headers, requestBodyJson);

        log.info(String.format("disableAllKeychainSqlLimitRule response: %s", respJson));

        TypeReference<ZebretteBaseResponse<Boolean>> typeReference = new TypeReference<ZebretteBaseResponse<Boolean>>() {};
        ZebretteBaseResponse<Boolean> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("disableAllKeychainSqlLimitRule response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("disableAllKeychainSqlLimitRule Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();

    }


    public HashMap<String, List<String>> getAllPhysicalDbAndTablesByKeychain(String keychain){
        HashMap<String, List<String>> result = new HashMap<>();

        KeychainDTO keychainDTO = keychainMapper.selectByKeychain(keychain);
        String keychaDbInInfo = keychainDTO.getDbInfos();
        if (keychaDbInInfo == null || keychaDbInInfo.isEmpty()) {
            keychaDbInInfo = "{}";
        }
        List<Pair<String,Integer>> ipPortList = new ArrayList<>();
        List<String> allDbList = new ArrayList<>();
        JSONObject dbInfos = new JSONObject(keychaDbInInfo);
        for (String key : dbInfos.keySet()) {
            JSONArray jsonArray = dbInfos.getJSONArray(key);
            for (int i=0;i< jsonArray.length();i++) {
                JSONObject dbInfo = jsonArray.getJSONObject(i);
                String ip = dbInfo.getString("ip");
                Integer port = dbInfo.getInt("port");
                String database = dbInfo.getString("database");

                if (!allDbList.contains(database)){
                    allDbList.add(database);
                }

                Pair<String,Integer> pair = Pair.of(ip,port);
                if(!ipPortList.contains(pair)){
                    ipPortList.add(pair);
                }
            }
        }
        log.info(String.format("ipPortList 为：%s", ipPortList));

        HashMap<String, List<String>> filterDbResult = new HashMap<>();
        for (Pair<String, Integer> pair : ipPortList) {
            String ip = pair.getLeft();
            Integer port = pair.getRight();
            HashMap<String, List<String>> tmpResult = InformationSchemaUtil.getDatabaseToTables(ip,port);
            log.info(String.format("筛选前的tmpResult为：%s", tmpResult));
            for (String key : tmpResult.keySet()) {
                if(allDbList.contains(key)){
                    filterDbResult.put(key,tmpResult.get(key));
                }
            }
        }
        log.info(String.format("filterDbResult结果为：%s", filterDbResult));

        // 如果是shard模式的Keychain，过滤库表
        List<TableShardConfig> tableShardConfigList = JSON.parseArray(keychainDTO.getShardRuleConfigs(), TableShardConfig.class);
        List<String> logicTableNameList = new ArrayList<>();
        for (TableShardConfig tableShardConfig : tableShardConfigList) {
            String logicTableName = tableShardConfig.getTableName();
            if(!logicTableName.startsWith("taishan_")){
                logicTableNameList.add(logicTableName);
            }
        }
        log.info(String.format("logicTableNameList 结果为：%s", logicTableNameList));

        if(keychainDTO.getType().equals("shard")){
            for (String key : filterDbResult.keySet()) {
                List<String> dbPhysicalTableList = filterDbResult.get(key);
                List<String> matchedTables = dbPhysicalTableList.stream()
                        .filter(table -> matchesLogicTables(table, logicTableNameList))
                        .collect(Collectors.toList());
                log.info(String.format("过滤后的结果为：%s,%s", key,matchedTables));
                result.put(key,matchedTables);
            }
        }

        return result;
    }

    /**
     * 判断物理表名是否匹配任意一个逻辑表名
     */
    public static boolean matchesLogicTables(String tableName, List<String> logicTableNames) {
        for (String logicName : logicTableNames) {
            String regex = "^" + logicName + "[-_]?[0-9]*$"; // 生成正则
            if (Pattern.matches(regex, tableName)) {
                return true;
            }
        }
        return false;
    }


    public List<String> getSQLWhereFields(String sqlString){
        String path = "/metaOfDqlDml";

        HashMap<String, String> headers = null;

        // 如果是本地调试，需要把headers加上，htj走srv域名 网关会带上认证消息
        if(localEnv.equals(PlatformConstant.ENV_DEV)||localEnv.equals(PlatformConstant.ENV_TESTING)){
            headers = new HashMap<String, String>(1) {{
                put("appid", "mountain-api");
            }};
        }

        Map<String,String> request = new HashMap<String,String>(){{
            put("sqls",sqlString);
        }};

        String requestBodyJson = StringUtil.jsonSerialize(request);
        log.info(String.format("getBlblWhereFields request: %s，postBody：%s", blblUrl + path,requestBodyJson));
        String respJson = HttpUtil.postWithError(blblUrl + path, headers, requestBodyJson);

        log.info(String.format("getBlblWhereFields response: %s", respJson));

        TypeReference<WhereFieldsResponse> typeReference = new TypeReference<WhereFieldsResponse>() {};
        WhereFieldsResponse fieldsResponse = StringUtil.jsonDeserialize(respJson, typeReference);
        if(fieldsResponse.getStatus()){
            return fieldsResponse.getData().get(0).getMd().get(0).getWhere_field().stream().distinct().collect(Collectors.toList());
        }else{
            throw new RuntimeException("BLBL自动解析Where语句出错，请大佬手动填入需要的Where列。");
        }

    }

    public String getZebraBasicUrl (String keychain){

        // Zebra的测试和线上的Keychain都存在线上环境，他们的胡桃街环境和线上环境是两个db，相当于和Mountain的现状一模一样。所以，我们Mountain的不管是主站线上还是主站胡桃街环境，都应该调Zebra的prod.srv.pdd.net环境，本地开发才调Zebra的testing.srv.pdd.net
        String baseEnvUrl = zebraBaseUrlProd;
        KeychainDTO keychainDTO = keychainMapper.selectByKeychain(keychain);
        String keychainEnv = keychainDTO.getEnv();

        if(keychainEnv.startsWith("fft")){
            baseEnvUrl = this.zebraBaseUrlFFT;
        }
        return baseEnvUrl;
    }

}
