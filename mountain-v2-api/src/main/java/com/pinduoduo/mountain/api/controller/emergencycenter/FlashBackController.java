package com.pinduoduo.mountain.api.controller.emergencycenter;

import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.emergencycenter.bo.FlashbackPandaExecuteHostBO;
import com.pinduoduo.mountain.service.emergencycenter.dto.*;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.service.emergencycenter.impl.FlashbackService;
import com.pinduoduo.mountain.service.emergencycenter.request.*;
import com.pinduoduo.mountain.service.emergencycenter.response.DownloadFileResp;
import com.site.lookup.util.StringUtils;
import com.yiran.arch.leo.util.LeoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 * @apiNote emergency center's flashback, using yinglong tools to do flashback
 */

@Api("EmergencyCenter flashback")
@Slf4j
@RestController
@RequestMapping("/api/v2/emergencyCenter/flashback")
@Validated
public class FlashBackController {

    private final FlashbackService flashbackService;

    public FlashBackController(FlashbackService flashbackService) {
        this.flashbackService = flashbackService;
    }

    @GetMapping("/list")
    @ApiOperation("list flashback tasks")
    public BaseResponse<PageResult<FlashbackTaskGroupListResultDTO>> list(@ApiParam(value = "search val") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
                                                                     @ApiParam(value = "page") @Min(value = 1, message = "page") @RequestParam(value = "page", required = false, defaultValue = "1") @Validated Integer page,
                                                                     @ApiParam(value = "page size") @Min(value = 0, message = "page size") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        try{
            PageResult<FlashbackTaskGroupListResultDTO> r = this.flashbackService.retrieveFlashbackTaskList(searchVal, page, pageSize);
            return BaseResponse.success(r);
        }catch (Exception e){
            log.error("/api/v2/emergencyCenter/flashback/list error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/listByIds")
    @ApiOperation("list flashback tasks by id")
    public BaseResponse<List<FlashbackTaskDTO>> listById(@ApiParam(value = "id val") @RequestBody List<Long> ids) {
        List<FlashbackTaskDTO> flashbackTaskDTOs = this.flashbackService.retrieveFlashbackTaskByIds(ids);
        return BaseResponse.success(flashbackTaskDTOs);
    }

    @PostMapping("/listByTaskGroupIdAndIpPort")
    @ApiOperation("list flashback task list by task_group_id and ip/port")
    public BaseResponse<FlashbackTaskDTO> listByTaskGroupIdAndIpPort(@ApiParam(value = "id val") @RequestBody TaskGroupInstanceNameReq req){
        FlashbackTaskDTO flashbackTaskDTO = this.flashbackService.retrieveFlashbackTaskByGroupIdAndIpPort(req.getTaskGroupId(), req.getIp(), req.getPort());
        if(flashbackTaskDTO == null){
            return BaseResponse.fail("Not found record for : " + req.getTaskGroupId() + ":" + req.getIp() + ":" + req.getPort());
        }
        return BaseResponse.success(flashbackTaskDTO);
    }


    @GetMapping("/listExecuteHost")
    @ApiOperation("list panda execute host")
    public BaseResponse<List<FlashbackPandaExecuteHostBO>> listPandaExecuteHost() {
        List<FlashbackPandaExecuteHostBO> flashbackPandaExecuteHostBOS = this.flashbackService.listPandaExecuteHost();
        return BaseResponse.success(flashbackPandaExecuteHostBOS);
    }

    @PostMapping("/submit")
    @ApiOperation("submit instance level flashback task")
    public BaseResponse<FlashbackTaskSubmitResultDTO> submit(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @ApiParam(value = "flashback info", required = true) @Validated @RequestBody List<FlashbackTaskReq> req) {

        FlashbackTaskSubmitResultDTO r = flashbackService.submitInstanceTask(username, req);
        if (r.getResult()) {
            return BaseResponse.success(r);
        }
        return BaseResponse.fail(r.getMessage());
    }

    @PostMapping("/submitLogic")
    @ApiOperation("submit logic flashback task")
    public BaseResponse<FlashbackTaskSubmitResultDTO> submitLogic(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @ApiParam(value = "flashback info", required = true) @Valid @RequestBody List<FlashbackLogicTaskReq> req) {
        FlashbackTaskSubmitResultDTO r = flashbackService.submitLogicTask(username, req);
        if (r.getResult()) {
            return BaseResponse.success(r);
        }
        return BaseResponse.fail(r.getMessage());
    }

    @PostMapping("/create")
    public BaseResponse<List<FlashbackRetrieveResultDTO>> executeYinglongCreateTask(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @ApiParam(value = "flashback info", required = true) @Validated @RequestBody List<Long> taskIdList) {
        List<FlashbackRetrieveResultDTO> results = flashbackService.flashbackRetrieve(taskIdList);
        return BaseResponse.success(results);
    }

    @PostMapping("/execute")
    @ApiOperation("yinglong execute")
    public BaseResponse<List<FlashbackExecuteResultDTO>> executeYinglongExecuteTask(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @Validated @RequestBody List<Long> taskIdList) {
        Boolean b = LeoUtils.getBooleanProperty("mountain-v2-api.flashback.enable_flashback_execute", false);
        if (b) {
            List<FlashbackExecuteResultDTO> resultDTO = flashbackService.flashbackExecute(taskIdList);
            return BaseResponse.success(resultDTO);
        }
        return BaseResponse.fail("mountain-v2-api.flashback.enable_flashback_execute is setup to false");
    }

    @GetMapping("/canExecute")
    public BaseResponse<Boolean> canExecuteYinglongExecuteTask(@RequestHeader(HEADER_GATEWAY_USERNAME) String username) {
        Boolean b = LeoUtils.getBooleanProperty("mountain-v2-api.flashback.enable_flashback_execute", false);
        return BaseResponse.success(b);
    }

    @PostMapping("/download")
    public ResponseEntity<Resource> download(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestBody DownloadFileReq req) {
        DownloadFileResp d = flashbackService.download(req);
        String path = d.getPath();
        if (!d.getResult() || StringUtils.isEmpty(d.getPath())) {
            return ResponseEntity.notFound().build();
        }
        try {
            InputStreamResource resource = new InputStreamResource(new FileInputStream(path));
            HttpHeaders headers = new HttpHeaders();
            int i = path.lastIndexOf("/");
            String fn = path.substring(i + 1);
            fn = fn.replace(".reversed", "");
            fn = fn.replace(".decrypted", "");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fn + "\"");
            return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_OCTET_STREAM).body(resource);
        } catch (FileNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }


    @PostMapping("/downloadAll")
    @ApiOperation("download multi sql files")
    public ResponseEntity<Resource> downloadAll(@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestBody FlashbackDownloadAllReq req) {
        DownloadFileResp d = flashbackService.downloadAll(req);
        if  (!d.getResult()|| StringUtils.isEmpty(d.getPath())) {
            return ResponseEntity.notFound().build();
        }
        try {
            String path = d.getPath();
            InputStreamResource resource = new InputStreamResource(new FileInputStream(path));
            HttpHeaders headers = new HttpHeaders();
            int i = path.lastIndexOf("/");
            String fn = path.substring(i + 1);
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fn + "\"");
            headers.add("Content-Type","application/zip");
            return ResponseEntity.ok().headers(headers).body(resource);
        } catch (FileNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
