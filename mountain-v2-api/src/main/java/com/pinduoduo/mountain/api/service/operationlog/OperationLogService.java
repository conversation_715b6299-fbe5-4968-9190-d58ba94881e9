package com.pinduoduo.mountain.api.service.operationlog;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinduoduo.mountain.api.dto.operationlog.OperationLogDTO;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.OperationLog;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.OperationLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OperationLogService {

    private final OperationLogMapper operationLogMapper;

    public OperationLogService(OperationLogMapper operationLogMapper) {
        this.operationLogMapper = operationLogMapper;
    }


    // 获取operationLog列表
    public PageResult<OperationLogDTO> list(String username, LocalDateTime startTime, LocalDateTime endTime, int pageNum, int pageSize) {
        List<OperationLog> operationLogList;
        try (Page<Object> ignored = PageHelper.startPage(pageNum, pageSize)) {
            operationLogList = operationLogMapper.selectBySomeConditions(username, startTime, endTime);
        }

        List<OperationLogDTO> operationLogDTO = operationLogList.stream().map(OperationLogDTO::new).collect(Collectors.toList());
        return PageResult.of(operationLogDTO, new PageInfo<>(operationLogList));
    }

    public int addOperationLog(String username, int logType, String opModule, int opType, String opDesc, String requestIp, String requestMethod, String requestParams, int requestCost, int responeStatus, String responseData) {
        int row = operationLogMapper.insert(username, logType, opModule, opType, opDesc, requestIp, requestMethod, requestParams, requestCost, responeStatus, responseData);

        return row;
    }
}
