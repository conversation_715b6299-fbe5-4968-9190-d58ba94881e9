package com.pinduoduo.mountain.api.controller.user;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.user.dto.RoleAddResultDTO;
import com.pinduoduo.mountain.service.user.dto.RoleInfoDTO;
import com.pinduoduo.mountain.service.user.impl.RoleService;
import com.pinduoduo.mountain.service.user.request.RoleReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api("角色相关操作")
@RestController
@RequestMapping("/api/v2/role")
@Slf4j
public class RoleController {
    private final RoleService roleService;

    public RoleController(RoleService roleService) {
        this.roleService = roleService;
    }

    @GetMapping("/list")
    @ApiOperation("获取角色列表")
    public BaseResponse<PageResult<RoleInfoDTO>> list(
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<RoleInfoDTO> result = roleService.list(pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("role list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/roleInfo")
    @ApiOperation("获取角色信息")
    public BaseResponse<RoleInfoDTO> roleInfo(
            @ApiParam("角色名") @RequestParam(value = "roleName", required = false, defaultValue = "") String roleName
    ) {
        try {
            RoleInfoDTO result = roleService.roleInfo(roleName);
            if (result == null) {
                return BaseResponse.fail("角色不存在");
            }
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("role info error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/addRole")
    @ApiOperation("新增角色")
    public BaseResponse<RoleAddResultDTO> addRole(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody RoleReq req
    ) {
        try {
            RoleInfoDTO role = roleService.roleInfo(req.getRoleName());
            if (role != null) {
                return BaseResponse.fail("角色" + req.getRoleName() + "已存在");
            }
            int row = roleService.addRole(req);
            RoleAddResultDTO result = new RoleAddResultDTO();
            if (row > 0) {
                result.setSuccess(true);
                result.setMessage("新增角色成功");
            }
            return BaseResponse.success(result);

        } catch (Exception e) {
            log.error("addRole error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/updateRole")
    @ApiOperation("修改角色")
    public BaseResponse<RoleAddResultDTO> updateRole(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody RoleReq req
    ) {
        try {
            int row = roleService.updateRole(req);
            RoleAddResultDTO result = new RoleAddResultDTO();
            result.setSuccess(true);
            result.setMessage("更新角色成功");
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("updateRole error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/deleteRole")
    @ApiOperation("删除角色")
    public BaseResponse<RoleAddResultDTO> deleteRole(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody RoleReq req
    ) {
        try {
            int row = roleService.deleteRole(req);
            RoleAddResultDTO result = new RoleAddResultDTO();
            result.setSuccess(true);
            result.setMessage("删除角色成功");
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("deleteRole error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/updatePermission")
    @ApiOperation("修改权限")
    public BaseResponse<RoleAddResultDTO> updatePermission(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody RoleReq req
    ) {
        try {
            int row = roleService.updatePermission(req);
            RoleAddResultDTO result = new RoleAddResultDTO();
            result.setSuccess(true);
            result.setMessage("更新权限成功");
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("updatePermission error", e);
            return BaseResponse.fail(e);
        }
    }

}
