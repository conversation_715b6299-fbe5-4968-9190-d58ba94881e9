package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.metadata.dto.*;
import com.pinduoduo.mountain.service.metadata.impl.LogicTableService;
import com.pinduoduo.mountain.service.metadata.request.LogicTableInfoByThirdPartyReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("新逻辑表相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/logicTableNew")
public class LogicTableNewController {

    private final LogicTableService logicTableService;

    public LogicTableNewController(LogicTableService logicTableService) {
        this.logicTableService = logicTableService;
    }

    @GetMapping("/list")
    @ApiOperation("根据逻辑库 ID 获取逻辑表列表")
    public BaseResponse<PageResult<LogicTableNewListInfoDTO>> list(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam(value = "logicDbId") long logicDbId,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("搜索逻辑表名") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal
    ) {
        try {
            PageResult<LogicTableNewListInfoDTO> result = logicTableService.list(username, logicDbId, pageNum, pageSize, searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error(String.format("list logic table by logic db: %d", logicDbId), e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/listV2")
    @ApiOperation("根据逻辑库 ID 获取逻辑表列表信息，新版首页接口")
    public BaseResponse<PageResult<LogicTableNewListInfoV2DTO>> listV2(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam(value = "logicDbId") long logicDbId,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "搜索逻辑表名", defaultValue = "") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
            @ApiParam(value = "云环境", defaultValue = "") @RequestParam(value = "cloudType", required = false, defaultValue = "") String cloudType,
            @ApiParam(value = "单元", defaultValue = "") @RequestParam(value = "set", required = false, defaultValue = "") String set
    ) {
        try {
            PageResult<LogicTableNewListInfoV2DTO> result = logicTableService.listV2(username, logicDbId, pageNum, pageSize, searchVal, cloudType, set);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error(String.format("list logic table v2 by logic db: %d", logicDbId), e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/infoByThirdParty")
    @ApiOperation("根据第三方平台查询到的逻辑表的其他信息，例如读写qps、是否有抽数任务")
    public BaseResponse<List<LogicTableInfoByThirdPartyDTO>> infoByThirdParty(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表请求信息", required = true) @RequestBody LogicTableInfoByThirdPartyReq req
    ) {
        try {
            List<LogicTableInfoByThirdPartyDTO> result = logicTableService.infoByThirdParty(username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("infoByThirdParty error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailBaseInfo")
    @ApiOperation("获取逻辑表详情的基本信息")
    public BaseResponse<LogicTableNewBaseInfoDTO> detailBaseInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            LogicTableNewBaseInfoDTO result = logicTableService.detailBaseInfo(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailBaseInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailFieldInfo")
    @ApiOperation("获取逻辑表详情的字段信息")
    public BaseResponse<List<LogicTableFieldNewInfoDTO>> detailFieldInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            List<LogicTableFieldNewInfoDTO> result = logicTableService.detailFieldInfo(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailFieldInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/logicFieldListV2")
    @ApiOperation("获取逻辑表的逻辑字段列表")
    public BaseResponse<List<LogicTableFieldNewInfoV2DTO>> logicFieldListV2(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            List<LogicTableFieldNewInfoV2DTO> result = logicTableService.logicFieldListV2(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicFieldListV2 error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailIndexInfo")
    @ApiOperation("获取逻辑表的索引信息")
    public BaseResponse<List<LogicTableIndexNewInfoDTO>> detailIndexInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            List<LogicTableIndexNewInfoDTO> result = logicTableService.detailIndexInfo(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailIndexInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailMetaInfo")
    @ApiOperation("获取逻辑表的元信息/统计信息")
    public BaseResponse<TableMetaNewInfoDTO> detailMetaInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例 ID，物理库传值时必须同时传递实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库，物理库传值时必须同时传递实例 ID", required = true) @RequestParam(value = "database") String database,
            @ApiParam(value = "物理表", required = true) @RequestParam(value = "table") String table
    ) {
        try {
            TableMetaNewInfoDTO result = logicTableService.detailMetaInfo(username, instanceId, database, table);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailMetaInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailIndexInfoV2")
    @ApiOperation("获取逻辑表的索引信息")
    public BaseResponse<List<TableIndexNewInfoDTO>> detailIndexInfoV2(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            List<TableIndexNewInfoDTO> result = logicTableService.detailIndexInfoV2(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailIndexInfoV2 error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailMetaInfoV2")
    @ApiOperation("获取逻辑表的元信息/统计信息")
    public BaseResponse<TableMetaNewInfoDTO> detailMetaInfoV2(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            TableMetaNewInfoDTO result = logicTableService.detailMetaInfoV2(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailMetaInfoV2 error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/createTableSql")
    @ApiOperation("获取逻辑表的建表语句")
    public BaseResponse<String> createTableSql(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId
    ) {
        try {
            String result = logicTableService.createTableSql(logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("createTableSql error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/physicalDbAndInstanceIdList")
    @ApiOperation("根据逻辑表ID 获取物理库和物理库所在的实例 ID 映射的列表")
    public BaseResponse<Map<String, String>> physicalDbAndInstanceIdList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId

    ) {
        try {
            Map<String, String> result = logicTableService.physicalDbAndInstanceIdList(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("physicalDbAndInstanceIdList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/physicalTableList")
    @ApiOperation("根据逻辑表、物理库、实例 ID 获取物理表列表")
    public BaseResponse<List<String>> physicalTableList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam(value = "logicTableId") long logicTableId,
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库", required = true) @RequestParam(value = "database") String database
    ) {
        try {
            List<String> result = logicTableService.physicalTableList(username, logicTableId, instanceId, database);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("physicalTableList error", e);
            return BaseResponse.fail(e);
        }
    }
}
