package com.pinduoduo.mountain.api.controller.monitor.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.pinduoduo.mountain.repository.clickhouse.entity.ProcessListSqlDetail;
import com.pinduoduo.mountain.repository.clickhouse.entity.SlowQueryDetail;
import com.pinduoduo.mountain.repository.clickhouse.mountain_backend.CoreProcessListCollectMapper;
import com.pinduoduo.mountain.repository.clickhouse.pdb_log_repository.SlowMetricsMapper;
import com.pinduoduo.mountain.repository.mysql.DataSourceRouter;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.InstanceMapper;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MonitorCKDataServiceImpl implements MonitorCKDataService {
    @Autowired
    private CoreProcessListCollectMapper coreProcessListCollectMapper;
    @Autowired
    private SlowMetricsMapper slowMetricsMapper;
    @Autowired
    private InstanceMapper instanceMapper;

    private Map<String, String> slqwSQLCKMap = JSON.parseObject(LeoUtils.getStringProperty("mountain-v2-api.slow_sql_ck_map"), new TypeReference<Map<String, String>>() {
    });

    @Override
    public String getCKPeriod(Date start, Date end) {
        if (start == null || end == null) {
            return null;
        }
        long timeSeconds = end.getTime() - start.getTime();
        String period = "toStartOfMinute";
        if (timeSeconds > TimeUnit.HOURS.toMillis(12)) {
            period = "toStartOfHour";
        } else if (timeSeconds > TimeUnit.HOURS.toMillis(6)) {
            period = "toStartOfFifteenMinutes";
        } else if (timeSeconds > TimeUnit.HOURS.toMillis(2)) {
            period = "toStartOfFiveMinute";
        }
        return period;
    }

    @Override
    public Integer getTsPeriod(Date start, Date end) {
        if (start == null || end == null) {
            return null;
        }
        long timeSeconds = end.getTime() - start.getTime();
        Long period = 60L;
        if (timeSeconds > TimeUnit.HOURS.toMillis(12)) {
            period = TimeUnit.HOURS.toSeconds(1);

        } else if (timeSeconds > TimeUnit.HOURS.toMillis(6)) {
            period = TimeUnit.MINUTES.toSeconds(15);

        } else if (timeSeconds > TimeUnit.HOURS.toMillis(2)) {
            period = TimeUnit.MINUTES.toSeconds(5);
        }

        return period.intValue();
    }

    @Override
    public List<TsDataPoint> get(String metric, String instId, Date start, Date end) {
        List<TsDataPoint> dataPoints = new LinkedList<>();
        if (StringUtils.isEmpty(instId)) {
            return dataPoints;
        }
        String period = getCKPeriod(start, end);
        if (StringUtils.isEmpty(period)) {
            return dataPoints;
        }
        if (StringUtils.equalsIgnoreCase(metric, PROCESS_LIST_CNT)) {
            Instance instance = instanceMapper.selectOneByInstanceId(instId);
            String ip = instance.getIp();
            String port = instance.getPort().toString();
            String indexSchedule = slqwSQLCKMap.get(instance.getSet());
            DataSourceRouter.setDataSourceKey(indexSchedule);
            try {
                dataPoints = coreProcessListCollectMapper.selectProcessListTs(ip, port, period, start, end);
            } catch (Exception e) {
                log.error("getRT,e={}", e.getMessage());
            } finally {
                DataSourceRouter.clearDataSourceKey();
            }

        } else if (StringUtils.equalsIgnoreCase(metric, SLOW_QUERY_CNT)) {
            // fixme: dynamic datasource
            dataPoints = slowMetricsMapper.query(instId, start, end, period);
        }
        return dataPoints;
    }

    @Override
    public Long getProcessListCnt(String instId, Date start, Date end) {
        Instance instance = instanceMapper.selectOneByInstanceId(instId);
        String ip = instance.getIp();
        String port = instance.getPort().toString();
        return coreProcessListCollectMapper.selectProcessListTotalCnt(ip, port, start, end);
    }

    @Override
    public List<TsDataPoint> getProcessList(String instId, Date start, Date end, Integer pageIndex, Integer pageSize) {
        Long offset = (pageIndex - 1) * pageSize.longValue();
        Instance instance = instanceMapper.selectOneByInstanceId(instId);
        String ip = instance.getIp();
        String port = instance.getPort().toString();
        return coreProcessListCollectMapper.selectProcessListStats(ip, port, start, end, offset, pageSize);
    }

    @Override
    public ProcessListSqlDetail getProcesslistSQLdetail(String instId, Long fingerprint) {
        Instance instance = instanceMapper.selectOneByInstanceId(instId);
        String ip = instance.getIp();
        String port = instance.getPort().toString();
        return coreProcessListCollectMapper.selectProcessListSqlDetail(ip, port, fingerprint);
    }

    @Override
    public List<SlowQueryDetail> slowQueryDetail(String instanceId, Date start, Date end) {
        return slowMetricsMapper.slowQueryDetail(instanceId, start, end);
    }

    @Override
    public ProcessListSqlDetail sqlDetail(String instanceId, Long fingerprint, Date start, Date end) {
        Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
        String ip = instance.getIp();
        String port = instance.getPort().toString();
        return slowMetricsMapper.sqlDetail(instanceId, fingerprint, start, end);
    }

    @Override
    public Long getProcessListCollectTotal(String instId, Date beginTime, Date endTime) {
        Instance instance = instanceMapper.selectOneByInstanceId(instId);
        String ip = instance.getIp();
        String port = instance.getPort().toString();
        return coreProcessListCollectMapper.getProcessListCollectTotal(ip, port, beginTime, endTime);
    }

    @Override
    public List<ProcessListSqlDetail> getProcessListData(String instId, Date beginTime, Date endTime, Integer pageIndex, Integer pageSize) {
        Long offset = (pageIndex - 1) * pageSize.longValue();
        Instance instance = instanceMapper.selectOneByInstanceId(instId);
        String ip = instance.getIp();
        String port = instance.getPort().toString();
        return coreProcessListCollectMapper.getProcessListData(ip, port, beginTime, endTime, offset, pageSize);
    }
}

