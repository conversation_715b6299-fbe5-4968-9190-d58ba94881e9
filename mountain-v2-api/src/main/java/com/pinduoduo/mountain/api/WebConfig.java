package com.pinduoduo.mountain.api;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinduoduo.mountain.api.controller.interceptor.AuthorityInterceptor;
import com.pinduoduo.mountain.api.controller.interceptor.OmegaCallbackInterceptor;
import com.pinduoduo.mountain.common.util.StringUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final AuthorityInterceptor authorityInterceptor;
    private final OmegaCallbackInterceptor omegaCallbackInterceptor;

    public WebConfig(AuthorityInterceptor authorityInterceptor, OmegaCallbackInterceptor omegaCallbackInterceptor) {
        this.authorityInterceptor = authorityInterceptor;
        this.omegaCallbackInterceptor = omegaCallbackInterceptor;
    }

    /**
     * 管控请求 sso 鉴权拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 所有以 /api/v2 开头的路径都需要先鉴权
        registry.addInterceptor(authorityInterceptor).addPathPatterns("/api/v2/**").order(1);
        // omega 回调接口的鉴权
        registry.addInterceptor(omegaCallbackInterceptor).addPathPatterns("/api/callback/omega/**").order(2);
    }

    /**
     * 配置 jackson 序列化反序列化
     */
    @Bean
    public ObjectMapper objectMapper() {
        return StringUtil.getObjectMapper();
    }

    @Bean
    public SerializeConfig serializeConfig() {
        // com.alibaba.fastjson.JSONObject 序列化对象时，属性名使用下划线分隔
        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.setPropertyNamingStrategy(PropertyNamingStrategy.SnakeCase);
        return serializeConfig;
    }
}
