package com.pinduoduo.mountain.api.controller.callback;

import com.alibaba.fastjson.JSON;
import com.pinduoduo.mountain.api.dto.callback.omega.OmegaCallbackBaseResponse;
import com.pinduoduo.mountain.api.model.request.callback.OmegaApproveFlowCallbackReq;
import com.pinduoduo.mountain.api.model.request.callback.OmegaSubTicketCallbackReq;
import com.pinduoduo.mountain.api.service.callback.OmegaCallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api("omega 回调相关接口")
@Slf4j
@RestController
@RequestMapping("/api/callback/omega")
public class OmegaCallbackController {

    private final OmegaCallbackService omegaCallbackService;

    public OmegaCallbackController(OmegaCallbackService omegaCallbackService) {
        this.omegaCallbackService = omegaCallbackService;
    }

    @PostMapping("/ddlApproveFlowCallback")
    @ApiOperation("omega 工单状态回调接口")
    public OmegaCallbackBaseResponse<Boolean> ddlApproveFlowCallback(
            @ApiParam(value = "回调请求", required = true) @RequestBody OmegaApproveFlowCallbackReq omegaApproveFlowCallbackReq
    ) {
        try {
            omegaCallbackService.ddlApproveFlowCallback(omegaApproveFlowCallbackReq);
            return OmegaCallbackBaseResponse.success(true, "成功", "成功");
        } catch (Exception e) {
            log.error("ddlApproveFlowCallback error", e);
            return OmegaCallbackBaseResponse.fail(e);
        }
    }

    @PostMapping("/ddlStatusSync")
    @ApiOperation("omega 主单审批完成之后的子单回调 - 状态同步步骤")
    public OmegaCallbackBaseResponse<Boolean> ddlStatusSync(
            @ApiParam(value = "工单 id", required = true) @RequestParam(value = "workId") String workId
    ) {
        try {
            // dba review 之后把 mountain 工单状态置为 omega 已审批
            omegaCallbackService.ddlStatusSync(workId);
            return OmegaCallbackBaseResponse.success(true, "成功", "成功");
        } catch (Exception e) {
            log.error("ddlStatusSync error", e);
            return OmegaCallbackBaseResponse.fail(e);
        }
    }

    @PostMapping("/ddlFieldKeySync")
    @ApiOperation("omega 主单审批完成之后的子单回调 - 字段加密 KEY 同步步骤")
    public OmegaCallbackBaseResponse<Boolean> ddlFieldKeySync(
            @ApiParam(value = "工单 id", required = true) @RequestParam(value = "workId") String workId
    ) {
        try {
            // 这里为了兼容旧版 ddl，只假装回调一下，真实的变更需要收在【ddl真正执行成功】之后的【元数据统一变更】中
            return OmegaCallbackBaseResponse.success(true, "成功", "成功");
        } catch (Exception e) {
            log.error("ddlFieldKeySync", e);
            return OmegaCallbackBaseResponse.fail(e);
        }
    }

    @PostMapping("/ddlFieldLevelSync")
    @ApiOperation("omega 主单审批完成之后的子单回调 - 字段安全等级同步步骤")
    public OmegaCallbackBaseResponse<Boolean> ddlFieldLevelSync(
            @ApiParam(value = "工单 id", required = true) @RequestParam(value = "workId") String workId
    ) {
        try {
            // 这里为了兼容旧版 ddl，只假装回调一下，真实的变更需要收在 【ddl真正执行成功】之后的【元数据统一变更】中
            return OmegaCallbackBaseResponse.success(true, "成功", "成功");
        } catch (Exception e) {
            log.error("ddlFieldLevelSync", e);
            return OmegaCallbackBaseResponse.fail(e);
        }
    }


    @PostMapping("/donothing")
    @ApiOperation("omega 审批 完成 子工单回调，什么都不做")
    public OmegaCallbackBaseResponse<Boolean> doNothing(
          @RequestBody OmegaSubTicketCallbackReq req
    ) {
        String jsonString = JSON.toJSONString(req);
        try {

            log.info("donothing: "+ jsonString);
            return OmegaCallbackBaseResponse.success(true, "成功", "do nothing");
        } catch (Exception e) {
            log.error("doNothing: "+ jsonString, e);
            return OmegaCallbackBaseResponse.fail(e);
        }
    }

    @PostMapping("/joinProjectGroupApproveFlowCallback")
    @ApiOperation("omega 审批 joinProjectGroup 完成 子工单回调")
    public OmegaCallbackBaseResponse<Boolean> joinProjectGroupApproveFlowCallback(
          @RequestBody OmegaSubTicketCallbackReq req
    ) {
        try {
//            String omegaId = req.getParentId();
//            TFOrderBasic tfOrderBasic = tfOrderService.getOrderByOmegaId(Long.valueOf(omegaId));
//            if (tfOrderBasic == null){
//                 return OmegaCallbackBaseResponse.fail(new RuntimeException(omegaId +" not found"));
//            }
//            Long workId = tfOrderBasic.getWorkId();
//            tfOrderService.updateOrderStatus(workId, TFOrderStatusEnum.EXECUTING.name());
//            Boolean newJobResult = tfOrderService.generateNewJob(tfOrderBasic);
//            if (!newJobResult){
//                String m = "Job 创建失败!";
//                log.error("joinProjectGroupApproveFlowCallback: "+ m);
//                return OmegaCallbackBaseResponse.fail(new RuntimeException(m));
//            }
            return OmegaCallbackBaseResponse.success(true, "joinProjectGroup 成功", "do nothing");
        } catch (Exception e) {
            log.error("joinProjectGroupApproveFlowCallback", e);
            return OmegaCallbackBaseResponse.fail(e);
        }
    }
}
