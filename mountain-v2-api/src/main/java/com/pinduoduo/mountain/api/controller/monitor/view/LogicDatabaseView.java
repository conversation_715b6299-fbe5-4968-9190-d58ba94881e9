package com.pinduoduo.mountain.api.controller.monitor.view;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.LogicDatabase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.pinduoduo.mountain.service.metadata.dto.LogicDatabaseNewSearchInfoDTO;
import lombok.NoArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;

import java.lang.reflect.InvocationTargetException;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LogicDatabaseView extends LogicDatabase {
    private String feDesc;

    public LogicDatabaseView(LogicDatabaseNewSearchInfoDTO logicDatabase) throws InvocationTargetException, IllegalAccessException {
        BeanUtils.copyProperties(this, logicDatabase);
        this.feDesc = String.format("%s/%s/%s/%s", this.getLogicDbName(), this.getLogicClusterName(), this.getServiceName(), this.getBusinessName());
    }

    public LogicDatabaseView(LogicDatabase logicDatabase) throws InvocationTargetException, IllegalAccessException {
        BeanUtils.copyProperties(this, logicDatabase);
        this.feDesc = String.format("%s/%s/%s/%s", this.getLogicDbName(), this.getLogicClusterName(), this.getServiceName(), this.getBusinessName());
    }


    public String getLogicDbIdStr() {
        return getLogicDbId() != null ? getLogicDbId().toString() : null;
    }
}
