package com.pinduoduo.mountain.api.controller.monitor.service;

import com.alibaba.dubbo.rpc.RpcContext;
import com.pinduoduo.arch.egret.contract.api.BaseResponse;
import com.pinduoduo.arch.egret.contract.api.request.meta.EgretListFieldRequest;
import com.pinduoduo.arch.egret.contract.api.request.meta.EgretListMetricRequest;
import com.pinduoduo.arch.egret.contract.api.request.meta.EgretListTagKeyRequest;
import com.pinduoduo.arch.egret.contract.api.request.meta.EgretListTagValueRequest;
import com.pinduoduo.arch.egret.contract.dto.meta.EgretListFieldDto;
import com.pinduoduo.arch.egret.contract.dto.meta.EgretListMetricDto;
import com.pinduoduo.arch.egret.contract.dto.meta.EgretListTagKeyDto;
import com.pinduoduo.arch.egret.contract.dto.meta.EgretListTagValueDto;
import com.pinduoduo.arch.egret.contract.service.EgretMetricMetaService;
import com.pinduoduo.mountain.api.controller.dashboard.service.UserBizService;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.*;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.monitor.MonitorDBInstance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.*;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.monitor.CoreAccountBusinessMapper;
import com.pinduoduo.mountain.service.user.impl.UserService;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.InetAddressValidator;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.zip.CRC32;

@Service
@Slf4j
public class MonitorMetaDataServiceImpl implements MonitorMetaDataService {
    private static final String LEO_DEFAULT_INVALID_VALUE = "LEO_DEFAULT_INVALID_VALUE";
    private static Map<String, Map<String, String>> CDB_MONITOR_GROUP;
    private static Map<String, String> DEFAULT_CDB_MONITOR_GROUP;
    private final ConcurrentHashMap<String, List<String>> metricFields = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, String> metricMaps = new ConcurrentHashMap<>();
    @Resource
    private LogicDatabaseMapper logicDatabaseMapper;
    @Resource
    private ClusterMapper clusterMapper;
    @Resource
    private PhysicalClusterMapper physicalClusterMapper;
    @Resource
    private InstanceMapper instanceMapper;
    @Resource
    private CoreAccountBusinessMapper coreAccountBusinessMapper;
    @Resource
    private SystemConfigTblMapper systemConfigTblMapper;
    @Resource
    private UserBizService userBizService;
    @Resource
    private EgretMetricMetaService egretMetricMetaService;
    @Resource
    private UserService userService;
    private String appId;
    private String secret;

    @PostConstruct
    public void init() {
        this.appId = LeoUtils.getStringProperty("mountain-v2-api.monitor_egret_appid");
        this.secret = DigestUtils.sha1Hex(LeoUtils.getStringProperty("mountain-v2-api.monitor_egret_secret") + appId);
    }

    @Override
    public List<LogicDatabase> searchLogicDatabases(String key, String biz, String userId, String env) {
        List<String> services = userService.getServiceListByUser(userId);
        return logicDatabaseMapper.selectLogicDatabaseLike(env, services, key);
    }

    @Override
    public List<LogicDatabase> searchLogicClusters(String key, String biz, String userId, String env) {
        List<String> services = userService.getServiceListByUser(userId);
        if (CollectionUtils.isEmpty(services)) {
            return Collections.emptyList();
        }
        return logicDatabaseMapper.selectLogicClusterLike(env, services, key);
    }

    @Override
    public List<Cluster> searchClusters(String key, String biz, String userId, String env) {
        List<String> services = userService.getServiceListByUser(userId);
        if (CollectionUtils.isEmpty(services)) {
            return Collections.emptyList();
        }
        return clusterMapper.selectLike(env, services, key);
    }

    @Override
    public List<PhysicalCluster> searchPhyClusters(String key, String biz, String env) {
        return physicalClusterMapper.selectLike(key, env);
    }

    @Override
    public List<MonitorDBInstance> searchInstances(String key, String biz, String env) {
        return instanceMapper.selectDBInstanceBasicInfo(key, getWhereStr(key), biz, env);
    }

    @Override
    public List<Instance> getInstances(LogicDatabase logicDatabase) {
        try {
            return instanceMapper.selectByLogicDatabaseId(logicDatabase.getId());
        } catch (Exception e) {
            log.error("getInstances error", e);
            return null;
        }
    }

    @Override
    public List<Instance> getInstances(Cluster cluster) {
        return instanceMapper.selectByClusterId(cluster.getId());
    }

    @Override
    public List<Instance> getInstances(PhysicalCluster physicalCluster) {
        return instanceMapper.selectByPhyClusterId(physicalCluster.getPhysicalClusterId());
    }

    @Override
    public List<Instance> getInstances(String key, String biz, String userId, String env) {
        List<String> services = userService.getServiceListByUser(userId);
        if (CollectionUtils.isEmpty(services)) {
            return Collections.emptyList();
        }
        return instanceMapper.selectLike(key, biz, services, userId, env);
    }

    @Override
    public Instance getInstance(String instanceId) {
        return instanceMapper.selectOneByInstanceId(instanceId);
    }


    @Override
    public List<String> getBigUser() {
        String users = LeoUtils.getStringProperty("mountain-v2-api.set_info_target");
        if (!StringUtils.equals(LEO_DEFAULT_INVALID_VALUE, users)) {
            return Arrays.asList(users.split(","));
        }
        return null;
    }

    @Override
    public boolean isBigUser(String userId) {
        List<String> users = getBigUser();
        return CollectionUtils.isNotEmpty(users) && users.contains(userId);
    }

    @Override
    public List<String> getMountainDBAs() {
        String dbas = LeoUtils.getStringProperty("mountain-v2-api.dba_members", LEO_DEFAULT_INVALID_VALUE);
        if (!StringUtils.equals(LEO_DEFAULT_INVALID_VALUE, dbas)) {
            return Arrays.asList(dbas.split(","));
        }
        return null;
    }

    @Override
    public boolean isDBA(String userName) {
        List<String> dbas = getMountainDBAs();
        return CollectionUtils.isNotEmpty(dbas) && dbas.contains(userName);
    }

    @Override
    public List<String> getWhites() {
        SystemConfigTbl configTbl = systemConfigTblMapper.selectSystemConfigByKeyName("get_user_business_white_list");
        if (configTbl != null && StringUtils.isNotEmpty(configTbl.getKeyValue())) {
            return Arrays.asList(configTbl.getKeyValue().split(","));
        }
        return null;
    }

    @Override
    public boolean isInWhiles(String userName) {
        List<String> whites = getWhites();
        return CollectionUtils.isNotEmpty(whites) && whites.contains(userName);
    }


    @Override
    public String getUserBiz(String userName) {
        // fixme : user authentication has not been supported
        if (isBigUser(userName) || isDBA(userName) || isInWhiles(userName)) {
            return ".*";
        }
        String biz = coreAccountBusinessMapper.selectBizByUsername(userName);
        if (StringUtils.isNotEmpty(biz)) {
            return String.join("|", biz.split(","));
        }
        return null;
    }

    @Override
    public String getMountainWebDomain() {
        return LeoUtils.getStringProperty("mountain-v2-api.web_domain");
    }

    @Override
    public String getHostName(String dbId) {
        return instanceMapper.selectHostName(dbId);
    }

    @Override
    public String getPdbPrefix() {
        return LeoUtils.getStringProperty("mountain-v2-api.pdb_prefix");
    }

    @Override
    public String getMonitorDbConfig(String dbId) {
        CRC32 crc = new CRC32();
        crc.update(dbId.getBytes(StandardCharsets.UTF_8));
        long crc_value = crc.getValue() % 4;
        return Long.toString(crc_value);
    }

    @Override
    public Map<String, String> getMonitorDbConfig() {
        if (MapUtils.isEmpty(DEFAULT_CDB_MONITOR_GROUP)) {
            DEFAULT_CDB_MONITOR_GROUP.put("ip", LeoUtils.getStringProperty("mountain-v2-api.monitor_db_ip_4"));
            DEFAULT_CDB_MONITOR_GROUP.put("port", LeoUtils.getStringProperty("mountain-v2-api.monitor_db_port_4"));
            DEFAULT_CDB_MONITOR_GROUP.put("user", LeoUtils.getStringProperty("mountain-v2-api.mountain_db_user"));
            DEFAULT_CDB_MONITOR_GROUP.put("password", LeoUtils.getStringProperty("mountain-v2-api.mountain_db_password"));
            DEFAULT_CDB_MONITOR_GROUP.put("db", "cdbmonitor");
        }
        return DEFAULT_CDB_MONITOR_GROUP;
    }

    @Override
    public String getEnv() {
        if (!StringUtils.equalsIgnoreCase(System.getProperty("os.name"), "linux")) {
            return "dev";
        }

        String env = LeoUtils.getStringProperty("mountain-v2-api.run_env");
        if (StringUtils.isEmpty(env)) {
            return "prod";
        }
        return env;
    }

    @Override
    public List<String> getEnvs() {
        return Arrays.asList("bg-prod", "prod", "dev", "testing", "bg-dev", "bg-knock-prod");
    }

    @Override
    public List<String> getMetricField(String ns, String metric) {
        String key = String.format("monitor.metric.field.%s.%s", ns, metric);
        List<String> fields = metricFields.get(key);
        if (CollectionUtils.isNotEmpty(fields)) {
            return fields;
        }
        return getMetricField(key);
    }

    public List<String> getMetricField(String key) {
        SystemConfigTbl v = systemConfigTblMapper.selectSystemConfigByKeyName(key);
        List<String> fields = Collections.singletonList("value");
        if (v != null && StringUtils.isNotEmpty(v.getKeyValue())) {
            fields = Arrays.asList(v.getKeyValue().split(","));
            metricFields.put(key, fields);
        }
        return fields;
    }

    @Override
    public String getMetric(String ns, String m) {
        String key = String.format("monitor.metric.map.%s", m);
        String metric = metricMaps.get(key);
        if (StringUtils.isNotEmpty(metric)) {
            return metric;
        }
        return getMetricMap(key);
    }

    @Override
    public List<String> dumpMetrics(String ns) {
        EgretListMetricRequest metaReq = new EgretListMetricRequest();
        RpcContext.getContext().setAttachment("AppName", appId);
        RpcContext.getContext().setAttachment("Secret", secret);
        metaReq.setAppId(appId);
        metaReq.setSecret(secret);
        metaReq.setNamespace(ns);
        List<String> metricNames = new LinkedList<>();
        while (true) {
            metaReq.setStartId(metricNames.size());
            BaseResponse<List<EgretListMetricDto>> metrics = egretMetricMetaService.listMetric(metaReq);
            List<String> currentMetrics = metrics.getResult().stream().map(EgretListMetricDto::getName).collect(Collectors.toList());
            metricNames.addAll(currentMetrics);
            if (currentMetrics.size() < 100) {
                break;
            }
        }
        System.out.println("-------------metrics:");
        for (String m : metricNames) {
            System.out.println(m);
        }
        return metricNames;
    }

    @Override
    public List<String> dumpTags(String ns, String metric) {
        EgretListTagKeyRequest tagKeyReq = new EgretListTagKeyRequest();
        tagKeyReq.setAppId(appId);
        tagKeyReq.setSecret(secret);
        tagKeyReq.setNamespace(ns);
        tagKeyReq.setMetric(metric);

        BaseResponse<List<EgretListTagKeyDto>> tagKeys = egretMetricMetaService.listTagKey(tagKeyReq);
        List<String> names = tagKeys.getResult().stream().map(v -> v.getName()).collect(Collectors.toList());
        for (String name : names) {
            System.out.printf("feynman-tags:%s %s %s\n", ns, metric, name);
        }
        return names;
    }

    @Override
    public List<String> dumpTagValues(String ns, String metric, String tagKey) {
        EgretListTagValueRequest tagValueRequest = new EgretListTagValueRequest();
        tagValueRequest.setAppId(appId);
        tagValueRequest.setSecret(secret);
        tagValueRequest.setNamespace(ns);
        tagValueRequest.setMetric(metric);
        tagValueRequest.setTagKey(tagKey);
        BaseResponse<List<EgretListTagValueDto>> tagValues = egretMetricMetaService.listTagValue(tagValueRequest);
        List<String> names = tagValues.getResult().stream().map(v -> v.getName()).collect(Collectors.toList());
        for (String name : names) {
            System.out.printf("feynman-tagValues:%s %s %s\n", ns, metric, name);
        }
        return names;
    }

    @Override
    public List<String> dumpFields(String ns, String metric) {
        EgretListFieldRequest fieldReq = new EgretListFieldRequest();
        fieldReq.setAppId(appId);
        fieldReq.setSecret(secret);
        fieldReq.setNamespace(ns);
        fieldReq.setMetric(metric);
        BaseResponse<List<EgretListFieldDto>> fields = egretMetricMetaService.listField(fieldReq);
        List<String> fieldNames = fields.getResult().stream().map(EgretListFieldDto::getName).collect(Collectors.toList());
        for (String f : fieldNames) {
            System.out.printf("feynman-fields:%s %s %s\n", ns, metric, f);
        }
        return fieldNames;
    }

    public String getMetricMap(String key) {
        String metric = null;
        SystemConfigTbl v = systemConfigTblMapper.selectSystemConfigByKeyName(key);
        if (v != null && StringUtils.isNotEmpty(v.getKeyValue())) {
            metric = v.getKeyValue();
            metricMaps.put(key, metric);
        }
        if (StringUtils.isEmpty(metric)) {
            metric = key.substring("monitor.metric.map.".length());
        }
        return metric;
    }

    private static String getWhereStr(String key) {
        try {
            if (StringUtils.contains(key, "__")) {
                String[] splits = key.split("__");
                if (splits.length == 2) {
                    String ip = splits[0];
                    int port = Integer.parseInt(splits[1]);
                    boolean isIp = InetAddressValidator.getInstance().isValidInet4Address(ip);
                    if (isIp && (port > 0 && port < 655535)) {
                        String str = " or (ip = '{0}' and port ='{1}') or" +
                                " (machine_ip = '{0}'and port = '{1}') ";
                        return MessageFormat.format(str, ip, port);
                    }
                }
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }
}
