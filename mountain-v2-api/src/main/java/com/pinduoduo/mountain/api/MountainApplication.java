package com.pinduoduo.mountain.api;

import com.dianping.cat.Cat;
import com.dianping.cat.servlet.CatFilter;
import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.TimeUnit;

/**
 * 这里默认禁止了 Spring Data JPA
 *
 * <AUTHOR>
 */
@Slf4j
@ComponentScan("com.pinduoduo.mountain")
@EnableScheduling
@SpringBootApplication(exclude = PageHelperAutoConfiguration.class)
public class MountainApplication {

    /**
     * 需要和金丝雀上服务的端口号一致
     */
    public static final int SERVER_PORT = 8080;

    public static void main(String[] args) {
        log.info("mountain start");

        SpringApplication springApplication = new SpringApplication(MountainApplication.class);
        // 优雅停机
        springApplication.setRegisterShutdownHook(false);
        ConfigurableApplicationContext ctx = springApplication.run(args);
        Runtime.getRuntime().addShutdownHook(
                new Thread(() -> {
                    try {
                        log.info("begin graceful shutdown, begin clean");

                        ApplicationShutdownClean shutdownClean = ctx.getBean(ApplicationShutdownClean.class);
                        shutdownClean.applicationShutdownClean();

                        TimeUnit.MILLISECONDS.sleep(10000L);
                    } catch (InterruptedException e) {
                        log.error("关机发生异常", e);
                        Thread.currentThread().interrupt();
                    }
                    ctx.close();
                }));
    }

    /**
     * 配置 tomcat http 端口
     * 目前 canary 没有向进程注入分配的 http 端口，需要业务进程主动配置，并且与金丝雀上端口号保持一致
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer(
            ServletRegistrationBean<?> servletRegistrationBean
    ) {
        return factory -> {
            servletRegistrationBean.setLoadOnStartup(1);// 程序启动之后初始化 spring servlet，否则会在第一次 url 请求初始化
            factory.addConnectorCustomizers(connector -> connector.setPort(SERVER_PORT));
        };
    }

    /**
     * 设置 cat filter，用于 http 请求的打点
     */
    @Bean
    public CatFilter catFilter() {
        Cat.initialize();
        return new CatFilter();
    }

}
