package com.pinduoduo.mountain.api.controller.monitor.common;

import com.alibaba.dubbo.rpc.RpcContext;
import com.pinduoduo.arch.egret.contract.api.BaseResponse;
import com.pinduoduo.arch.egret.contract.api.request.MetricQueryRequest;
import com.pinduoduo.arch.egret.contract.dto.MetricDataDto;
import com.pinduoduo.arch.egret.contract.dto.WhereItem;
import com.pinduoduo.arch.egret.contract.dto.query.QueryOption;
import com.pinduoduo.arch.egret.contract.service.EgretMetricDataService;
import com.pinduoduo.arch.egret.contract.service.EgretMetricMetaService;
import com.pinduoduo.arch.leo.AppEnvUtil;
import com.pinduoduo.mountain.api.controller.monitor.MetricData;
import com.pinduoduo.mountain.api.controller.monitor.service.MonitorMetaDataService;
import com.pinduoduo.mountain.common.constant.PlatformConstant;
import com.pinduoduo.mountain.common.constant.Time;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import com.pinduoduo.mountain.repository.mysql.cdbmonitorofrt.entity.InstanceRt;
import com.pinduoduo.mountain.repository.mysql.cdbmonitorofrt.mapper.InstanceRtMapper;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@ImportResource("classpath:consumer.xml")
@Service
@Slf4j
public class MonitorDataServiceImpl implements MonitorDataService {
    @Resource
    private EgretMetricDataService egretMetricDataService;
    @Resource
    private EgretMetricMetaService egretMetricMetaService;
    @Autowired
    private MonitorMetaDataService monitorMetaDataService;
    @Autowired
    private InstanceRtMapper instanceRtMapper;

    private static String PREFIX;
    private String appId;
    private String secret;
    private String appIdHtj;
    private String secretHtj;

    @PostConstruct
    public void init() {
        this.appId = LeoUtils.getStringProperty("mountain-v2-api.monitor_egret_appid");
        this.appIdHtj = LeoUtils.getStringProperty("mountain-v2-api.monitor_egret_appid_htj");
        this.secret = DigestUtils.sha1Hex(LeoUtils.getStringProperty("mountain-v2-api.monitor_egret_secret") + appId);
        this.secretHtj = DigestUtils.sha1Hex(LeoUtils.getStringProperty("mountain-v2-api.monitor_egret_secret_htj") + appIdHtj);
    }

    /**
     * @param metric:    指标列表
     * @param startTime: 开始时间
     * @param endTime:   结束时间， 如果开始、结束时间都为空，则表示查询当前最新值，否则表示时间范围查询
     * @return 数据点位
     **/
    public MetricData getDataPoints(String ns,
                                    String metric,
                                    List<Triple<String, String, List<String>>> tags,
                                    List<String> levels,
                                    List<String> fieldKeys,
                                    List<String> groupBy,
                                    Integer granularity,
                                    Long startTime,
                                    Long endTime, String env, String type) {
        List<WhereItem> whereItems = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Triple<String, String, List<String>> tag : tags) {
                WhereItem whereItem = new WhereItem(tag.getLeft(), tag.getMiddle(), tag.getRight());
                whereItems.add(whereItem);
            }
        }
        if (granularity == null) {
            long range = endTime - startTime;
            if (range > 2592000000L) { // 30天
                granularity = 3600;
            } else if (range > 86400000) {// 1天
                granularity = 300;
            } else if (range > 21600000) { // 6小时
                granularity = 60;
            } else {
                granularity = 5;
            }
        }
        MetricQueryRequest dataReq = MetricQueryRequest.builder()
                .name(metric)
                .title(metric)
                .relative(false)
                .startTime(startTime.toString())
                .endTime(endTime.toString())
                .granularity(granularity)
                .timeUnit("s")
                .queryType("max")
                .timeKey("time")
                .delaySeconds(-1)
                .fieldExpression(Collections.emptyMap())
                .whereItems(whereItems) //
                .joinConditions(Collections.emptyList())
                .groupBy(groupBy) //
                .levelList(levels) //
                .fieldKeys(fieldKeys) //
                .subQuery(false)
                .defaultFillValue("0")
                .futureQuery(false)
                .fillData(false)
                .autoAdjustGranularity(true)
                .queryOption(QueryOption.builder()
                        .mayNotContainsService(false)
                        .enableDelayMoveStartTime(false)
                        .localDr(false)
                        .dr("all")
                        .expressionFillTag(false)
                        .timezone("Asia/Shanghai")
                        .build())
                .disableRoundGranularity(false)
                .forceMask(false)
                .build();
        // 自建的 htj 实例需要跨环境调用 egret 的 htj
        dataReq.setUsername("lien");
        if (AppEnvUtil.getPddEnv().equals(PlatformConstant.ENV_PROD) && type.equals("pdb") && env.equals("test")) {
            dataReq.setAppId(appIdHtj);
            dataReq.setAppName(appIdHtj);
            dataReq.setSecret(secretHtj);
            RpcContext.getContext().setAttachment("gateway-target-env", "testing");
        } else {
            dataReq.setAppId(appId);
            dataReq.setAppName(appId);
            dataReq.setSecret(secret);
            RpcContext.getContext().removeAttachment("gateway-target-env");
        }

        BaseResponse<List<MetricDataDto>> response = egretMetricDataService.getMetricData(dataReq);
        List<MetricDataDto> result = response.getResult();
        if (CollectionUtils.isEmpty(result)) {
            log.error("getDataPoints: no result found {} {} {} {}", response.getError(), response.getCode(), response.getInspectError(), response.getExtraInfo());
            return null;
        }

        MetricData data = new MetricData();
        String key = groupBy.get(0); // instanceId, 非自建实例还有个 instanceType
        for (MetricDataDto d : result) {
            String instanceId = d.getTags().get(key);
            List<TsDataPoint> values = d.getValues().stream().map(v ->
                            new TsDataPoint(Math.round(((Double) v.get(1)) * 100.0) / 100.0, LocalDateTime.ofInstant(Instant.ofEpochMilli(((Double) v.get(0)).longValue()), ZoneId.systemDefault()).format(Time.FORMATTER)))
                    .collect(Collectors.toList());
            if (!type.equals("pdb")) {
                String instanceType = d.getTags().get("instanceType");
                if (instanceType != null && instanceType.equalsIgnoreCase("slave")) {
                    instanceId = instanceId + "-backup";
                } else if (instanceType != null && instanceType.equalsIgnoreCase("slave1")) {
                    instanceId = instanceId + "-second-backup";
                }
            }
            data.add(instanceId, values);
        }

        return data;
    }

    @Override
    public MetricData getRT(String dbId, String start, String end) {
        MetricData data = new MetricData();
        try {
            List<InstanceRt> instanceRts = instanceRtMapper.selectByCdbIdAndStartAndEnd(dbId, start, end);
            if (CollectionUtils.isNotEmpty(instanceRts)) {
                List<TsDataPoint> tsDataPoints = instanceRts.stream().map(e ->
                                new TsDataPoint(Math.round(e.getRt() * 100.0) / 100.0, e.getCreatedAt().format(Time.FORMATTER)))
                        .collect(Collectors.toList());
                data.add(dbId, tsDataPoints);
            }
        } catch (Exception e) {
            log.error("getRT {} {} {}", dbId, start, end, e);
        }
        return data;
    }

    @Override
    public List<List<MetricDataDto>> getData(String ns, String metric, String tagKey, String tagValue, String fieldKey) {
        List<String> levels = Arrays.asList(ns.split("\\."));
        List<String> fieldKeys = Arrays.asList(fieldKey);
        List<WhereItem> items = Arrays.asList(new WhereItem(tagKey, "=", Arrays.asList(tagValue)));
        // query data in batch model feynman.zhou
        Map<String, String> fieldExpr = new HashMap<>();
        fieldExpr.put("当前值", "now_busy");
        MetricQueryRequest dataReq = MetricQueryRequest.builder()
                .name(metric)
                .title("now")
                .relative(false)
                .timeRange("1s")
                .startTime(Long.toString(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1)))
                .endTime(Long.toString(System.currentTimeMillis()))
                .timeUnit("s")
                .queryType("max")
                .granularity(-1)
                .autoAdjustGranularity(true)
                .levelList(levels)
                .fieldKeys(fieldKeys)
                .fieldExpression(fieldExpr)
                .whereItems(items)
                .build();
        dataReq.setAppId(appId);
        dataReq.setSecret(secret);
        return egretMetricDataService.getMetricData(Arrays.asList(dataReq)).getResult();
    }

    public String getDBIdsFromIpPort(String ipPort) {
        if (!ipPort.startsWith("cdb") &&
                !ipPort.startsWith(getPREFIX()) &&
                !ipPort.startsWith("ddc") &&
                !ipPort.startsWith("rds")) {
            return null;
        }
        // not an ipPort string.
        return ipPort;
    }

    public String getPREFIX() {
        if (StringUtils.isEmpty(PREFIX)) {
            PREFIX = monitorMetaDataService.getPdbPrefix();
        }
        return PREFIX;
    }
}
