package com.pinduoduo.mountain.api.controller.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicResponse<T> {
    private boolean success;
    private String message;
    private String comments;
    private T data;

    public BasicResponse(boolean success, T data) {
        this.data = data;
        this.success = success;
    }
}
