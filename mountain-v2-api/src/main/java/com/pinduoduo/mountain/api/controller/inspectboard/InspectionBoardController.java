package com.pinduoduo.mountain.api.controller.inspectboard;

import com.pinduoduo.mountain.api.dto.inspectboard.InspectBoardSummaryDTO;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectDataDetailDTO;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectErrorTrendDTO;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectLogListDTO;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectAddWhiteReq;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectResultAddReq;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectTaskAddReq;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectTaskUpdateReq;
import com.pinduoduo.mountain.api.service.inspectboard.InspectBoardService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.repository.mysql.pddmountain.entity.InspectTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("巡检大盘接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/inspectBoard")
public class InspectionBoardController {

    private final InspectBoardService inspectBoardService;

    public InspectionBoardController(InspectBoardService inspectBoardService) {
        this.inspectBoardService = inspectBoardService;
    }

    @GetMapping("/summary")
    @ApiOperation("巡检大盘数据汇总")
    public BaseResponse<InspectBoardSummaryDTO> summary(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "日期", required = true) @RequestParam("date") String date
    ) {
        try {
            InspectBoardSummaryDTO inspectBoardSummaryDTO = inspectBoardService.summary(date);
            return BaseResponse.success(inspectBoardSummaryDTO);
        } catch (Exception e) {
            log.error("summary error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/logList")
    @ApiOperation("巡检日志结果列表")
    public BaseResponse<PageResult<InspectLogListDTO>> logList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "日期", required = true) @RequestParam("date") String date,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", defaultValue = "", required = false) String searchVal,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "排序方式, eg: name,desc;code,asc;date,desc 排序字段支持列表 name, code, total, error, ignore_count, error_rate") @RequestParam(value = "order", defaultValue = "", required = false) String order
    ) {
        try {
            PageResult<InspectLogListDTO> pageResult = inspectBoardService.logList(date, searchVal, pageNum, pageSize, order);
            return BaseResponse.success(pageResult);
        } catch (Exception e) {
            log.error("list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/download")
    @ApiOperation("下载某一巡检项的巡检结果")
    public void download(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "巡检项code", required = true) @RequestParam("code") String code,
            @ApiParam(value = "日期", required = true) @RequestParam("date") String date,
            HttpServletResponse response
    ) {
        try {
            inspectBoardService.download(code, date, response);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            log.error("download error", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/errorTrend")
    @ApiOperation("获取巡检异常的变化趋势")
    public BaseResponse<List<InspectErrorTrendDTO>> errorTrend(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "开始日期", required = true) @RequestParam("beginDate") String beginDate,
            @ApiParam(value = "结束日期", required = true) @RequestParam("endDate") String endDate,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", defaultValue = "", required = false) String searchVal
    ) {
        try {
            List<InspectErrorTrendDTO> inspectErrorTrendDTOList = inspectBoardService.errorTrend(beginDate, endDate, searchVal);
            return BaseResponse.success(inspectErrorTrendDTOList);
        } catch (Exception e) {
            log.error("errorTrend error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/inspectDataDetailList")
    @ApiOperation("巡检数据列表")
    public BaseResponse<PageResult<InspectDataDetailDTO>> inspectDataDetailList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "开始日期", required = true) @RequestParam("beginDate") String beginDate,
            @ApiParam(value = "结束日期", required = true) @RequestParam("endDate") String endDate,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", defaultValue = "", required = false) String searchVal,
            @ApiParam(value = "是否健康，否 0 是 1 NA 2") @RequestParam(value = "isHealthy", required = false) Integer isHealthy,
            @ApiParam(value = "实例/集群") @RequestParam(value = "instanceCluster", defaultValue = "", required = false) String instanceCluster,
            @ApiParam(value = "业务线") @RequestParam(value = "business", defaultValue = "", required = false) String business,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "排序方式, eg: inspect_date,desc;inspect_name,asc 支持的排序字段 inspect_date, inspect_code, inspect_name, business, unique_id, unique_name, created_at") @RequestParam(value = "order", defaultValue = "", required = false) String order
    ) {
        try {
            PageResult<InspectDataDetailDTO> pageResult = inspectBoardService.inspectDataDetailList(beginDate, endDate, searchVal, isHealthy, instanceCluster, business, pageNum, pageSize, order);
            return BaseResponse.success(pageResult);
        } catch (Exception e) {
            log.error("inspectDataDetail error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/inspectDataDetailListExport")
    @ApiOperation("巡检数据列表导出")
    public void inspectDataDetailListExport(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "开始日期", required = true) @RequestParam("beginDate") String beginDate,
            @ApiParam(value = "结束日期", required = true) @RequestParam("endDate") String endDate,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", defaultValue = "", required = false) String searchVal,
            @ApiParam(value = "是否健康，否 0 是 1 NA 2") @RequestParam(value = "isHealthy", required = false) Integer isHealthy,
            @ApiParam(value = "实例/集群") @RequestParam(value = "instanceCluster", defaultValue = "", required = false) String instanceCluster,
            @ApiParam(value = "业务线") @RequestParam(value = "business", defaultValue = "", required = false) String business,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "排序方式, eg: inspect_date,desc;inspect_name,asc 支持的排序字段 inspect_date, inspect_code, inspect_name, business, unique_id, unique_name, created_at") @RequestParam(value = "order", defaultValue = "", required = false) String order,
            HttpServletResponse response
    ) {
        try {
            inspectBoardService.inspectDataDetailListExport(beginDate, endDate, searchVal, isHealthy, instanceCluster, business, pageNum, pageSize, order, response);
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            log.error("inspectDataDetail error", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/addWhitelist")
    @ApiOperation("巡检加白")
    public BaseResponse<Boolean> addWhitelist(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "加白请求体", required = true) @RequestBody InspectAddWhiteReq inspectAddWhiteRequest
    ) {
        try {
            inspectBoardService.addWhitelist(username, inspectAddWhiteRequest);
            return BaseResponse.success(true);
        } catch (Exception e) {
            log.error("addWhitelist error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/inspectDbTypes")
    @ApiOperation("获取巡检数据库类型")
    public BaseResponse<List<String>> inspectDbTypes(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username
    ) {
        try {
            List<String> dbTypes = inspectBoardService.getInspectDbTypes();
            return BaseResponse.success(dbTypes);
        } catch (Exception e) {
            log.error("inspectDbTypes error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/taskList")
    @ApiOperation("获取巡检任务列表")
    public BaseResponse<PageResult<InspectTask>> taskList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", defaultValue = "", required = false) String searchVal,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "排序方式, eg: id,desc;name,asc 字段名支持: id, name, code, need_inform, is_inspect, dba_owner, create_time, last_inspect_time") @RequestParam(value = "order", defaultValue = "", required = false) String order
    ) {
        try {
            PageResult<InspectTask> pageResult = inspectBoardService.taskList(searchVal, pageNum, pageSize, order);
            return BaseResponse.success(pageResult);
        } catch (Exception e) {
            log.error("taskList error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/taskAdd")
    @ApiOperation("添加巡检任务")
    public BaseResponse<Boolean> taskAdd(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "添加任务请求体", required = true) @RequestBody InspectTaskAddReq req
    ) {
        try {
            inspectBoardService.taskAdd(username, req);
            return BaseResponse.success(true);
        } catch (Exception e) {
            log.error("taskAdd error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/taskUpdate")
    @ApiOperation("更新巡检任务")
    public BaseResponse<Boolean> taskUpdate(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "修改任务请求体", required = true) @RequestBody InspectTaskUpdateReq req
    ) {
        try {
            inspectBoardService.taskUpdate(username, req);
            return BaseResponse.success(true);
        } catch (Exception e) {
            log.error("taskUpdate error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/addInspectResult")
    @ApiOperation("添加巡检结果")
    public BaseResponse<String> addInspectResult(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "添加巡检结果请求体", required = true) @RequestBody @Valid InspectResultAddReq req
    ) {
        try {
            String result = inspectBoardService.addInspectResult(username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("addInspectResult error", e);
            return BaseResponse.fail(e);
        }
    }

}
