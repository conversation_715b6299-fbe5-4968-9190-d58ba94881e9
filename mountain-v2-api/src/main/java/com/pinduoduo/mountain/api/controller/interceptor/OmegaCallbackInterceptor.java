package com.pinduoduo.mountain.api.controller.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinduoduo.mountain.api.dto.callback.omega.OmegaCallbackBaseResponse;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("NullableProblems")
public class OmegaCallbackInterceptor implements HandlerInterceptor {

    /**
     * <a href="https://note.pdd.net/doc/204716866951688192?root=496344150063153152#no8sm">omega 鉴权说明文档</a>
     */
    @Override
    public boolean preHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler
    ) throws IOException {
        // getRequestURI 不带域名的请求路径，也不带 query 参数，例如 /api/callback/omega/ddlApproveFlowCallback
        // getRequestURL 带域名的完整 url，也不带 query 参数，例如 http://localhost:8080/api/callback/omega/ddlApproveFlowCallback
        // 如果请求是 /api/callback/omega/ddlApproveFlowCallback，跳过鉴权
        if (request.getRequestURI().equals("/api/callback/omega/ddlApproveFlowCallback")) {
            return true;
        }

        OmegaCallbackBaseResponse<String> respBody = new OmegaCallbackBaseResponse<>();
        respBody.setResult(false);
        response.setContentType("application/json;charset=UTF-8");

        ObjectMapper objectMapper = new ObjectMapper();


        String appkey = request.getHeader("appkey");
        String logId = request.getHeader("logId");
        String sign = request.getHeader("sign");

        long nowTimestamp = System.currentTimeMillis() / 1000;
        long reqTimestamp = Long.parseLong(logId);
        if (nowTimestamp + 300 < reqTimestamp || nowTimestamp - 300 > reqTimestamp) {
            respBody.setMessage(String.format("请求时间戳超过当前时间戳前后 300s，当前时间戳：%d，请求时间戳：%d", nowTimestamp, reqTimestamp));
            respBody.setComments(String.format("请求时间戳超过当前时间戳前后 300s，当前时间戳：%d，请求时间戳：%d", nowTimestamp, reqTimestamp));
            response.getWriter().write(objectMapper.writeValueAsString(respBody));
            return false;
        }

        if (!"omega".equals(appkey)) {
            respBody.setMessage(String.format("请求 appkey 非 omega，当前 appkey：%s", appkey));
            respBody.setComments(String.format("请求 appkey 非 omega，当前 appkey：%s", appkey));
            response.getWriter().write(objectMapper.writeValueAsString(respBody));
            return false;
        }

        String salt = LeoUtils.getStringProperty("mountain-v2-api.omega_callback_salt");
        String expectedSign = StringUtil.calculateMd5(logId + salt);
        if (!expectedSign.equals(sign)) {
            respBody.setMessage(String.format("请求签名不正确，当前签名：%s，期望签名：%s", sign, expectedSign));
            respBody.setComments(String.format("请求签名不正确，当前签名：%s，期望签名：%s", sign, expectedSign));
            response.getWriter().write(objectMapper.writeValueAsString(respBody));
            return false;
        }

        return true;
    }

    @Override
    public void postHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView
    ) {
    }

    @Override
    public void afterCompletion(
            HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex
    ) {
    }
}
