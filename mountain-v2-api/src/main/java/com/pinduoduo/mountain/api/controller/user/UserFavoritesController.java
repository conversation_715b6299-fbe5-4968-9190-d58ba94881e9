package com.pinduoduo.mountain.api.controller.user;


import com.alibaba.fastjson.JSON;
import com.pinduoduo.mountain.api.controller.monitor.GenericResponse;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.SystemConfigTbl;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.SystemConfigTblMapper;
import com.pinduoduo.mountain.service.user.impl.UserFavorites;
import com.pinduoduo.mountain.service.user.impl.UserFavoritesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/user")
@Slf4j
public class UserFavoritesController {
    @Autowired
    private SystemConfigTblMapper systemConfigTblMapper;
    @Autowired
    private UserFavoritesService userFavoritesService;

    @GetMapping("/favorites/default/version")
    public GenericResponse defaultVersion() {
        try {
            return new GenericResponse(true, userFavoritesService.getDefaultVersion());
        } catch (Exception e) {
            log.error("defaultVersion, stack", e);
            return new GenericResponse(false, e.getMessage());
        }
    }


    @GetMapping("/favorites/grayscale")
    public GenericResponse grayscale() {
        try {
            return new GenericResponse(true, userFavoritesService.enableGrayscale());
        } catch (Exception e) {
            log.error("enableGrayscale, stack", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/favorites/user/grayscale")
    public GenericResponse isInGrayscale(@RequestParam("userId") String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new GenericResponse(false, "用户名不能为空");
        }
        try {
            return new GenericResponse(true, userFavoritesService.isInGrayscale(userId));
        } catch (Exception e) {
            log.error(String.format("isInGrayscale userId=%s", userId), e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/favorites/get")
    GenericResponse getFavorites(@RequestParam("userId") String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new GenericResponse(false, "用户名不能为空");
        }
        try {
            UserFavorites userfavorites = userFavoritesService.getUserFavorites(userId);
            if (CollectionUtils.isNotEmpty(userfavorites.getFavoritesList())) {
                userfavorites.getFavoritesList().sort(String::compareTo);
            }
            return new GenericResponse(true, userfavorites);
        } catch (Exception e) {
            log.error(String.format("getFavorites userId = %s", userId), e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @PostMapping("/favorites/add")
    GenericResponse addFavorites(@RequestBody UserFavorites userFavorites) {
        if (StringUtils.isEmpty(userFavorites.getUserId())) {
            return new GenericResponse(false, "用户名不能为空");
        }
        try {
            return new GenericResponse(true, userFavoritesService.addUserFavorites(userFavorites));
        } catch (Exception e) {
            log.error(String.format("addFavorite request: %s", JSON.toJSONString(userFavorites)), e);
            return new GenericResponse(false, e.getMessage());
        }

    }

    @PostMapping("/favorites/del")
    GenericResponse delFavorites(@RequestBody UserFavorites userFavorites) {
        if (StringUtils.isEmpty(userFavorites.getUserId())) {
            return new GenericResponse(false, "用户名不能为空");
        }
        try {
            return new GenericResponse(true, userFavoritesService.deleteUserFavorites(userFavorites));
        } catch (Exception e) {
            log.error(String.format("delFavorites request: %s", JSON.toJSONString(userFavorites)), e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/ntf/enable")
    GenericResponse enableNtf(@RequestParam("ntfId") String ntfId) {
        if (StringUtils.isEmpty(ntfId)) {
            return new GenericResponse(false, "通知ID不能为空");
        }
        try {
            SystemConfigTbl tbl = systemConfigTblMapper.selectSystemConfigByKeyName(String.format("dashboard.ntf.id_%s", ntfId));
            boolean res = true;
            if (tbl != null) {
                res = Boolean.parseBoolean(tbl.getKeyValue());
            }
            return new GenericResponse(true, res);
        } catch (Exception e) {
            log.error(String.format("getFavorites ntfId %s", ntfId), e);
            return new GenericResponse(false, e.getMessage());
        }
    }
}