package com.pinduoduo.mountain.api.controller.tfapi;

import com.pinduoduo.mountain.api.model.request.order.ResetStatusReq;
import com.pinduoduo.mountain.api.model.request.order.WorkIdReq;
import com.pinduoduo.mountain.common.bean.AuthUserPerms;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.platform.BaseRetDTO;
import com.pinduoduo.mountain.common.thirdparty.omega.reponse.OmegaCallBackResponse;
import com.pinduoduo.mountain.common.thirdparty.omega.request.OmegaCallbackReq;
import com.pinduoduo.mountain.common.util.JSONUtils;
import com.pinduoduo.mountain.service.authmng.AuthMngService;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFOrderStatusEnum;
import com.pinduoduo.mountain.service.teamflow.order.TFOrderService;
import com.pinduoduo.mountain.service.teamflow.order.entity.TFOrderBasic;
import com.pinduoduo.mountain.service.teamflow.order.entity.TFOrderJobBasic;
import com.pinduoduo.mountain.service.teamflow.order.entity.TFOrderMainOrder;
import com.pinduoduo.mountain.service.teamflow.order.impl.TFOrderServiceImpl;
import com.pinduoduo.mountain.service.user.impl.UserServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static com.pinduoduo.mountain.common.constant.Http.*;

@Slf4j
@RestController
@RequestMapping("/api/v2/tf/order")
@Api("TFOrderController")
public class TFOrderController {
	@Resource
	private TFOrderService tfOrderService;

	@Resource
	private TFOrderServiceImpl tfOrderServiceImpl;

	@Resource
	private UserServiceImpl userServiceImpl;

	@Resource
	private AuthMngService authMngService;

	@GetMapping("/status/list")
	@ApiOperation("工单状态列表")
	public BaseResponse<List<Map<String, String>>> getStatusList() {
		try {
			List<Map<String, String>> result = new ArrayList<>(TFOrderStatusEnum.toList());
			return BaseResponse.success(result);
		} catch (Exception e) {
			log.error("getStatusList error", e);
			return BaseResponse.fail("获取Job状态的集合列表失败: + " + e.getMessage());
		}
	}

	@GetMapping("/list")
	@ApiOperation("工单列表接口")
	public BaseResponse<List<TFOrderMainOrder>> search(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
		   @RequestHeader(HEADER_ENV) String env,
		   @ApiParam(value = "搜索值", required = false) @RequestParam(value = "keyword", required = false) String keyword,
		   @ApiParam(value = "我创建的或者我管理的工单，包含：空字符串，createdByMe", required = false) @RequestParam(value = "ownerType", defaultValue = "") String ownerType,
		   @ApiParam(value = "工单状态，包含：WAITING_DBA_REVIEW,OMEGA_REVIEWING,REJECTED_OR_CANCELED,WAITING_EXEC,EXECUTING,SUSPENDED,FINISHED,FAILED,CLOSED", required = false) @RequestParam(value = "statuses", required = false) String statuses,
		   @ApiParam(value = "工单类型，逗号分隔", required = false) @RequestParam(value = "operationTypes", required = false) String operationTypes,
		   @ApiParam(value = "业务线，逗号分隔", required = false) @RequestParam(value = "businesses", required = false) String businesses,
		   @ApiParam(value = "服务名，逗号分隔", required = false) @RequestParam(value = "services", required = false) String services,
		   @ApiParam(value = "工单最早创建时间", required = false) @RequestParam(value = "createBegin", required = false) Long createBegin,
		   @ApiParam(value = "工单最晚创建时间", required = false) @RequestParam(value = "createEnd", required = false) Long createEnd,
		   @ApiParam(value = "页数，1开始", required = true) @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
		   @ApiParam(value = "每页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "19") Integer pageSize) {
		try {
			List<String> statusList = null;
			if (StringUtils.isNotEmpty(statuses)) {
				statusList = Arrays.asList(statuses.split(","));
			}
			List<String> operationTypeList = null;
			List<String> businesseList = null;
			List<String> serviceList = null;
			if (StringUtils.isNotEmpty(operationTypes)) {
				operationTypeList = Arrays.asList(operationTypes.split(","));
			}
			if (StringUtils.isNotEmpty(businesses)) {
				businesseList = Arrays.asList(businesses.split(","));
			}
			if (StringUtils.isNotEmpty(services)) {
				serviceList = Arrays.asList(services.split(","));
			}
			List<TFOrderMainOrder> basics = tfOrderService.search(username, keyword, env, statusList, operationTypeList,
					businesseList, serviceList, ownerType, createBegin == null ? null : new Date(createBegin),
					createEnd == null ? null : new Date(createEnd), pageNum, pageSize);

			return BaseResponse.success(basics);
		} catch (Exception e) {
			log.error("list error", e);
			return BaseResponse.fail(e);
		}
	}

	// 为啥统计个数也要写个接口，我也不知道，保供就这么写的🤷🏻‍。为啥我们没改，要改的地方太多了，这个接口可以晚点再搞
	@GetMapping("/count")
	@ApiOperation("count jobs(orders) by title/description/entity/work_id")
	public BaseResponse<Long> searchCount(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(HEADER_ENV) String env,
		  @ApiParam(value = "搜索值", required = false) @RequestParam(value = "keyword", required = false) String keyword,
		  @ApiParam(value = "我创建的或者我管理的工单，包含：空字符串，createdByMe", required = false) @RequestParam(value = "ownerType", defaultValue = "") String ownerType,
		  @ApiParam(value = "工单状态，包含：WAITING_DBA_REVIEW,OMEGA_REVIEWING,REJECTED_OR_CANCELED,WAITING_EXEC,EXECUTING,SUSPENDED,FINISHED,FAILED,CLOSED", required = false) @RequestParam(value = "statuses", required = false) String statuses,
		  @ApiParam(value = "工单类型，逗号分隔", required = false) @RequestParam(value = "operationTypes", required = false) String operationTypes,
		  @ApiParam(value = "业务线，逗号分隔", required = false) @RequestParam(value = "businesses", required = false) String businesses,
		  @ApiParam(value = "服务名，逗号分隔", required = false) @RequestParam(value = "services", required = false) String services,
		  @ApiParam(value = "工单最早创建时间", required = false) @RequestParam(value = "createBegin", required = false) Long createBegin,
		  @ApiParam(value = "工单最晚创建时间", required = false) @RequestParam(value = "createEnd", required = false) Long createEnd) {
		try {
			List<String> statusList = null;
			if (StringUtils.isNotEmpty(statuses)) {
				statusList = Arrays.asList(statuses.split(","));
			}
			List<String> operationTypeList = null;
			List<String> businesseList = null;
			List<String> serviceList = null;
			if (StringUtils.isNotEmpty(operationTypes)) {
				operationTypeList = Arrays.asList(operationTypes.split(","));
			}
			if (StringUtils.isNotEmpty(businesses)) {
				businesseList = Arrays.asList(businesses.split(","));
			}
			if (StringUtils.isNotEmpty(services)) {
				serviceList = Arrays.asList(services.split(","));
			}
			long cnt = tfOrderService.searchCount(username, keyword, env, statusList, operationTypeList, businesseList,
					serviceList, ownerType, createBegin == null ? null : new Date(createBegin),
					createEnd == null ? null : new Date(createEnd));
			return BaseResponse.success(cnt);
		} catch (Exception e) {
			log.error("list error", e);
			return BaseResponse.fail(e);
		}
	}

	@PostMapping("/addOrder")
	@ApiOperation("添加一个新工单接口")
	public BaseResponse<Long> orderAdd(@ApiParam("Job Content") @RequestBody TFOrderBasic orderBasicRq,
									@RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestHeader(value = HEADER_ENV) String env)
			throws Exception {
		if (!Objects.equals(orderBasicRq.getApplyUser(), username)) {
			return BaseResponse.fail("工单申请人和当前用户不一致!");
		}
		if (Strings.isBlank(orderBasicRq.getBusiness())) {
			return BaseResponse.fail("工单业务线不能为空!");
		}

		AuthUserPerms perms = authMngService.getUserPerms(username, env);
		if (!userServiceImpl.isDBA(username, env) && perms != null && !perms.hasBizPerm(orderBasicRq.getBusiness())
				&& !StringUtils.equals(orderBasicRq.getOperationType(), "AuthApplyOmegaCreateJob")) {
			log.info("添加工单,userPerms,userName={},env={},bizs={},services={}", username, env,
					JSONUtils.toJSONString(perms.getBizNames()), JSONUtils.toJSONString(perms.getServices()));
			return BaseResponse.fail("请申请业务线权限:" + orderBasicRq.getBusiness());
		}
		if (StringUtils.isEmpty(orderBasicRq.getEnv())) {
			orderBasicRq.setEnv(env);
		}

		final Triple<Boolean, Long, String> result = tfOrderService.addOrderMethod(orderBasicRq);
		if(result.getLeft()){
			return BaseResponse.success(result.getMiddle());
		}else{
			return BaseResponse.fail(result.getRight());
		}

	}

	@PostMapping("/reviewOrder")
	@ApiOperation("DBA Review工单接口")
	public BaseResponse<String> review(@RequestBody TFOrderBasic tfOrderBasicReq,
									   @RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestHeader(HEADER_ENV) String env) {
		if (!userServiceImpl.isDBA(username, env)) {
			return BaseResponse.fail("你没有权限审批工单！");
		}
		final Pair<Boolean, String> result = tfOrderService.reviewOrderMethod(tfOrderBasicReq);

		if(result.getLeft()){
			return BaseResponse.success(tfOrderBasicReq.getWorkId().toString());
		}else{
			return BaseResponse.fail(result.getRight());
		}
	}

	@GetMapping("/query/detail")
	@ApiOperation("根据workID获取工单详细信息")
	public BaseResponse<TFOrderJobBasic> query(@RequestParam(value = "workId", required = false) Long workId,
								 @RequestHeader(value = HEADER_ENV, required = false) String env,
								 @RequestHeader(HEADER_GATEWAY_USERNAME) String username) {
		try {
			TFOrderJobBasic orderJobDetail = tfOrderServiceImpl.selectTFOrderDetailByWorkId(workId);
			return BaseResponse.success(orderJobDetail);
		} catch (Exception e) {
			log.error("list error", e);
			return BaseResponse.fail(e);
		}
	}

	/**
	 * 参考文档： https://note.pdd.net/doc/496344297989447680?root=496344150063153152#GM6P6r
	 * @param req
	 * @param serviceName
	 * @return
	 */
	@PostMapping("/omegaCallback")
	@ApiOperation("OMEGA回调接口")
	public OmegaCallBackResponse omegaCallBack(@RequestBody OmegaCallbackReq req,
											   @RequestHeader(HEADER_GATEWAY_ORIGIN_SERVICE) String serviceName) {
		log.info("job omegaCallback:{}\nserviceName: {}", JSONUtils.toJSONString(req), serviceName);
		if (!Objects.equals(serviceName, "omega-task")) {
			return OmegaCallBackResponse.fail(String.format("Illegal request source service: %s", serviceName));
		}
		BaseRetDTO<String> result = tfOrderServiceImpl.handleOmegaCallback(req);
		if (!result.getResult()) {
			return OmegaCallBackResponse.fail(result.getMessage());
		}
		return OmegaCallBackResponse.success(result.getData());
	}

	@PostMapping("/resetOrderStatus")
	@ApiOperation("重置工单状态接口")
	public BaseResponse<String> resetOrderStatus(@RequestBody ResetStatusReq req, @RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestHeader(HEADER_ENV) String env) {
		if (!userServiceImpl.isDBA(username, env)) {
			return BaseResponse.fail("你没有权限更改工单状态！");
		}
		final Pair<Boolean, String> result = tfOrderService.resetOrderStatus(req.getWorkId(), req.getNewOrderStatus(),username);
		if(result.getLeft()){
			return BaseResponse.success(result.getRight());
		}else{
			return BaseResponse.fail(result.getRight());
		}
	}

	@PostMapping("/execOrder")
	@ApiOperation("手动执行工单")
	public BaseResponse<String> execOrder(@RequestBody WorkIdReq workIdReq, @RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestHeader(HEADER_ENV) String env) {
		if (!userServiceImpl.isDBA(username, env)) {
			return BaseResponse.fail("你没有权限更改工单状态！");
		}
		final Pair<Boolean, String> result = tfOrderService.execOrder(workIdReq.getWorkId());
		if(result.getLeft()){
			return BaseResponse.success(result.getRight());
		}else{
			return BaseResponse.fail(result.getRight());
		}
	}
}
