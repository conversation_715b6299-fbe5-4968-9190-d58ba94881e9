package com.pinduoduo.mountain.api.controller.dashboard.service;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.GenericMetricConf;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.GenericMetricConfMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GenericMetricConfServiceImpl implements GenericMetricConfService {
    @Autowired
    private GenericMetricConfMapper genericMetricConfMapper;

    @Override
    public GenericMetricConf getMetricConf(String metricName) {
        return genericMetricConfMapper.findByName(metricName);
    }

    @Override
    public List<GenericMetricConf> getMetricConfs(List<String> metricNames) {
        return genericMetricConfMapper.findByNames(metricNames);
    }
}
