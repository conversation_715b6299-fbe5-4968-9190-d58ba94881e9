package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.pinduoduo.mountain.api.model.request.metadata.InstanceTableInfoSearchReq;
import com.pinduoduo.mountain.api.model.request.metadata.LogicTable2PhysicalTableReq;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.metadata.dto.*;
import com.pinduoduo.mountain.service.metadata.impl.InstanceDbInfoService;
import com.pinduoduo.mountain.service.metadata.impl.InstanceService;
import com.pinduoduo.mountain.service.metadata.impl.InstanceTableInfoService;
import com.pinduoduo.mountain.service.metadata.impl.LogicTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("新逻辑库-集群相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/instanceNew")
public class InstanceNewController {

    private final InstanceDbInfoService instanceDbInfoService;

    private final InstanceTableInfoService instanceTableInfoService;

    private final InstanceService instanceService;
    private final LogicTableService logicTableService;


    public InstanceNewController(InstanceDbInfoService instanceDbInfoService, InstanceTableInfoService instanceTableInfoService, InstanceService instanceService, LogicTableService logicTableService) {
        this.instanceDbInfoService = instanceDbInfoService;
        this.instanceTableInfoService = instanceTableInfoService;
        this.instanceService = instanceService;
        this.logicTableService = logicTableService;
    }

    @ApiOperation("获取实例的主从复制拓扑图")
    @GetMapping("/instanceTopology")
    public BaseResponse<InstanceNewTopologyDTO> instanceTopology(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例 ID", required = true) @RequestParam("instanceId") String instanceId
    ) {
        try {
            InstanceNewTopologyDTO result = instanceService.instanceTopology(instanceId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("instanceTopology error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * retrieve databases list for each instance_id
     * @param instanceIds
     * @return
     */
    @ApiOperation("retrieve databases list for each instance_id")
    @PostMapping("/databases")
    public BaseResponse<List<InstanceDBInfoListDTO>> retrieveDatabases(@RequestBody List<String> instanceIds) {
        List<InstanceDbInfoDTO> instanceDbInfoByInstanceIdList = instanceDbInfoService.getInstanceDbInfoByInstanceIdList(instanceIds);
        Map<String, List<InstanceDbInfoDTO>> map = new HashMap<>(16);
        instanceDbInfoByInstanceIdList.forEach(instanceDbInfoDTO -> {
            String instanceId = instanceDbInfoDTO.getInstanceId();
            List<InstanceDbInfoDTO> dtos = map.computeIfAbsent(instanceId, k -> new ArrayList<>());
            dtos.add(instanceDbInfoDTO);
        });
        List<InstanceDBInfoListDTO> res = new ArrayList<>();
        map.forEach((k, v) -> {
            InstanceDBInfoListDTO dbInfoListDTO = new InstanceDBInfoListDTO();
            dbInfoListDTO.setInstanceId(k);
            dbInfoListDTO.setInstanceDbInfoDTOList(v);
            res.add(dbInfoListDTO);
        });
        return BaseResponse.success(res);
    }

    @ApiOperation("retrieve logic tables list by logic database id list")
    @PostMapping("/logicTables")
    public BaseResponse<Map<Long, List<LogicTableDTO>>> retrieveLogicTables(@RequestBody List<Long> logicDatabasesIds) {
        List<LogicTableDTO> logicTableDTOS = logicTableService.getLogicTableByLogicTableIdList(logicDatabasesIds);
        Map<Long, List<LogicTableDTO>> map = new HashMap<>(16);;
        for (LogicTableDTO dto : logicTableDTOS) {
            List<LogicTableDTO> tableDTOS = map.computeIfAbsent(dto.getLogicDbId(), k -> new ArrayList<>());
            tableDTOS.add(dto);
        }
        return BaseResponse.success(map);
    }


    @ApiOperation("retrieve all physical table info list by logic table id list")
    @PostMapping("/physicalTables")
    public BaseResponse<Map<Long, List<InstanceTableInfoDTO>>> retrievePhysicalTables(@RequestBody @Valid LogicTable2PhysicalTableReq req) {
        List<InstanceTableInfoDTO> instanceTableInfoByLogicTableIdList = instanceTableInfoService.getByLogicTableIdsAndPhyClusters(req.getLogicTableIdList(), req.getPhysicalClusterIdList());
        Map<Long, List<InstanceTableInfoDTO>> map = new HashMap<>(16);
        for (InstanceTableInfoDTO instanceTableInfoDTO : instanceTableInfoByLogicTableIdList) {
            Long logicTableId = instanceTableInfoDTO.getLogicTableId();
            List<InstanceTableInfoDTO> dtos = map.computeIfAbsent(logicTableId, k -> new ArrayList<>());
            dtos.add(instanceTableInfoDTO);
        }
        return BaseResponse.success(map);
    }

    @ApiOperation("retrieve all tables for one instance_id")
    @PostMapping("/dbphysicalTables")
    public BaseResponse<List<InstanceDbTableInfoListDTO>> retrieveDbPhysicalTables(@RequestBody @Valid InstanceTableInfoSearchReq req){
        List<InstanceTableInfoDTO> instanceTableInfoByLogicTableIdList = instanceTableInfoService.getByInstanceIdList(req.getInstanceIds(), req.getDbname(), req.getTableName());
         Map<String, List<InstanceTableInfoDTO>> map = new HashMap<>(16);
        for (InstanceTableInfoDTO instanceTableInfoDTO : instanceTableInfoByLogicTableIdList) {
            String instanceId = instanceTableInfoDTO.getInstanceId();
            List<InstanceTableInfoDTO> dtos = map.computeIfAbsent(instanceId, k -> new ArrayList<>());
            dtos.add(instanceTableInfoDTO);
        }
        List<InstanceDbTableInfoListDTO> res = new ArrayList<>();

        map.forEach((k, v) -> {
            Map<String, List<InstanceTableInfoDTO>> listMap = new HashMap<>(16);
            for (InstanceTableInfoDTO instanceTableInfoDTO : v) {
                listMap.computeIfAbsent(instanceTableInfoDTO.getDbName(), k1 -> new ArrayList<>());
                listMap.get(instanceTableInfoDTO.getDbName()).add(instanceTableInfoDTO);
            }
            InstanceDbTableInfoListDTO instanceDbTableInfoListDTO = new InstanceDbTableInfoListDTO();
            instanceDbTableInfoListDTO.setInstanceId(k);
            instanceDbTableInfoListDTO.setDatabases(new ArrayList<>());
            res.add(instanceDbTableInfoListDTO);
            listMap.forEach((k1,v1) -> {
                InstanceDbTableInfoListDTO.InstanceDbInfoListDTO instanceDbInfoListDTO = new InstanceDbTableInfoListDTO.InstanceDbInfoListDTO(k1, new ArrayList<>());
                instanceDbTableInfoListDTO.getDatabases().add(instanceDbInfoListDTO);
                for (InstanceTableInfoDTO instanceTableInfoDTO : v1) {
                    instanceDbInfoListDTO.getTables().add(instanceTableInfoDTO);
                }
            });
        });
        return BaseResponse.success(res);
    }


    @ApiOperation("retrieve all physical tables info list by ")
    @PostMapping("/physicalTablesByLogicDatabaseId")
    public BaseResponse<Map<Long, List<InstanceTableInfoDTO>>> retrievePhysicalTablesByLogicDatabaseId(@RequestBody @Valid List<Long> logicDatabaseIds) {
        Map<Long, List<InstanceTableInfoDTO>> instanceTableInfoByLogicTableIdList = instanceTableInfoService.getInstanceTableInfoByLogicDbIdList(logicDatabaseIds);
        return BaseResponse.success(instanceTableInfoByLogicTableIdList);
    }

    @ApiOperation("retrieve all physical table info list by instance_id list")
    @PostMapping("/tables")
    public BaseResponse<Map<Long, List<InstanceTableInfoDTO>>> retrieveInstanceIdPhysicalTables(@RequestBody @Valid List<Long> instanceDbIds) {
        List<InstanceTableInfoDTO> instanceTableInfoByLogicTableIdList = instanceTableInfoService.getInstanceTableInfoByInstanceDbIdList(instanceDbIds);
        Map<Long, List<InstanceTableInfoDTO>> map = new HashMap<>(16);
        for (InstanceTableInfoDTO instanceTableInfoDTO : instanceTableInfoByLogicTableIdList) {
            Long instanceDbId = instanceTableInfoDTO.getInstanceDbId();
            List<InstanceTableInfoDTO> dtos = map.computeIfAbsent(instanceDbId, k -> new ArrayList<>(1));
            dtos.add(instanceTableInfoDTO);
        }
        return BaseResponse.success(map);
    }

    @GetMapping("/getMasterInstance")
    @ApiOperation("根据实例id反查主实例")
    public BaseResponse<List<InstanceNewBaseInfoDTO>> getMasterInstance(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例ID列表,多个时用英文逗号分隔", required = true) @RequestParam("instanceIds") String instanceIds
    ) {
        try {
            List<InstanceNewBaseInfoDTO> result = instanceService.getMasterInstance(instanceIds);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getMasterInstance error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/masterListOfPhysicalCluster")
    @ApiOperation("获取物理集群的实例信息列表")
    public BaseResponse<PageResult<InstanceWithDetailInfoDTO>> masterListOfPhysicalCluster(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "物理集群 ID", required = true) @RequestParam(value = "physicalClusterId") String physicalClusterId,
            @ApiParam("页数") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam("单页个数") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize

    ) {
        try {
            PageResult<InstanceWithDetailInfoDTO> result = instanceService.masterListOfPhysicalCluster(username, physicalClusterId, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("masterListOfPhysicalCluster error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取slave信息列表")
    @GetMapping("/slaveList")
    public BaseResponse<InstanceSlaveInfoDTO> slaveList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "主实例的实例 id", required = true) @RequestParam(value = "instanceMasterId") String instanceMasterId
    ) {
        try {
            InstanceSlaveInfoDTO result = instanceService.slaveList(username, instanceMasterId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("slaveList error", e);
            return BaseResponse.fail(e);
        }
    }

}
