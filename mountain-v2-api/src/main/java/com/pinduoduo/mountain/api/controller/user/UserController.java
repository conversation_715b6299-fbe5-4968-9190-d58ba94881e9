package com.pinduoduo.mountain.api.controller.user;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.common.model.cmdb.CmdbBusinessDTO;
import com.pinduoduo.mountain.common.model.cmdb.CmdbUserServiceDTO;
import com.pinduoduo.mountain.service.user.dto.RoleAddResultDTO;
import com.pinduoduo.mountain.service.user.dto.UserInfoDTO;
import com.pinduoduo.mountain.service.user.impl.UserService;
import com.pinduoduo.mountain.service.user.request.OperateUserTopLogicDbReq;
import com.pinduoduo.mountain.service.user.request.UserReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("用户相关操作")
@RestController
@RequestMapping("/api/v2/user")
@Slf4j
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/list")
    @ApiOperation("获取用户列表")
    public BaseResponse<PageResult<UserInfoDTO>> list(
            @ApiParam(value = "用户名") @RequestParam(value = "username", required = false) String username,
            @ApiParam(value = "角色名") @RequestParam(value = "roleName", required = false) String roleName,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<UserInfoDTO> result = userService.list(username, roleName, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("user list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/userInfo")
    @ApiOperation("获取用户信息")
    public BaseResponse<UserInfoDTO> userInfo(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username
    ) {
        try {
            UserInfoDTO result = userService.userInfo(username);
            if (result == null) {
                userService.addUser(username);
                result = userService.userInfo(username);
            }
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("user info error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/updateRole")
    @ApiOperation("修改角色")
    public BaseResponse<RoleAddResultDTO> updateRole(
            @ApiParam(value = "角色信息", required = true) @Validated @RequestBody UserReq userReq
    ) {
        try {
            int row = userService.updateRole(userReq);
            RoleAddResultDTO result = new RoleAddResultDTO();
            result.setSuccess(true);
            result.setMessage("更新角色成功");
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("updateRole error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/businessList")
    @ApiOperation("获取用户相关业务线列表")
    public BaseResponse<List<CmdbBusinessDTO>> businessListByUser(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username
    ) {
        try {
            List<CmdbBusinessDTO> result = userService.getBusinessList(username);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("businessList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/serviceList")
    @ApiOperation("获取用户在业务线下相关服务列表")
    public BaseResponse<List<CmdbUserServiceDTO>> serviceListByUserBusiness(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username,
            @ApiParam(value = "中文业务线名称", required = true) @RequestParam(value = "businessName") String businessName,
            @ApiParam(value = "搜索服务的关键字") @RequestParam(value = "serviceNameLike", required = false) String serviceNameLike
    ) {
        try {
            List<CmdbUserServiceDTO> result = userService.getServiceListByBusiness(username, businessName, serviceNameLike);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("serviceList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/serviceListByUser")
    @ApiOperation("获取用户在业务线下相关服务列表")
    public BaseResponse<List<String>> serviceListByUser(
            @ApiParam(value = "用户名") @RequestParam(value = "username", required = false) String username,
            @RequestHeader(HEADER_GATEWAY_USERNAME) String ssoUser
    ) {
        try {
            if (username == null || username.isEmpty()) {
                username = ssoUser;
            }
            List<String> result = userService.getServiceListByUser(username);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("serviceListByUser error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/dbaList")
    @ApiOperation("获取DBA用户列表")
    public BaseResponse<List<String>> dbaList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username
    ) {
        try {
            List<String> result = userService.getDbaList();
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("dbaList error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/operateUserTopLogicDb")
    @ApiOperation("用户置顶或取消置顶逻辑库操作")
    public BaseResponse<Boolean> operateUserTopLogicDb(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "操作请求体", required = true) @Validated @RequestBody OperateUserTopLogicDbReq operateUserTopLogicDbReq
    ) {
        try {
            boolean result = userService.operateUserTopLogicDb(username, operateUserTopLogicDbReq);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("operateUserTopLogicDb error", e);
            return BaseResponse.fail(e);
        }
    }
}
