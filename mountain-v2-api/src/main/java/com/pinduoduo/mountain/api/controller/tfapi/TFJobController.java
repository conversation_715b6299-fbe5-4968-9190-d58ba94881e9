package com.pinduoduo.mountain.api.controller.tfapi;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.pinduoduo.mountain.api.model.request.order.ResetStatusReq;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDO;
import com.pinduoduo.mountain.service.teamflow.order.entity.TFOrderBasic;
import com.pinduoduo.mountain.service.teamflow.order.impl.TFOrderServiceImpl;
import com.yiran.arch.leo.util.LeoUtils;
import com.pinduoduo.mountain.api.dto.tfapi.TFJobReq;
import com.pinduoduo.mountain.common.bean.AuthUserPerms;
import com.pinduoduo.mountain.common.constant.OrderType;
import com.pinduoduo.mountain.common.dto.common.StatusCount;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.common.model.platform.BaseRetDTO;
import com.pinduoduo.mountain.common.thirdparty.omega.reponse.OmegaCallBackResponse;
import com.pinduoduo.mountain.common.thirdparty.omega.reponse.OmegaCreateTicketResponse;
import com.pinduoduo.mountain.common.thirdparty.omega.request.OmegaCallbackReq;
import com.pinduoduo.mountain.common.util.JSONUtils;
import com.pinduoduo.mountain.common.util.NumberUtil;
import com.pinduoduo.mountain.service.authmng.AuthMngService;
import com.pinduoduo.mountain.service.monitor.GenericResponse;
import com.pinduoduo.mountain.service.order.OrderCommonService;
import com.pinduoduo.mountain.service.teamflow.api.approval.TFJobApprovalService;
import com.pinduoduo.mountain.service.teamflow.api.validator.TFJobValidatorService;
import com.pinduoduo.mountain.service.teamflow.biz.TFJobBizApiService;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFJob;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFOrderStatusEnum;
import com.pinduoduo.mountain.service.teamflow.entity.tf.TFJobStatusEnum;
import com.pinduoduo.mountain.service.teamflow.execute.TFJobService;
import com.pinduoduo.mountain.service.teamflow.master.TFAppMng;
import com.pinduoduo.mountain.service.teamflow.master.TFTenantService;
import com.pinduoduo.mountain.service.teamflow.master.TFWorkerService;
import com.pinduoduo.mountain.service.user.impl.UserServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.pinduoduo.mountain.common.constant.Http.*;
import static com.pinduoduo.mountain.service.teamflow.entity.tf.TFJobStatusEnum.SCHEDULING;

@Slf4j
@RestController
@Api("TF Job Management")
@RequestMapping("/api/v2/tf/job")
public class TFJobController {

	// Collection of work order types that require DBA approval
	private Set<String> dbaReviewOrderSet;
	// Collection of work order types that require manual execution
	private Set<String> manualExecOrderSet;

	private Map<String, String> feStatusMap;

	@Resource
	private TFJobBizApiService tfJobBizApiService;
	@Resource
	private TFJobApprovalService tfJobApprovalService;
	@Resource
	private TFJobValidatorService tfJobValidatorService;
	@Resource
	private OrderCommonService orderCommonService;
	@Resource
	private UserServiceImpl userServiceImpl;
	@Resource
	private TFJobService tfJobService;

	@Resource
	private TFOrderServiceImpl tfOrderService;

	@Resource
	private AuthMngService authMngService;
	@Resource
	private TFTenantService tfTenantService;
	@Resource
	private TFWorkerService tfWorkerService;

	@PostConstruct
	public void init() {
		dbaReviewOrderSet = LeoUtils.getJsonProperty("mountain-v2-api.dba_review_order_set",
				new TypeReference<Set<String>>() {
				});
		manualExecOrderSet = LeoUtils.getJsonProperty("mountain-v2-api.manual_exec_order_set",
				new TypeReference<Set<String>>() {
				});
		feStatusMap = LeoUtils.getJsonProperty("mountain-v2-api.tf_status_fe_label_map",
				new TypeReference<Map<String, String>>() {
				});

		LeoUtils.addKeyChangeCallback("mountain-v2-api.dba_review_order_set", new TypeReference<Set<String>>() {
		}, (oldValue, newValue) -> dbaReviewOrderSet = newValue);
	}

	@GetMapping("/job/types")
	public BaseResponse<List<Pair<String, String>>> getJobList() {
		return BaseResponse.success(OrderType.getJobList());
	}

	/**
	 * curl -XPOST -H'gateway-username:feynman.zhou' -H'Content-Type:
	 * application/json' 'http://localhost:8080/api/v2/tf/job/add' -d ' { "title":
	 * "x3", "description": "This is a test job.", "operationType": "SimpleJob",
	 * "componentType": "componentType", "status": "SCHEDULING", "callbackUrl":
	 * "https://example.com/callback", "applyUser": "feynman.zhou", "business":
	 * "DBA", "orderSource": "mountain_v2", "orderSourceId": "1", "entity":
	 * "TEST_ENTITY", "tenantName": "feynman.zhou", "content": {"k1": "v1", "k2": 2}
	 * }'
	 *
	 * @param jobReq
	 * @return
	 */

	@Deprecated
	@PostMapping("/execute")
	public BaseResponse<Long> execute(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(HEADER_ENV) String env, @RequestBody TFJob jobReq) {
		Long workId = jobReq.getWorkId();

		TFJob job = tfJobBizApiService.query(workId);
		TFOrderBasic order = tfOrderService.selectTFOrderByWorkId(workId);

		if (job == null) {
			return BaseResponse.fail(String.format("Ticket %d does not exist", workId));
		}

		if (!userServiceImpl.isDBA(username, env) && !order.getApplyUser().equals(username)) {
			return BaseResponse.fail("You do not have permission to execute the ticket");
		}

		BaseRetDTO<Long> result = tfJobBizApiService.execute(job);

		if (!result.getResult()) {
			return BaseResponse.fail(result.getMessage());
		}

		return BaseResponse.success(result.getData());
	}

	@GetMapping("/query/cnt")
	public GenericResponse queryCnt(@RequestParam(value = "workId", required = false) Long workId,
			@RequestParam(value = "title", required = false) String name,
			@RequestParam(value = "description", required = false) String description,
			@RequestParam(value = "operationType", required = false) String type,
			@RequestParam(value = "entity", required = false) String entity,
			@RequestParam(value = "status", required = false) String status,
			@RequestParam(value = "creator", required = false) String applyUser,
			@RequestParam(value = "business", required = false) String business,
			@RequestHeader(value = HEADER_ENV, required = false) String env,
			@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestParam(value = "createdAtBegin") Long createdAtBegin,
			@RequestParam(value = "createdAtEnd") Long createdAtEnd) throws Exception {
		Date begin = new Date(createdAtBegin);
		Date end = new Date(createdAtEnd);
		List<String> types = null;
		if (StringUtils.isNotEmpty(type)) {
			types = Collections.singletonList(type);
		}
		List<String> statuses = TFOrderStatusEnum.getStatusListByOrderStatus(status);
		long cnt = tfJobBizApiService.queryBatchCnt(workId, name, description, types, entity, statuses, applyUser,
				business, begin, end);
		return new GenericResponse(true, cnt);
	}

	@GetMapping("/list")
	public BaseResponse<PageResult<TFJob>> list(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV, required = false) String env,
			@ApiParam("Page Number") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
			@ApiParam("Number of items per page") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
			@ApiParam("Work Order ID") @RequestParam(value = "workId", required = false) String workId,
			@ApiParam("Business Line") @RequestParam(value = "businessName", required = false) String businessName,
			@ApiParam("Order Status") @RequestParam(value = "orderStatus", required = false) String status,
			@ApiParam("Search Keyword") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
			@ApiParam("Work Order Start Time") @RequestParam(value = "createdAtBegin", required = false) Long createdAtBegin,
			@ApiParam("Work Order End Time") @RequestParam(value = "createdAtEnd", required = false) Long createdAtEnd,
			@ApiParam("View Only My Tickets") @RequestParam(value = "onlyMine", required = false, defaultValue = "false") boolean onlyMine,
			@ApiParam("Work Order Type") @RequestParam(value = "operationType", required = false) String operationType,
			@ApiParam("Applicant") @RequestParam(value = "applyUser", required = false) String applyUser) {
		if (onlyMine) {
			applyUser = username;
		}
		PageResult<TFJob> result = tfJobBizApiService.list(username, env, pageNum, pageSize, searchVal, workId, status,
				businessName, createdAtBegin, createdAtEnd, operationType, applyUser);
		return BaseResponse.success(result);
	}

	@GetMapping("/query")
	public GenericResponse query(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV, required = false) String env,
			@RequestParam(value = "workId", required = false) Long workId,
			@RequestParam(value = "title", required = false) String name,
			@RequestParam(value = "description", required = false) String description,
			@RequestParam(value = "operationType", required = false) String type,
			@RequestParam(value = "entity", required = false) String entity,
			@RequestParam(value = "status", required = false) String status,
			@RequestParam(value = "creator", required = false) String applyUser,
			@RequestParam(value = "business", required = false) String business,
			@RequestParam(value = "createdAtBegin", required = false) Long createdAtBegin,
			@RequestParam(value = "createdAtEnd", required = false) Long createdAtEnd,
			@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer cnt) throws Exception {
		int offset = (pageNum - 1) * cnt;
		Date end, begin;
		if (createdAtBegin == null) {
			begin = null;
		} else {
			begin = new Date(createdAtBegin);
		}
		if (createdAtEnd == null) {
			end = null;
		} else {
			end = new Date(createdAtEnd);
		}
		List<String> types = null;
		if (StringUtils.isNotEmpty(type)) {
			types = Collections.singletonList(type);
		}

		List<String> statuses = TFOrderStatusEnum.getStatusListByOrderStatus(status);
		List<TFJob> jobs = tfJobBizApiService.queryBatch(workId, name, description, types, entity, statuses, applyUser,
				business, begin, end, offset, cnt);
		return new GenericResponse(true, jobs);
	}

	@GetMapping("/query/detail")
	public GenericResponse query(@RequestParam(value = "workId", required = false) Long workId,
			@RequestHeader(value = HEADER_ENV, required = false) String env,
			@RequestHeader(HEADER_GATEWAY_USERNAME) String username) {
		TFJob job = tfJobBizApiService.query(workId);
		return new GenericResponse(true, job);
	}

	@GetMapping("/result/query")
	public GenericResponse queryResult(@RequestParam("workId") Long workId,
			@RequestParam(value = "business") String business,
			@RequestHeader(value = HEADER_ENV, required = false) String env,
			@RequestParam(value = "username") String username) {
		TFJob job = tfJobBizApiService.query(workId);
		return new GenericResponse(true, job);
	}

	@PostMapping("/status/update")
	public BaseResponse<Object> updateStatus(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(HEADER_ENV) String env, @RequestBody TFJobReq job) {
		long workId = job.getWorkId();
		String status = job.getStatus();

		boolean isExecuteOperation = status.equals(SCHEDULING.name());

		TFJob innerJob = tfJobBizApiService.query(workId);
		TFOrderBasic tfOrderDO = tfOrderService.selectTFOrderByWorkId(workId);

		if (!userServiceImpl.isDBA(username, env)
				&& !(isExecuteOperation && tfOrderDO.getApplyUser().equals(username))) {
			return BaseResponse.fail("You do not have permission to modify the ticket");
		}

		if (manualExecOrderSet.contains(innerJob.getOperationType())) {
			return BaseResponse.fail(
					"cannot reset to this work order status to WAIT_EXECUTE, it's not a manual execute work order");
		}
		Pair<Boolean, String> res = tfJobBizApiService.updateStatusByWorkId(workId,
				TFJobStatusEnum.valueOf(innerJob.getStatus()), TFJobStatusEnum.valueOf(status), username);
		log.info("updateStatus:id={},status={},res={}", workId, status, res);
		return res.getLeft() ? BaseResponse.success() : BaseResponse.fail(res.getRight());
	}

	@PostMapping("/suspend")
	public BaseResponse<Object> suspend(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV, required = false) String env, @RequestBody TFJobReq job) {
		long workId = job.getWorkId();
		TFJobStatusEnum status = job.getStatusEnum();
		TFJobStatusEnum preStatus = job.getPreStatusEnum();
		Pair<Boolean, String> res = tfJobBizApiService.updateStatusByWorkId(workId, preStatus, status, username);
		log.info("suspend:id={},preStatus={},status={},res={}", workId, preStatus, status, res);
		return res.getLeft() ? BaseResponse.success() : BaseResponse.fail(res.getRight());
	}

	@PostMapping("/reset")
	public BaseResponse<Object> reset(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV, required = false) String env, @RequestBody TFJobReq job) {
		long workId = job.getWorkId();
		TFJobStatusEnum status = job.getStatusEnum();
		TFJobStatusEnum preStatus = job.getPreStatusEnum();
		Pair<Boolean, String> res = tfJobBizApiService.updateStatusByWorkId(workId, preStatus, status, username);
		log.info("reset:id={},preStatus={},status={},res={}", workId, preStatus, status, res);
		return res.getLeft() ? BaseResponse.success() : BaseResponse.fail(res.getRight());
	}

	@GetMapping("/workerIp/list")
	public BaseResponse<Object> getWorkerIpList(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV, required = false) String env) {
		return BaseResponse.success(tfWorkerService.getOnlineWorkers());
	}

	@GetMapping("/job/workerIp/validStatusForUpdateWorkerIp")
	public BaseResponse<Object> updateWorkerIp(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV, required = false) String env) {
		return BaseResponse.success(TFJobStatusEnum.getValidStatusForUpdateWorkerIp());
	}

	@PostMapping("/workerIp/update")
	public BaseResponse<Object> updateWorkerIp(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(value = HEADER_ENV) String env, @RequestBody TFJobReq job) {
		if (!userServiceImpl.isDBA(username, env)) {
			return BaseResponse.fail("You are not allowed to update the worker ip(Do not have DBA permission)");
		}

		long workId = job.getWorkId();
		String workerIp = job.getWorkerIp();
		Pair<Boolean, String> res = tfJobBizApiService.updateWorkerIp(workId, workerIp, username);
		log.info("updateStatus:id={},workerIp={},res={}", workId, workerIp, res);
		return res.getLeft() ? BaseResponse.success() : BaseResponse.fail(res.getRight());
	}


	// Get the collection of work order types that need to be reviewed
	@GetMapping("/review/orderSet")
	public BaseResponse<Map<String, Boolean>> getDbaReviewOrderSet() {
		// This interface does not require authentication
		try {
			return BaseResponse.success(orderCommonService.getDbaReviewOrderSet());
		} catch (Exception e) {
			log.error("get The collection of work order types that need to be reviewed failed", e);
			return BaseResponse.fail(
					"get The collection of work order types that need to be reviewed failed: + " + e.getMessage());
		}
	}

	// 获取Job状态的集合列表
	@GetMapping("/status/list")
	public BaseResponse<List<Map<String, String>>> getStatusList() {
		try {
			List<Map<String, String>> result = new ArrayList<>(TFJobStatusEnum.toList());
			return BaseResponse.success(result);
		} catch (Exception e) {
			log.error("getStatusList error", e);
			return BaseResponse.fail("获取Job状态的集合列表失败: + " + e.getMessage());
		}
	}

	@PostMapping("/resetJobStatus")
	@ApiOperation("重置Job状态接口")
	public BaseResponse<String> resetOrderStatus(@RequestBody ResetStatusReq req, @RequestHeader(HEADER_GATEWAY_USERNAME) String username, @RequestHeader(HEADER_ENV) String env) {
		if (!userServiceImpl.isDBA(username, env)) {
			return BaseResponse.fail("你没有权限更改Job状态！");
		}
		int result = tfJobService.updateJobStatus(req.getWorkId(),null,null,null, TFJobStatusEnum.valueOf(req.getNewOrderStatus()),username);
		if(result>0){
			return BaseResponse.success("更新成功！");
		}else{
			return BaseResponse.fail("更新失败！");
		}
	}

}
