package com.pinduoduo.mountain.api.controller.user;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.user.dto.PermissionAddResultDTO;
import com.pinduoduo.mountain.service.user.dto.PermissionDTO;
import com.pinduoduo.mountain.service.user.impl.PermissionService;
import com.pinduoduo.mountain.service.user.request.PermissionReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api("权限相关操作")
@RestController
@RequestMapping("/api/v2/permission")
@Slf4j
public class PermissionController {

    private final PermissionService permissionService;

    public PermissionController(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @GetMapping("/list")
    @ApiOperation("获取权限列表")
    public BaseResponse<PageResult<PermissionDTO>> list(
            @ApiParam("权限名称") @RequestParam(value = "permissionName", required = false, defaultValue = "") String permissionName,
            @ApiParam("权限类型") @RequestParam(value = "permissionType", required = false, defaultValue = "") String permissionType,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<PermissionDTO> result = permissionService.list(permissionName, permissionType, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("permission list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/permissionInfo")
    @ApiOperation("获取权限信息")
    public BaseResponse<PermissionDTO> permissionInfo(
            @ApiParam("权限名称") @RequestParam(value = "permissionName", required = false, defaultValue = "") String permissionName
    ) {
        try {
            PermissionDTO result = permissionService.permissionInfo(permissionName);
            if (result == null) {
                return BaseResponse.fail("权限不存在");
            }
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("permission info error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/addPermission")
    @ApiOperation("新增权限")
    public BaseResponse<PermissionAddResultDTO> addPermission(
            @ApiParam(value = "信息", required = true) @Validated @RequestBody PermissionReq req
    ) {
        try {
            PermissionDTO pms = permissionService.permissionInfo(req.getPermissionName());
            if (pms != null) {
                return BaseResponse.fail("权限" + req.getPermissionName() + "已存在");
            }

            int row = permissionService.addPermission(req);
            PermissionAddResultDTO result = new PermissionAddResultDTO();
            if (row > 0) {
                result.setSuccess(true);
                result.setMessage("新增权限成功");
            }

            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("addPermission error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/updatePermission")
    @ApiOperation("修改权限")
    public BaseResponse<PermissionAddResultDTO> updatePermission(
            @ApiParam(value = "权限信息", required = true) @Validated @RequestBody PermissionReq req
    ) {
        try {
            int row = permissionService.updatePermission(req);
            PermissionAddResultDTO result = new PermissionAddResultDTO();
            result.setSuccess(true);
            result.setMessage("更新权限成功");
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("updatePermission error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/deletePermission")
    @ApiOperation("删除权限")
    public BaseResponse<PermissionAddResultDTO> deletePermission(
            @ApiParam(value = "权限信息", required = true) @Validated @RequestBody PermissionReq req
    ) {
        try {
            int row = permissionService.deletePermission(req);
            PermissionAddResultDTO result = new PermissionAddResultDTO();
            result.setPermissionId(req.getId());
            result.setSuccess(true);
            result.setMessage("删除权限成功");
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("updatePermission error", e);
            return BaseResponse.fail(e);
        }
    }
}
