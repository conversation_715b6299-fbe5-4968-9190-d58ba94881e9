package com.pinduoduo.mountain.api.controller.dashboard.service;

import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.GenericMetricConf;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

public interface GenericDataService {
    Map<String, Triple<Boolean, String, Object>> get(GenericQueryParam param);


    Integer getInteger(GenericMetricConf conf);

    Double getDouble(GenericMetricConf conf);

    String getString(GenericMetricConf conf);

    List<TsDataPoint> getTsData(GenericMetricConf conf);

    Map<String, Long> getAggregationData(GenericMetricConf conf);
}
