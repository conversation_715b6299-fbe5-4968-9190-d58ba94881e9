package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.InstanceDbInfo;
import com.pinduoduo.mountain.service.keychain.dto.KeychainMainPageDTO;
import com.pinduoduo.mountain.service.metadata.dto.InstanceNewBaseInfoDTO;
import com.pinduoduo.mountain.service.metadata.dto.LogicDatabaseNewBaseInfoDTO;
import com.pinduoduo.mountain.service.metadata.dto.LogicDatabaseNewListInfoDTO;
import com.pinduoduo.mountain.service.metadata.dto.LogicDatabaseNewSearchInfoDTO;
import com.pinduoduo.mountain.service.metadata.impl.LogicDatabaseService;
import com.pinduoduo.mountain.service.metadata.impl.LogicDbDetailAnalyzeService;
import com.pinduoduo.mountain.service.metadata.request.UpdateLogicDatabaseReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_ENV;
import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("新逻辑库相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/logicDatabaseNew")
public class LogicDatabaseNewController {

    private final LogicDatabaseService logicDatabaseService;
    private final LogicDbDetailAnalyzeService logicDbDetailAnalyzeService;

    public LogicDatabaseNewController(LogicDatabaseService logicDatabaseService, LogicDbDetailAnalyzeService logicDbDetailAnalyzeService) {
        this.logicDatabaseService = logicDatabaseService;
        this.logicDbDetailAnalyzeService = logicDbDetailAnalyzeService;
    }

    /**
     * 搜索用户可见的逻辑库，搜索范围
     * <ul>
     *     <li>逻辑库 ID（等值匹配）</li>
     *     <li>逻辑库名称（模糊匹配）</li>
     *     <li>逻辑库包含集群名称（模糊匹配）</li>
     *     <li>逻辑库包含物理集群名称（模糊匹配）</li>
     *     <li>逻辑库包含主实例的 instance_id（等值匹配）</li>
     * </ul>
     *
     * @param username  用户名
     * @param searchVal 搜索关键字
     * @return BaseResponse<List < LogicDatabaseSearchInfoDTO>>
     */
    @GetMapping("/search")
    @ApiOperation("搜索用户可见的逻辑库")
    public BaseResponse<List<LogicDatabaseNewSearchInfoDTO>> search(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
            @ApiParam(value = "返回数量") @RequestParam(value = "size", required = false, defaultValue = "100") int size
    ) {
        try {
            List<LogicDatabaseNewSearchInfoDTO> result = logicDatabaseService.search(username, env, searchVal, size);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("search error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/listOne")
    @ApiOperation("获取指定的逻辑库信息")
    public BaseResponse<LogicDatabaseNewListInfoDTO> listOne(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam("逻辑库 id") @RequestParam(value = "logicDbId") Long logicDbId
    ) {
        try {
            LogicDatabaseNewListInfoDTO result = logicDatabaseService.listOne(username, logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("listOne error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("listByNameAndTop")
    @ApiOperation("获取逻辑库列表，按照逻辑库名排序，并优先返回置顶的逻辑库")
    public BaseResponse<List<LogicDatabaseNewSearchInfoDTO>> listByNameAndTop(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
            @ApiParam(value = "返回数量") @RequestParam(value = "size", required = false, defaultValue = "100") int size
    ) {
        try {
            List<LogicDatabaseNewSearchInfoDTO> result = logicDatabaseService.listByNameAndTop(username, searchVal, env, size);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("search error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/count")
    @ApiOperation("获取用户可见的逻辑库总数量")
    public BaseResponse<Long> count(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env
    ) {
        try {
            Long result = logicDatabaseService.count(username, env);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("count error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/list")
    @ApiOperation("获取逻辑库列表")
    public BaseResponse<PageResult<LogicDatabaseNewListInfoDTO>> list(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("业务线中文名称") @RequestParam(value = "business", required = false, defaultValue = "") String business,
            @ApiParam("服务名") @RequestParam(value = "service", required = false, defaultValue = "") String service,
            @ApiParam("搜索关键字，搜索字段 logic_db_name") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal
    ) {
        try {
            PageResult<LogicDatabaseNewListInfoDTO> result = logicDatabaseService.list(username, env, pageNum, pageSize, business, service, searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/detailBaseInfo")
    @ApiOperation("获取逻辑库详情的基本信息")
    public BaseResponse<LogicDatabaseNewBaseInfoDTO> detailBaseInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId
    ) {
        try {
            LogicDatabaseNewBaseInfoDTO result = logicDatabaseService.detailBaseInfo(username, logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("detailBaseInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/logicDbMapPhyDbInfo")
    @ApiOperation("获取逻辑库对应的物理库和实例信息")
    public BaseResponse<List<InstanceDbInfo>> logicDbMapInstance(Long logicDbId) {
        try {
            return BaseResponse.success(logicDatabaseService.getLogicDbMapInstanceId(logicDbId));
        } catch (Exception e) {
            log.error("logicDbMapInstance failed", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/followServiceNow")
    @ApiOperation("获取物理库对应的服务信息")
    public BaseResponse<List<String>> followServiceNow(String instanceId) {
        try {
            return BaseResponse.success(logicDbDetailAnalyzeService.getVisitServiceAnalyze(instanceId));
        } catch (Exception e) {
            log.error("followServiceNow error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/followServiceDay")
    @ApiOperation("获取对应时间物理库来访服务信息")
    public BaseResponse<List<HashMap<String, String>>> followServiceDay(String instanceId, Integer dayNum) {
        try {
            return BaseResponse.success(logicDbDetailAnalyzeService.getVisitServiceByDay(instanceId, dayNum));
        } catch (Exception e) {
            log.error("followServiceDay error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/logicDbDetail")
    @ApiOperation("逻辑库详情首页信息")
    public BaseResponse<String> logicDbDetail() {
        return BaseResponse.success("");
    }


    @GetMapping("/logicDbTopo")
    @ApiOperation("获取逻辑库对应群集拓扑")
    public BaseResponse<Map<String, Map<String, Map<String, List<Object>>>>> logicDbTopology(@RequestHeader(HEADER_ENV) String env, Long logicDbId, Boolean showAll, String searchVal) {
        try {
            return BaseResponse.success(logicDatabaseService.getLogicDbTopology(logicDbId, env, showAll, searchVal));
        } catch (Exception e) {
            log.error("logicDbTopology error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/updateLogicDatabase")
    @ApiOperation("更新逻辑库相关信息")
    public BaseResponse<Boolean> updateLogicDatabase(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
                                                     @ApiParam(value = "更新逻辑库相关信息的请求体", required = true) @RequestBody UpdateLogicDatabaseReq updateLogicDatabaseReq) {
        try {
            return BaseResponse.success(logicDatabaseService.updateLogicDatabase(username, updateLogicDatabaseReq));
        } catch (Exception e) {
            log.error("updateLogicDatabase error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/setList")
    @ApiOperation("获取逻辑库的资源的单元列表")
    public BaseResponse<List<String>> setList(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
                                              @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") Long logicDbId,
                                              @ApiParam(value = "云环境", required = true) @RequestParam("cloudType") String cloudType) {
        try {
            return BaseResponse.success(logicDatabaseService.getLogicDatabaseSetList(logicDbId, cloudType));
        } catch (Exception e) {
            log.error("setList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/instanceList")
    @ApiOperation("获取逻辑库对应的实例列表")
    public BaseResponse<List<InstanceNewBaseInfoDTO>> instanceList(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
                                                                   @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") Long logicDbId) {
        try {
            return BaseResponse.success(logicDatabaseService.getLogicDatabaseInstanceList(logicDbId));
        } catch (Exception e) {
            log.error("instanceList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/cloudTypeList")
    @ApiOperation("获取逻辑库的云环境列表")
    public BaseResponse<List<String>> cloudTypeList(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
                                                    @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") Long logicDbId) {
        try {
            return BaseResponse.success(logicDatabaseService.getLogicDatabaseCloudTypeList(logicDbId));
        } catch (Exception e) {
            log.error("cloudTypeList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/keychainList")
    @ApiOperation("获取逻辑库的 keychain 列表")
    public BaseResponse<List<KeychainMainPageDTO>> keychainList(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
                                                                @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") Long logicDbId) {
        try {
            List<KeychainMainPageDTO> result = logicDatabaseService.getLogicDatabaseKeychainList(username, logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("keychainList error", e);
            return BaseResponse.fail(e);
        }
    }
}
