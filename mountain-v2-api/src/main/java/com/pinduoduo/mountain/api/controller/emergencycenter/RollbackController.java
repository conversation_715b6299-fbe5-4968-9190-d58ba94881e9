/**
 * <AUTHOR>
 * @date 2025/5/20
 * @description
 */

package com.pinduoduo.mountain.api.controller.emergencycenter;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.integration.rollback.bo.*;
import com.pinduoduo.mountain.service.emergencycenter.impl.RollbackService;
import com.site.lookup.util.StringUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.test.annotation.Rollback;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 急救中心 - 回档
 * <p>
 * <a href="https://cloud.tencent.com/document/product/236/18726">腾讯云回档接口</a>
 * <p>
 *
 * 百度云
 */
@ApiOperation("Emergency rollback")
@Slf4j
@RestController
@RequestMapping("/api/v2/emergencyCenter/rollback")
@Validated
public class RollbackController {

    private final RollbackService rollbackService;

    public RollbackController(RollbackService rollbackService) {
        this.rollbackService = rollbackService;
    }


    @PostMapping("/describeRollbackRangeTime")
    public BaseResponse< List<RollbackRangeTime>> describeRollbackRangeTime(@ApiParam(value = "instance_id list") @RequestBody List<String> instanceIdList){
        List<RollbackRangeTime> rangeTimes = this.rollbackService.describeRollbackRangeTime(instanceIdList);
        return BaseResponse.success(rangeTimes);
    }

    @PostMapping("/batchRollback")
    public BaseResponse<List<RollbackResp>> batchRollback(@ApiParam(value = "batch rollback requests") @RequestBody List<RollbackReq> rollbackReqs){
        List<RollbackResp> rollbackResps = this.rollbackService.batchRollback(rollbackReqs);
        return BaseResponse.success(rollbackResps);
    }

    @PostMapping("/describeRollbackTaskDetail")
    public BaseResponse<List<RollbackTaskDetail>> describeRollbackTaskDetail(@ApiParam(value = "instance_id list") @RequestBody List<String> instanceIdList){
        List<RollbackTaskDetail> rollbackTaskDetails = this.rollbackService.describeRollbackTaskDetail(instanceIdList);
        return BaseResponse.success(rollbackTaskDetails);
    }

    @GetMapping("/describeRollbackTaskDetailSingle")
    public BaseResponse<List<RollbackTaskDetail>> describeRollbackTaskDetail(@ApiParam(value = "instance_id list") @RequestParam String instanceId){
        List<String> instanceIdList = new ArrayList<>();
        instanceIdList.add(instanceId);
        List<RollbackTaskDetail> rollbackTaskDetails = this.rollbackService.describeRollbackTaskDetail(instanceIdList);
        if (rollbackTaskDetails.isEmpty()){
            return BaseResponse.success(rollbackTaskDetails);
        }
        List<RollbackTaskDetail> rollbackTaskDetailList = new ArrayList<>();
        for (RollbackTaskDetail rollbackTaskDetail : rollbackTaskDetails) {
            if (StringUtils.isEmpty(rollbackTaskDetail.getEndTime())){
                continue;
            }
            rollbackTaskDetailList.add(rollbackTaskDetail);
        }
        return BaseResponse.success("success", rollbackTaskDetailList);
    }

    @PostMapping("/batchStopRollback")
    public BaseResponse<List<StopRollbackResp>> batchStopRollback(@ApiParam(value = "batch stop rollback") @RequestBody List<String> instanceIdList){
        List<StopRollbackResp> stopRollbackResps = this.rollbackService.batchStopRollback(instanceIdList);
        return BaseResponse.success(stopRollbackResps);
    }






}
