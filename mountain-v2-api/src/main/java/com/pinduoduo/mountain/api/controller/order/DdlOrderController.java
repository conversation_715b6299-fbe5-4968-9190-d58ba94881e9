package com.pinduoduo.mountain.api.controller.order;

import com.pinduoduo.mountain.api.dto.order.*;
import com.pinduoduo.mountain.api.model.request.order.*;
import com.pinduoduo.mountain.api.service.order.DdlOtherService;
import com.pinduoduo.mountain.api.service.order.DdlSqlCheckService;
import com.pinduoduo.mountain.api.service.order.DdlSqlCommitService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.order.ddl.DdlLogDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("ddl 工单接口")
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v2/sqlOrder")
public class DdlOrderController {

    private final DdlOtherService ddlOtherService;
    private final DdlSqlCheckService ddlSqlCheckService;
    private final DdlSqlCommitService ddlSqlCommitService;


    public DdlOrderController(DdlOtherService ddlOtherService, DdlSqlCheckService ddlSqlCheckService, DdlSqlCommitService ddlSqlCommitService) {
        this.ddlOtherService = ddlOtherService;
        this.ddlSqlCheckService = ddlSqlCheckService;
        this.ddlSqlCommitService = ddlSqlCommitService;
    }

    @PostMapping("/sqlCheck")
    @ApiOperation("sql 检测")
    public BaseResponse<SqlCheckResultDTO> sqlCheck(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "sql 检测请求体", required = true) @RequestBody SqlCheckReq req
    ) {
        try {
            SqlCheckResultDTO result = ddlSqlCheckService.sqlCheck(env, username, req.getLogicDbId(), req.getDdlClusterId(), req.getSql(), req.getSqlType());
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("sqlCheck error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/securityKey")
    @ApiOperation("获取加密 key 列表")
    public BaseResponse<List<String>> securityKey(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "逻辑库 id", required = true) @RequestParam("logicDbId") Long logicDbId,
            @ApiParam(value = "ddl 集群 id", required = true) @RequestParam("ddlClusterId") Long ddlClusterId
    ) {
        try {
            List<String> result = ddlOtherService.securityKey(env, username, logicDbId, ddlClusterId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("securityKey error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/bigdataUseStatus")
    @ApiOperation("大数据使用情况")
    public BaseResponse<BigdataUseStatusDTO> bigdataUseStatus(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "逻辑库 id", required = true) @RequestParam("logicDbId") Long logicDbId,
            @ApiParam(value = "ddl 集群 id", required = true) @RequestParam("ddlClusterId") Long ddlClusterId
    ) {
        try {
            BigdataUseStatusDTO result = ddlOtherService.bigdataUseStatus(env, username, logicDbId, ddlClusterId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("bigdataUseStatus error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/sqlCommit")
    @ApiOperation("sql 工单提交")
    public BaseResponse<SqlCommitResultDTO> sqlCommit(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "sql 工单提交请求体", required = true) @Validated @RequestBody SqlCommitReq req
    ) {
        try {
            SqlCommitResultDTO result = ddlSqlCommitService.sqlCommit(env, username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("sqlCommit error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 获取 sql 工单列表
     * searchVal 参数搜索范围：
     * - 工单 ID，等值查询
     * - 逻辑库名，等值查询
     * - 集群名，等值查询
     * - 用户名，等值查询
     * - 业务线，等值查询
     */
    @GetMapping("/list")
    @ApiOperation("sql 工单列表")
    public BaseResponse<PageResult<OrderSqlDTO>> list(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "状态，可选值：omega审批中 omega已关闭 待执行 执行中 执行成功 执行失败 已终止") @RequestParam(value = "status", required = false) String searchStatus,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false) String searchVal) {
        try {
            PageResult<OrderSqlDTO> result = ddlOtherService.list(env, username, pageNum, pageSize, searchStatus, searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("sqlList error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 获取 sql 工单详情
     */
    @GetMapping("/detail")
    @ApiOperation("sql 工单详情")
    public BaseResponse<OrderSqlDetailDTO> detail(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "工单 id", required = true) @RequestParam("workId") Long workId) {
        try {
            OrderSqlDetailDTO result = ddlOtherService.detail(env, username, workId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("sqlDetail error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 获取 ddl 子单列表
     */
    @GetMapping("/ddlSubList")
    @ApiOperation("获取 ddl 子单列表")
    public BaseResponse<PageResult<OrderDdlSubDTO>> ddlSubList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "搜索关键字，提示文案：ip:port/库名/表名") @RequestParam(value = "searchVal", required = false) String searchVal,
            @ApiParam(value = "状态，可选值：待执行 待调度 执行中 执行成功 执行失败 已终止 跳过执行") @RequestParam(value = "status", required = false) String status,
            @ApiParam(value = "工单 id", required = true) @RequestParam("workId") Long workId,
            @ApiParam(value = "步骤") @RequestParam(value = "step", required = false, defaultValue = "1") Integer step) {
        try {
            PageResult<OrderDdlSubDTO> result = ddlOtherService.ddlSubList(env, username, pageNum, pageSize, searchVal, status, workId, step);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("ddlSubList error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 更新 ddl 执行配置
     */
    @PostMapping("/updateDdlConfig")
    @ApiOperation("更新 ddl 执行配置，成功时返回空字符串，失败时返回提示信息")
    public BaseResponse<Boolean> updateDdlConfig(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "更新 ddl 执行配置请求体", required = true) @Validated @RequestBody UpdateDdlConfigReq req) {
        try {
            Boolean result = ddlOtherService.updateDdlConfig(env, username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("updateDdlConfig error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 操作 ddl
     */
    @PostMapping("/operateDdl")
    @ApiOperation("操作 ddl，成功时返回空字符串，失败时返回提示信息")
    public BaseResponse<Boolean> operateDdl(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "操作 ddl 请求体", required = true) @RequestBody OperateDdlReq req) {
        try {
            Boolean result = ddlOtherService.operateDdl(env, username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("executeDdl error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 表结构校验
     */
    @PostMapping("/tableStructureVerify")
    @ApiOperation("表结构校验")
    public BaseResponse<TableStructureVerifyDTO> tableStructureVerify(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam("env") String env,
            @ApiParam(value = "工单 id", required = true) @RequestParam("workId") Long workId) {
        try {
            TableStructureVerifyDTO result = ddlOtherService.tableStructureVerify(workId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("tableStructureVerify error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/statusList")
    @ApiOperation("工单状态列表")
    public BaseResponse<List<String>> statusList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username
    ) {
        try {
            List<String> result = ddlOtherService.statusList(username);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("statusList error", e);
            return BaseResponse.fail(e);
        }
    }


    @PostMapping("/genOrderByV1")
    @ApiOperation("根据 v1 工单生成 v2 工单")
    public BaseResponse<Boolean> genOrderByV1(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "请求体信息", required = true) @RequestBody V1OrderReq req) {
        try {
            Boolean result = ddlOtherService.genOrderByV1(username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("genOrderByV1 error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/execOrderByV1")
    @ApiOperation("根据 v1 工单执行 v2 工单")
    public BaseResponse<Boolean> execOrderByV1(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "请求体信息", required = true) @RequestBody V1OrderReq req) {
        try {
            Boolean result = ddlOtherService.execOrderByV1(username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("execOrderByV1 error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/terminateOrderByV1")
    @ApiOperation("根据 v1 工单终止 v2 工单")
    public BaseResponse<Boolean> terminateOrderByV1(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "请求体信息", required = true) @RequestBody V1OrderReq req) {
        try {
            Boolean result = ddlOtherService.terminateOrderByV1(username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("terminateOrderByV1 error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/ddlLadonLog")
    @ApiOperation("获取 ddl 调度日志")
    public BaseResponse<PageResult<DdlLogDTO>> ddlLadonLog(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "工单 id", required = true) @RequestParam("workId") Long workId,
            @ApiParam(value = "子 ddl id", required = true) @RequestParam("ddlSubId") Long ddlSubId
    ) {
        try {
            PageResult<DdlLogDTO> result = ddlOtherService.ddlLadonLog(username, pageNum, pageSize, workId, ddlSubId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("ddlLadonLog error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/ddlGhostLog")
    @ApiOperation("获取 ddl ghost 日志")
    public BaseResponse<PageResult<DdlLogDTO>> ddlGhostLog(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "工单 id", required = true) @RequestParam("workId") Long workId,
            @ApiParam(value = "子 ddl id", required = true) @RequestParam("ddlSubId") Long ddlSubId
    ) {
        try {
            PageResult<DdlLogDTO> result = ddlOtherService.ddlGhostLog(username, pageNum, pageSize, workId, ddlSubId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("ddlGhostLog error", e);
            return BaseResponse.fail(e);
        }
    }

}
