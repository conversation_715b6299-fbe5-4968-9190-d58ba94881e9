package com.pinduoduo.mountain.api.service.inspectboard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectBoardSummaryDTO;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectDataDetailDTO;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectErrorTrendDTO;
import com.pinduoduo.mountain.api.dto.inspectboard.InspectLogListDTO;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectAddWhiteReq;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectResultAddReq;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectTaskAddReq;
import com.pinduoduo.mountain.api.model.request.inspectboard.InspectTaskUpdateReq;
import com.pinduoduo.mountain.api.service.dbtracking.DbtrackingService;
import com.pinduoduo.mountain.api.service.tank.TankStatusService;
import com.pinduoduo.mountain.common.constant.InspectConstant;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.common.model.ResultOrder;
import com.pinduoduo.mountain.common.model.request.inspectboard.InspectAddWhiteItem;
import com.pinduoduo.mountain.common.thirdparty.inform.InformService;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.InstanceMapper;
import com.pinduoduo.mountain.repository.mysql.pddmountain.entity.*;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.*;
import com.pinduoduo.mountain.repository.mysql.pddriver.entity.*;
import com.pinduoduo.mountain.repository.mysql.pddriver.mapper.*;
import com.pinduoduo.mountain.service.user.impl.UserService;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InspectBoardService {
    private final InspectTaskMapper inspectTaskMapper;
    private final InspectLogMapper inspectLogMapper;
    private final InspectDataMapper inspectDataMapper;
    private final InspectDataDetailMapper inspectDataDetailMapper;
    private final InspectWhiteListMapper inspectWhiteListMapper;

    private final UserService userService;
    private final DbtrackingProjectMapper dbtrackingProjectMapper;
    private final DbtrackingService dbtrackingService;
    private final InstanceMapper instanceMapper;
    private final BatClusterMapper batClusterMapper;
    private final BatHostMapper batHostMapper;
    private final PrsInstanceMapper prsInstanceMapper;
    private final CodisClusterMapper codisClusterMapper;
    private final CodisServerMapper codisServerMapper;
    private final TankStatusService tankStatusService;
    private final InformService informService;

    public InspectBoardService(@Qualifier("inspectTaskMapper") InspectTaskMapper inspectTaskMapper, @Qualifier("inspectLogMapper") InspectLogMapper inspectLogMapper, @Qualifier("inspectDataMapper") InspectDataMapper inspectDataMapper, @Qualifier("inspectDataDetailMapper") InspectDataDetailMapper inspectDataDetailMapper, UserService userService, @Qualifier("inspectWhiteListMapper") InspectWhiteListMapper inspectWhiteListMapper, DbtrackingProjectMapper dbtrackingProjectMapper, DbtrackingService dbtrackingService, @Qualifier("instanceMapper") InstanceMapper instanceMapper, @Qualifier("batClusterMapper") BatClusterMapper batClusterMapper, @Qualifier("batHostMapper") BatHostMapper batHostMapper, @Qualifier("prsInstanceMapper") PrsInstanceMapper prsInstanceMapper, @Qualifier("codisClusterMapper") CodisClusterMapper codisClusterMapper, @Qualifier("codisServerMapper") CodisServerMapper codisServerMapper, TankStatusService tankStatusService, InformService informService) {
        this.inspectTaskMapper = inspectTaskMapper;
        this.inspectLogMapper = inspectLogMapper;
        this.inspectDataMapper = inspectDataMapper;
        this.inspectDataDetailMapper = inspectDataDetailMapper;
        this.userService = userService;
        this.inspectWhiteListMapper = inspectWhiteListMapper;
        this.dbtrackingProjectMapper = dbtrackingProjectMapper;
        this.dbtrackingService = dbtrackingService;
        this.instanceMapper = instanceMapper;
        this.batClusterMapper = batClusterMapper;
        this.batHostMapper = batHostMapper;
        this.prsInstanceMapper = prsInstanceMapper;
        this.codisClusterMapper = codisClusterMapper;
        this.codisServerMapper = codisServerMapper;
        this.tankStatusService = tankStatusService;
        this.informService = informService;
    }

    public InspectBoardSummaryDTO summary(String date) {
        InspectBoardSummaryDTO result = new InspectBoardSummaryDTO();

        // 1、统计 dba 异常任务
        List<InspectTask> inspectTaskList = inspectTaskMapper.selectAll();
        Map<String, InspectTask> inspectTaskMap = inspectTaskList.stream().collect(HashMap::new, (m, v) -> m.put(v.getCode(), v), HashMap::putAll);

        List<InspectLog> unHealthyInspectLogList = inspectLogMapper.selectErrorLogByDate(date);
        Map<String, Integer> dbaOwner2Counts = new HashMap<>(16);
        for (InspectLog inspectLog : unHealthyInspectLogList) {
            InspectTask inspectTask = inspectTaskMap.get(inspectLog.getCode());
            if (inspectTask != null) {
                String dbaOwner = inspectTask.getDbaOwner();
                dbaOwner2Counts.put(dbaOwner, dbaOwner2Counts.getOrDefault(dbaOwner, 0) + 1);
            }
        }
        result.setDbaCount(dbaOwner2Counts.entrySet().stream().map(e -> new InspectBoardSummaryDTO.LabelValue(e.getKey(), e.getValue())).collect(Collectors.toList()));

        // 2、统计健康和不健康任务
        int healthyCount = inspectLogMapper.selectNotErrorLogCountByDate(date);
        int unhealthyCount = unHealthyInspectLogList.size();
        result.setHealthyCount(new ArrayList<InspectBoardSummaryDTO.LabelValue>() {{
            add(new InspectBoardSummaryDTO.LabelValue("健康数", healthyCount));
            add(new InspectBoardSummaryDTO.LabelValue("不健康数", unhealthyCount));
        }});

        // 3、统计成功和不成功任务
        int successCount = inspectTaskMapper.selectSuccessCountByDate(date);
        int notSuccessCount = inspectTaskMapper.selectNotSuccessCountByDate(date);
        result.setStatusCount(new ArrayList<InspectBoardSummaryDTO.LabelValue>() {{
            add(new InspectBoardSummaryDTO.LabelValue("成功数", successCount));
            add(new InspectBoardSummaryDTO.LabelValue("不成功数", notSuccessCount));
        }});

        return result;
    }

    public PageResult<InspectLogListDTO> logList(String date, String searchVal, int pageNum, int pageSize, String order) {
        // 获取所有巡检任务并转换为 code -> InspectTask 的 map
        List<InspectTask> allInspectTaskList = inspectTaskMapper.selectAll();
        Map<String, InspectTask> allInspectTaskMap = allInspectTaskList.stream().collect(HashMap::new, (m, v) -> m.put(v.getCode(), v), HashMap::putAll);

        // 获取根据 dba_owner 搜索的巡检任务
        List<InspectTask> searchInspectTaskList = inspectTaskMapper.selectByDbaOwnerLike(searchVal);
        String searchCodesInSql = String.format("'%s'", searchInspectTaskList.stream().map(InspectTask::getCode).collect(Collectors.joining("', '")));

        // 获取分页的巡检日志
        List<InspectLog> inspectLogList;
        try (Page<Object> ignored = PageHelper.startPage(pageNum, pageSize)) {
            List<ResultOrder> resultOrders = ResultOrder.getOrderList(order);
            String checkMsg = ResultOrder.checkValid(resultOrders, new HashSet<>(Arrays.asList("name", "code", "total", "error", "ignore_count", "error_rate")));
            if (checkMsg != null && !checkMsg.isEmpty()) {
                throw new RuntimeException(checkMsg);
            }
            // error_rate 排序，需要转为 error/total
            for (ResultOrder resultOrder : resultOrders) {
                if (resultOrder.getFieldName().equals("error_rate")) {
                    resultOrder.setFieldName("error/total");
                }
            }
            inspectLogList = inspectLogMapper.selectByDateAndCodeAndSearchVal(date, searchCodesInSql, searchVal, resultOrders);
        }

        List<InspectLogListDTO> pageResultList = new ArrayList<>(inspectLogList.size());
        for (InspectLog inspectLog : inspectLogList) {
            pageResultList.add(new InspectLogListDTO(inspectLog, allInspectTaskMap.get(inspectLog.getCode())));
        }

        return PageResult.of(pageResultList, new PageInfo<>(inspectLogList));
    }

    public void download(String code, String date, HttpServletResponse response) {
        try (OutputStream outputStream = response.getOutputStream()) {
            InspectData inspectData = inspectDataMapper.selectOneByCodeAndDate(code, date);
            // inspectData.getData() eg:
            //{
            //    "inform": 1,
            //    "name": "CDB主从磁盘配置不一致",
            //    "code": "cdb_master_slave_volume_check",
            //    "start_time": "2024-12-24 01:01:05",
            //    "error_list": [
            //        {
            //            "pdd_id": "pdd-5tl15n9t",
            //            "vol_rate": 1,
            //            "ip": "************",
            //            "port": 3320,
            //            "instance_name": "sp4_orchestrator_pdbha-backup",
            //            "master_volume": 2000,
            //            "slave_volume": 1000,
            //            "business": "数据库运维",
            //            "db_owner": "baiyutang,lien,zhanbai,shugui,yaoyang,luziqian,shuyu,pingmo",
            //            "service_name": "DBA",
            //            "master_id": "pdd-jo5o5jkc",
            //            "desc": "master_slave_volumn_diff"
            //        },
            //        ......
            //    ],
            //    "total": 89733,
            //    "error": 868,
            //    "end_time": "2024-12-24 01:01:08",
            //    "db_type": "MySQL",
            //    "unique_id_column": "pdd_id",
            //    "is_healthy": "0",
            //    "atMans": [
            //        "shugui"
            //    ],
            //    "project_code": ""
            //}
            JsonNode dataJsonNode = StringUtil.getObjectMapper().readTree(inspectData.getData());
            // 设置响应头
            String fileName = String.format("%s-%s.csv", code, date);
            response.setContentType("text/csv");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 响应开头写入 UTF-8 BOM，添加该 bom 可以识别中文字符
            outputStream.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            // 写入 CSV 数据
            if (!dataJsonNode.isEmpty()) {
                // 写入表头
                JsonNode errorListOneJsonNode = dataJsonNode.get("error_list").get(0);
                if (errorListOneJsonNode == null) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    throw new RuntimeException("error_list 为空，不可导出");
                }
                StringBuilder csvHeader = new StringBuilder();
                for (Iterator<String> it = errorListOneJsonNode.fieldNames(); it.hasNext(); ) {
                    String fieldName = it.next();
                    csvHeader.append(fieldName).append(",");
                }
                csvHeader.append("\n");
                outputStream.write(csvHeader.toString().getBytes(StandardCharsets.UTF_8));

                // 写入数据行
                for (JsonNode errorJsonNode : dataJsonNode.get("error_list")) {
                    StringBuilder csvRow = new StringBuilder();
                    for (Iterator<String> it = errorJsonNode.fieldNames(); it.hasNext(); ) {
                        String fieldName = it.next();
                        String fieldValue = errorJsonNode.get(fieldName).asText();
                        csvRow.append(fieldValue).append(",");
                    }
                    csvRow.append("\n");
                    outputStream.write(csvRow.toString().getBytes(StandardCharsets.UTF_8));
                }
            }
            outputStream.flush();
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("download error", e);
            throw new RuntimeException(e);
        }
    }

    public List<InspectErrorTrendDTO> errorTrend(String beginDate, String endDate, String searchVal) {
        if (Strings.isBlank(beginDate)) {
            beginDate = LocalDate.now().minusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        if (Strings.isBlank(endDate)) {
            endDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        // 如果 beginDate == endDate，则把 beginDate 重置为 endDate 的前 7 天
        if (beginDate.equals(endDate)) {
            beginDate = LocalDate.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE).minusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        List<String> codeList = inspectTaskMapper.selectCodeBySearchVal(searchVal);
        String codesInSql = String.format("'%s'", String.join("','", codeList));
        List<InspectLog> inspectLogList = inspectLogMapper.selectByBetweenDateAndCodes(beginDate, endDate, codesInSql);

        List<InspectErrorTrendDTO> result = new ArrayList<>();
        for (InspectLog inspectLog : inspectLogList) {
            result.add(new InspectErrorTrendDTO(inspectLog));
        }
        return result;
    }

    public PageResult<InspectDataDetailDTO> inspectDataDetailList(String beginDate, String endDate, String searchVal, Integer isHealthy, String instanceCluster, String business, int pageNum, int pageSize, String orderStr) {
        if (Strings.isBlank(beginDate)) {
            beginDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        if (Strings.isBlank(endDate)) {
            endDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        List<String> codeList = inspectTaskMapper.selectCodeBySearchVal(searchVal);
        List<InspectDataDetail> inspectDataDetailList;
        try (Page<Object> ignored = PageHelper.startPage(pageNum, pageSize)) {
            List<ResultOrder> resultOrderList = ResultOrder.getOrderList(orderStr);
            String checkMsg = ResultOrder.checkValid(resultOrderList, new HashSet<>(Arrays.asList("inspect_date", "inspect_code", "inspect_name", "business", "unique_id", "unique_name", "created_at")));
            if (checkMsg != null && !checkMsg.isEmpty()) {
                throw new RuntimeException(checkMsg);
            }
            inspectDataDetailList = inspectDataDetailMapper.selectByDynamicCondition(beginDate, endDate, isHealthy, instanceCluster, business, codeList, resultOrderList);
        }

        List<InspectDataDetailDTO> pageResult = new ArrayList<>(inspectDataDetailList.size());
        for (InspectDataDetail inspectDataDetail : inspectDataDetailList) {
            pageResult.add(new InspectDataDetailDTO(inspectDataDetail));
        }

        return PageResult.of(pageResult, new PageInfo<>(inspectDataDetailList));
    }

    public void inspectDataDetailListExport(String beginDate, String endDate, String searchVal, Integer isHealthy, String instanceCluster, String business, int pageNum, int pageSize, String order, HttpServletResponse response) {
        PageResult<InspectDataDetailDTO> pageResult = inspectDataDetailList(beginDate, endDate, searchVal, isHealthy, instanceCluster, business, pageNum, pageSize, order);

        String fileName = String.format("inspect_data_detail_%s_%s_%s.csv", beginDate, business, instanceCluster);
        response.setContentType("text/csv");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

        try (OutputStream outputStream = response.getOutputStream()) {
            // 写入 BOM 头以支持 UTF-8, 添加该 bom 可以识别中文字符
            outputStream.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            if (pageResult.getData().isEmpty()) {
                return;
            }

            // 先写表头
            String csvHeaders = "inspectDate,inspectCode,inspectName,business,uniqueId,uniqueName,isHealthy,otherData,createdAt\n";
            outputStream.write(csvHeaders.getBytes(StandardCharsets.UTF_8));

            // 再写数据
            for (InspectDataDetailDTO inspectDataDetailDTO : pageResult.getData()) {
                String row = String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                        inspectDataDetailDTO.getInspectDate(),
                        inspectDataDetailDTO.getInspectCode(),
                        inspectDataDetailDTO.getInspectName(),
                        inspectDataDetailDTO.getBusiness(),
                        inspectDataDetailDTO.getUniqueId(),
                        inspectDataDetailDTO.getUniqueName(),
                        inspectDataDetailDTO.getIsHealthy(),
                        inspectDataDetailDTO.getOtherData(),
                        inspectDataDetailDTO.getCreatedAt());
                outputStream.write(row.getBytes(StandardCharsets.UTF_8));
            }
            outputStream.flush();
        } catch (Exception e) {
            log.error("inspectDataDetailListExport error", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            throw new RuntimeException(e);
        }
    }

    public void addWhitelist(String username, InspectAddWhiteReq req) {
        if (!userService.isDBA(username)) {
            throw new RuntimeException("仅限 DBA 执行加白操作");
        }

        // 检查 code 是否已接入问题管理系统，如果有接入的，需要在问题管理系统操作
        for (InspectAddWhiteItem inspectAddWhiteItem : req.getWhiteList()) {
            InspectTask inspectTask = inspectTaskMapper.selectByCode(inspectAddWhiteItem.getInspectCode());
            if (inspectTask.getProjectCode() != null && !inspectTask.getProjectCode().isEmpty()) {
                throw new RuntimeException(String.format("巡检项 %s 已接入问题管理系统，禁止通过巡检大盘加白，请在问题管理系统进行加白！", inspectAddWhiteItem.getInspectCode()));
            }
        }

        for (InspectAddWhiteItem inspectAddWhiteItem : req.getWhiteList()) {
            InspectWhiteList inspectWhiteList = inspectWhiteListMapper.selectByInspectCodeAndWhiteList(inspectAddWhiteItem.getInspectCode(), inspectAddWhiteItem.getUniqueId());
            if (inspectWhiteList != null) {
                inspectWhiteListMapper.updateById(inspectWhiteList.getId(), inspectAddWhiteItem.getBusiness(), inspectAddWhiteItem.getUniqueId(), req.getRemark(), username, req.getBeginTime(), req.getEndTime());
                log.warn("巡检加白已存在相同项，执行更新: {}", inspectAddWhiteItem);
            } else {
                inspectWhiteListMapper.insertOne(inspectAddWhiteItem.getInspectCode(), inspectAddWhiteItem.getBusiness(), inspectAddWhiteItem.getUniqueId(), req.getRemark(), username, req.getBeginTime(), req.getEndTime());
                log.info("巡检加白成功: {}", inspectAddWhiteItem);
            }
        }
    }

    public List<String> getInspectDbTypes() {
        return LeoUtils.getJsonProperty("mountain-v2-api.inspect_db_type", new TypeReference<List<String>>() {
        });
    }

    public PageResult<InspectTask> taskList(String searchVal, int pageNum, int pageSize, String order) {
        List<InspectTask> inspectTaskList;
        try (Page<Object> ignored = PageHelper.startPage(pageNum, pageSize)) {
            List<ResultOrder> orderList = ResultOrder.getOrderList(order);
            String checkMsg = ResultOrder.checkValid(orderList, new HashSet<>(Arrays.asList("id", "name", "code", "need_inform", "is_inspect", "dba_owner", "create_time", "last_inspect_time")));
            if (checkMsg != null && !checkMsg.isEmpty()) {
                throw new RuntimeException(checkMsg);
            }
            inspectTaskList = inspectTaskMapper.selectBySearchVal(searchVal, orderList);
        }
        return PageResult.of(inspectTaskList, new PageInfo<>(inspectTaskList));
    }

    public void taskAdd(String username, InspectTaskAddReq req) {
        if (!userService.isDBA(username)) {
            throw new RuntimeException("仅限 DBA 执行添加巡检任务操作");
        }

        inspectTaskMapper.insertOne(req.getCode(), req.getName(), req.getDbType(), req.getUniqueIdColumn(), req.getNeedInform(), req.getIsInspect(), req.getDbaOwner(), req.getRemark());
    }

    public void taskUpdate(String username, InspectTaskUpdateReq req) {
        if (!userService.isDBA(username)) {
            throw new RuntimeException("仅限 DBA 执行修改巡检任务操作");
        }

        inspectTaskMapper.updateById(req.getId(), req.getName(), req.getDbType(), req.getUniqueIdColumn(), req.getNeedInform(), req.getIsInspect(), req.getDbaOwner(), req.getRemark());
    }

    public String addInspectResult(String username, InspectResultAddReq req) {
        // 检查巡检任务是否存在
        InspectTask inspectTask = inspectTaskMapper.selectByCode(req.getCode());
        if (inspectTask == null) {
            String link = LeoUtils.getStringProperty("mountain-v2-api.add_inspect_link", "");
            throw new RuntimeException(String.format("巡检 code %s 找不到对应巡检任务, 请检查或添加巡检任务（添加指引链接：%s）", req.getCode(), link));
        }

        InspectResultAddReq newReq = InspectResultAddReq.fillWithInspectTask(req, inspectTask);
        InspectResultAddReq ignoreReq = new InspectResultAddReq();
        BeanUtils.copyProperties(newReq, ignoreReq);
        ignoreReq.setErrorList(new ArrayList<>());
        ignoreReq.setError(0);
        ignoreReq.setIsHealthy(2);

        // 如果没有接入问题管理，进行一下白名单过滤
        if (inspectTask.getProjectCode() == null || inspectTask.getProjectCode().isEmpty()) {
            procErrorListByWhiteList(req, newReq, ignoreReq);
        }

        // 写入巡检记录 inspect_log，inspect_data
        LocalDate inspectDate = LocalDate.now();
        inspectLogMapper.replaceInto(new InspectLog(null, newReq.getName(), newReq.getCode(), inspectDate, newReq.getTotal(), newReq.getError(), null,
                newReq.getStartTime(), newReq.getEndTime(), null, ignoreReq.getError()));
        inspectDataMapper.replaceInto(new InspectData(null, null, StringUtil.jsonSerialize(newReq), newReq.getCode(), inspectDate, null, StringUtil.jsonSerialize(ignoreReq)));
        inspectTaskMapper.updateLastInspectTimeByCode(inspectTask.getCode(), LocalDateTime.now());

        // 如果接入问题管理，也上报到问题管理系统，不再进行明细数据解析和通知发送
        if (inspectTask.getProjectCode() != null && !inspectTask.getProjectCode().isEmpty()) {
            DbtrackingProject dbtrackingProject = dbtrackingProjectMapper.selectByProjectCode(inspectTask.getProjectCode());
            if (dbtrackingProject.getDtlsColumnDefinition() != null && !dbtrackingProject.getDtlsColumnDefinition().isEmpty()) {
                Pair<Boolean, String> loadResult = dbtrackingService.loadDtlData(inspectTask.getProjectCode(), "parse-data", "update", "full", newReq.getErrorList(), dbtrackingProject);
                if (!loadResult.getLeft()) {
                    String msg = String.format("该巡检已接入问题管理系统，走上报问题管理系统的逻辑。数据上报时出错，报错信息：%s", loadResult.getRight());
                    tankStatusService.updateStatus("inspect", 0, 2, req.getCode(), msg);
                    throw new RuntimeException(msg);
                } else {
                    tankStatusService.updateStatus("inspect", 0, 1, req.getCode(), "");
                    log.warn("该巡检已接入问题管理系统，走上报问题管理系统的逻辑。巡检数据已成功上报到问题管理系统。巡检大盘不再进行明细数据解析，仅可查询执行日志，请在问题管理系统查询数据明细和进行跟进！ {}", req);
                    return String.format("该巡检已接入问题管理系统，走上报问题管理系统的逻辑。巡检数据已成功上报到问题管理系统。巡检大盘不再进行明细数据解析，仅可查询执行日志，请在问题管理系统查询数据明细和进行跟进！ %s", req);
                }
            }
        }

        String newReqMsg = extractInspectDtls(newReq);
        String ignoreReqMsg = extractInspectDtls(ignoreReq);
        String retMsg = String.format("Extract inspect data: %s\nExtract ignore data: %s", newReqMsg, ignoreReqMsg);

        if (newReq.getError() > 0 && newReq.getInform()) {
            String inspectUrl = String.format("%s%s", LeoUtils.getStringProperty("mountain-v2-api.inspect_detail_url", ""), req.getCode());
            double errorRate = newReq.getError() * 100.0 / newReq.getTotal();
            String title = "巡检通知";
            String content = String.format("%s\n" +
                            "错误/总数: %d/%d\n" +
                            "问题率: %.2f\n" +
                            "白名单已忽略: %d\n" +
                            "详情地址: %s\n" +
                            "负责人: %s",
                    newReq.getName(), newReq.getError(), newReq.getTotal(), errorRate, ignoreReq.getError(), inspectUrl,
                    newReq.getAtMans().isEmpty() ? "无" : String.join(",", newReq.getAtMans()));
            informService.sendMsgToKnockRobots(title, content, Collections.singletonList("dbaGroupRobot"), newReq.getAtMans());
        }
        tankStatusService.updateStatus("inspect", 0, 1, req.getCode(), "");
        return retMsg;
    }

    private void procErrorListByWhiteList(InspectResultAddReq originReq, InspectResultAddReq newReq, InspectResultAddReq ignoreReq) {
        // 过滤加白的巡检
        List<InspectWhiteList> inspectWhiteLists = inspectWhiteListMapper.selectByInspectCodeAndBeginTimeAndEndTime(originReq.getCode());
        HashMap<String, Integer> whiteMap = new HashMap<>(16);
        for (InspectWhiteList inspectWhiteList : inspectWhiteLists) {
            whiteMap.put(inspectWhiteList.getWhiteList(), 1);
        }

        List<JsonNode> originErrorList = originReq.getErrorList();
        List<JsonNode> newErrorList = new ArrayList<>(10);
        List<JsonNode> ignoreErrorList = new ArrayList<>(10);
        String uniqueIdColumn = originReq.getUniqueIdColumn();
        /*
         error_list eg:
         "error_list": [
         {
            "shard_name": "st5_gin_detail_",
            "hotMetric": "CPUUseRate",
            "hotInstance": "cdb-7o7q29gi",
            "owner": "xxx,xxx,xxx,xxx",
            "business": "商品",
            "service_name": "gin-detail",
            "uniqueIdColumnString": "xxx",
            "uniqueIdColumnArray": ["xxx", "yyy"]
         }, {......}, {......}]
         */
        for (JsonNode error : originErrorList) {
            JsonNodeType errorUKColNodeType = error.get(uniqueIdColumn).getNodeType();
            if (errorUKColNodeType == JsonNodeType.STRING) {
                if (!whiteMap.containsKey(error.get(uniqueIdColumn).asText())) {
                    newErrorList.add(error);
                } else {
                    ignoreErrorList.add(error);
                }
            } else if (errorUKColNodeType == JsonNodeType.ARRAY) {
                boolean isWhite = false;
                for (JsonNode oneErrorUKEleJsonNode : error.get(uniqueIdColumn)) {
                    if (oneErrorUKEleJsonNode.getNodeType() == JsonNodeType.STRING) {
                        if (whiteMap.containsKey(oneErrorUKEleJsonNode.asText())) {
                            isWhite = true;
                        }
                    }
                }
                if (!isWhite) {
                    newErrorList.add(error);
                } else {
                    ignoreErrorList.add(error);
                }
            } else {
                newErrorList.add(error);
            }
        }
        newReq.setErrorList(newErrorList);
        newReq.setError(newErrorList.size());
        ignoreReq.setErrorList(ignoreErrorList);
        ignoreReq.setError(ignoreErrorList.size());
    }

    private String extractInspectDtls(InspectResultAddReq req) {
        try {
            String inspectCode = req.getCode(), inspectName = req.getName();
            List<JsonNode> errorList = req.getErrorList();
            if (errorList == null || errorList.isEmpty()) {
                log.error("extractInspectDtls encounter error, no error_list defined, inspectCode {}, inspectName {}", inspectCode, inspectName);
                return "error_list has no data, won't extract! Not affect original data(JSON) inserting";
            }

            if (req.getUniqueIdColumn() == null || req.getUniqueIdColumn().isEmpty()) {
                log.error("extractInspectDtls encounter error, no unique_id_column defined, inspectCode {}, inspectName {}", inspectCode, inspectName);
                return "No unique_id_column defined, won't extract inspect details! Not affect original data(JSON) inserting";
            }

            List<InspectDataDetail> inspectDataDetailList = new ArrayList<>(10);
            for (JsonNode errorJN : req.getErrorList()) {
                String otherData = StringUtil.jsonSerialize(errorJN);
                JsonNode uniqueIdJN = errorJN.get(req.getUniqueIdColumn());
                if (uniqueIdJN == null) {
                    log.error("extractInspectDtls encounter error, no unique_id_column defined, inspectCode {}, inspectName {}, unique_id_column {}", inspectCode, inspectName, req.getUniqueIdColumn());
                    return String.format("No unique_id_column defined (%s) won't extract inspect details! Not affect original data(JSON) inserting", req.getUniqueIdColumn());
                }
                List<String> uniqueIdList = new ArrayList<>(10);
                if (uniqueIdJN.getNodeType().equals(JsonNodeType.STRING)) {
                    uniqueIdList.add(uniqueIdJN.asText());
                } else if (uniqueIdJN.getNodeType().equals(JsonNodeType.ARRAY)) {
                    for (JsonNode oneUniqueIdJN : uniqueIdJN) {
                        uniqueIdList.add(oneUniqueIdJN.asText());
                    }
                }
                for (String uniqueId : uniqueIdList) {
                    String uniqueName = "", business = "";
                    switch (req.getDbType()) {
                        case InspectConstant.InspectDbTypeMySQL:
                            Instance instance = instanceMapper.selectOneByInstanceId(uniqueId);
                            uniqueName = instance.getInstanceName();
                            business = instance.getBusinessName();
                            break;
                        case InspectConstant.InspectDbTypeBatCluster:
                            BatCluster batCluster = batClusterMapper.selectOneByClusterName(uniqueId);
                            uniqueName = batCluster.getClusterName();
                            business = batCluster.getBusiness();
                            break;
                        case InspectConstant.InspectDbTypeBatHost:
                            BatHost batHost = batHostMapper.selectOneByHostNameOrIp(uniqueId);
                            uniqueName = batHost.getHostname();
                            batCluster = batClusterMapper.selectOneByClusterName(batHost.getClusterName());
                            business = batCluster.getBusiness();
                            break;
                        case InspectConstant.InspectDbTypePRS:
                            // fallthrough ↓
                        case InspectConstant.InspectDbTypeCRS:
                            PrsInstance prsInstance;
                            String[] uniqueIdSplits = uniqueId.split(":");
                            if (uniqueIdSplits.length == 2) {
                                prsInstance = prsInstanceMapper.selectOneByIpPort(uniqueIdSplits[0], Integer.parseInt(uniqueIdSplits[1]));
                            } else {
                                prsInstance = prsInstanceMapper.selectOneByPrsId(uniqueId);
                            }
                            uniqueName = prsInstance.getInstanceName();
                            business = prsInstance.getBusiness();
                        case InspectConstant.InspectDbTypeCodisCluster:
                            CodisCluster codisCluster = codisClusterMapper.selectOneByClusterName(uniqueId);
                            uniqueName = codisCluster.getClusterName();
                            business = codisCluster.getBusiness();
                        case InspectConstant.InspectDbTypeCodisServer:
                            // fallthrough ↓
                        case InspectConstant.InspectDbTypeCodisProxy:
                            CodisServer codisServer = codisServerMapper.selectOneByHostname(uniqueId);
                            uniqueName = codisServer.getHostname();
                            business = codisServer.getBusiness();
                        default:
                            log.warn("extractInspectDtls encounter error, unsupported dbType {}, inspectCode {}, inspectName {}", req.getDbType(), inspectCode, inspectName);
                    }
                    inspectDataDetailList.add(new InspectDataDetail(null,
                            req.getStartTime().toLocalDate(),
                            req.getCode(), req.getName(), business, uniqueId, uniqueName, req.getIsHealthy(), otherData, null, null));
                }
            }
            int rows = inspectDataDetailMapper.replaceIntoMany(inspectDataDetailList);
            return String.format("Successfully extracting and inserting inspect details, %s rows", rows);
        } catch (Exception e) {
            log.error("extractInspectDtls error, inspectReq {}", req, e);
            return String.format("extractInspectDtls error, inspectCode %s, inspectName %s, exception %s", req.getCode(), req.getName(), e);
        }
    }
}
