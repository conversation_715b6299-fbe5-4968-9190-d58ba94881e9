package com.pinduoduo.mountain.api.controller.monitor;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pinduoduo.arch.egret.contract.service.EgretMetricDataService;
import com.pinduoduo.arch.egret.contract.service.EgretMetricMetaService;
import com.pinduoduo.mountain.api.controller.monitor.common.MonitorDataService;
import com.pinduoduo.mountain.api.controller.monitor.service.MonitorLongTxDataService;
import com.pinduoduo.mountain.api.controller.monitor.service.MonitorMetaDataService;
import com.pinduoduo.mountain.api.controller.monitor.service.MonitorParam;
import com.pinduoduo.mountain.api.controller.monitor.view.ClusterView;
import com.pinduoduo.mountain.api.controller.monitor.view.LogicDatabaseView;
import com.pinduoduo.mountain.common.constant.Time;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.thirdparty.xcloud.XcloudProxyService;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Cluster;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.LogicDatabase;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.monitor.LongTx;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.monitor.MonitorDBInstance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.ClusterMapper;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.InstanceMapper;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.LogicDatabaseMapper;
import com.pinduoduo.mountain.service.metadata.dto.LogicDatabaseNewSearchInfoDTO;
import com.pinduoduo.mountain.service.metadata.impl.LogicDatabaseService;
import com.pinduoduo.mountain.service.user.impl.UserService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v2/monitor")
public class MonitorController {
    @Autowired
    private MonitorDataService monitorDataService;
    @Autowired
    private MonitorMetaDataService monitorMetaDataService;
    @Autowired
    private UserService userService;
    @Autowired
    private MonitorLongTxDataService monitorLongTxDataService;
    @Autowired
    private LogicDatabaseService logicDatabaseService;
    @Resource
    private EgretMetricDataService egretMetricDataService;
    @Resource
    private ClusterMapper clusterMapper;
    @Resource
    private LogicDatabaseMapper logicDatabaseMapper;
    @Resource
    private EgretMetricMetaService egretMetricMetaService;
    @Resource
    private InstanceMapper instanceMapper;
    @Autowired
    private XcloudProxyService xcloudProxyService;


    @GetMapping("/metadata/logicdatabases")
    public GenericResponse queryLogicDatabases(@RequestParam("searchcdb") String key,
                                               @RequestParam("user") String user,
                                               @RequestParam("env") String env) {
        try {
            String biz = monitorMetaDataService.getUserBiz(user);
            List<LogicDatabaseNewSearchInfoDTO> result = logicDatabaseService.search(user, env, key, 100);
            if (CollectionUtils.isNotEmpty(result)) {
                List<LogicDatabaseView> views = result.stream().map(e -> {
                    try {
                        return new LogicDatabaseView(e);
                    } catch (InvocationTargetException | IllegalAccessException ex) {
                        throw new RuntimeException(ex);
                    }
                }).collect(Collectors.toList());
                return new GenericResponse(true, views);
            }
            return new GenericResponse(true, null);
        } catch (Exception e) {
            log.error("queryLogicDatabases error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/metadata/logicclusters")
    public GenericResponse queryLogicClusters(@RequestParam("searchcdb") String key,
                                              @RequestParam("user") String user,
                                              @RequestParam("env") String env) {
        try {
            String biz = monitorMetaDataService.getUserBiz(user);
            List<LogicDatabase> databases = monitorMetaDataService.searchLogicClusters(key, biz, user, env);
            if (CollectionUtils.isNotEmpty(databases)) {
                List<LogicDatabaseView> views = databases.stream().map(e -> {
                    try {
                        return new LogicDatabaseView(e);
                    } catch (InvocationTargetException | IllegalAccessException ex) {
                        throw new RuntimeException(ex);
                    }
                }).collect(Collectors.toList());
                return new GenericResponse(true, views);
            }
            return new GenericResponse(true, null);
        } catch (Exception e) {
            log.error("queryLogicClusters error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/metadata/clusters")
    public GenericResponse queryClusters(@RequestParam("searchcdb") String key,
                                         @RequestParam("user") String user,
                                         @RequestParam("env") String env) {
        try {
            String biz = monitorMetaDataService.getUserBiz(user);
            List<Cluster> clusters = monitorMetaDataService.searchClusters(key, biz, user, env);
            if (CollectionUtils.isNotEmpty(clusters)) {
                List<ClusterView> views = clusters.stream().map(e -> {
                    try {
                        return new ClusterView(e);
                    } catch (InvocationTargetException | IllegalAccessException ex) {
                        throw new RuntimeException(ex);
                    }
                }).collect(Collectors.toList());
                return new GenericResponse(true, views);
            }
            return new GenericResponse(true, null);
        } catch (Exception e) {
            log.error("queryClusters error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/metadata/instances")
    public GenericResponse queryInstances(@RequestParam(value = "searchcdb", required = false) String key,
                                          @RequestParam(value = "logicDbId", required = false) String logicDbIdStr,
                                          @RequestParam(value = "clusterId", required = false) String clusterIdStr,
                                          @RequestParam("user") String user,
                                          @RequestParam("env") String env) {
        try {
            String biz = monitorMetaDataService.getUserBiz(user);
            Long logicDbId = null;
            if (StringUtils.isNotEmpty(logicDbIdStr)) {
                logicDbId = Long.valueOf(logicDbIdStr);
            }
            Long clusterId = null;
            if (StringUtils.isNotEmpty(clusterIdStr)) {
                clusterId = Long.valueOf(clusterIdStr);
            }
            if (StringUtils.isNotEmpty(key)) {
                if (StringUtils.length(key) < 3) {
                    String msg = "搜索字符串过短 至少3个字符";
                    return new GenericResponse(false, msg);
                }
                List<Instance> data = monitorMetaDataService.getInstances(key, biz, user, env);
                return new GenericResponse(true, convert(data));

            } else if (logicDbId != null) {
                LogicDatabase database = logicDatabaseMapper.selectOneByLogicDbId(logicDbId);
                List<Instance> instances = monitorMetaDataService.getInstances(database);
                return new GenericResponse(true, convert(instances));

            } else if (clusterId != null) {
                Cluster cluster = clusterMapper.selectOneByClusterId(clusterId);
                List<Instance> instances = monitorMetaDataService.getInstances(cluster);
                return new GenericResponse(true, convert(instances));
            } else {
                return new GenericResponse(false, "实例名、逻辑库ID、集群ID至少要设置一个");
            }
        } catch (Exception e) {
            log.error("queryInstances error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    private List<MonitorDBInstance> convert(List<Instance> data) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.stream().map(v -> {
            MonitorDBInstance instance = new MonitorDBInstance();
            instance.setInstanceId(v.getInstanceId());
            String label = String.format("%s/%s-%s(%s %s核,%sG内存,%sG磁盘", v.getInstanceName(), v.getIp(), v.getPort(), v.getZone(), v.getInstanceCpu(), v.getInstanceMemory(), v.getInstanceVolume());
            if (StringUtils.equals(v.getDeviceClass(), "Y0-SH02")) {
                label = label + " 40T机型";
            }
            if (StringUtils.equals(v.getDeviceClass(), "Y0-TS85-00")) {
                label = label + " 7T机型";
            }
            if (StringUtils.equals(v.getDeviceClass(), "Y0-SH12")) {
                label = label + " 46T机型";
            }
            if (StringUtils.equals(v.getDeviceClass(), "Y0-SW2500-25G")) {
                label = label + " 14T机型";
            }
            label = label + ")";
            instance.setLabel(label);
            instance.setPhyId(v.getMachineHostname());
            instance.setRole(v.getRole());
            instance.setValue(v.getInstanceId());
            instance.setFeDesc(label);
            return instance;
        }).collect(Collectors.toList());
    }


    @PostMapping("/egret/queryDataPoints")
    @ApiOperation("查询普通指标监控数据")
    public BaseResponse<List<BasicMetricData>> queryDataPoints(@RequestBody MonitorParam param) {
        try {
            String ns = "ROC.CDB";
            String metric = param.getMetric();
            List<String> dbIds = param.getCdbid().stream().map(v -> v.get("value")).collect(Collectors.toList());
            Long start = param.parseStartTimeTSOfMs();
            Long end = param.parseEndTimeTSOfMs();
            List<String> levels = new ArrayList<>(2);
            levels.add("ROC");
            levels.add("CDB");

            // 由于自建实例和云实例的查询逻辑不同，按照自建实例、云实例分成两组，每组批量查询，提升速度
            // 自建实例：htj 的数据在 egret 的 htj；物理机监控通过 egret 查询
            // 云实例：htj 的数据在 egret 的 prod；物理机监控只能通过 xcloud-proxy 查询
            List<Instance> instances = instanceMapper.selectByInstanceIdList(dbIds);
            List<ArrayList<Instance>> instanceGroups = new ArrayList<ArrayList<Instance>>(2) {{
                ArrayList<Instance> pdbInstanceList = new ArrayList<>();
                ArrayList<Instance> cloudInstanceList = new ArrayList<>();
                for (Instance instance : instances) {
                    if (instance.getType().equals("pdb")) {
                        pdbInstanceList.add(instance);
                    } else {
                        cloudInstanceList.add(instance);
                    }
                }
                if (!pdbInstanceList.isEmpty()) {
                    add(pdbInstanceList);
                }
                if (!cloudInstanceList.isEmpty()) {
                    add(cloudInstanceList);
                }
            }};

            List<BasicMetricData> resData = new LinkedList<>();
            for (ArrayList<Instance> instanceList : instanceGroups) {
                // rt 直接从 mysql 中查询
                if (StringUtils.equalsIgnoreCase(param.getMetric(), "rt")) {
                    for (Instance instance : instanceList) {
                        String dbId = instance.getInstanceId();
                        // now
                        MetricData data = monitorDataService.getRT(dbId, param.getStart(), param.getEnd());
                        appendData(instanceList, data, resData, param.getStart());
                        // dod
                        if (CollectionUtils.isNotEmpty(param.getAddtype()) && CollectionUtils.containsAny(param.getAddtype(), "同比")) {
                            start = param.parseStartTimeTSOfMs() - TimeUnit.DAYS.toMillis(1);
                            end = param.parseEndTimeTSOfMs() - TimeUnit.DAYS.toMillis(1);
                            MetricData dodData = monitorDataService.getRT(dbId, LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).format(Time.FORMATTER),
                                    LocalDateTime.ofInstant(Instant.ofEpochMilli(end), ZoneId.systemDefault()).format(Time.FORMATTER));
                            appendData(instanceList, dodData, resData, LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).format(Time.FORMATTER));
                        }
                        // wow
                        if (CollectionUtils.isNotEmpty(param.getAddtype()) && CollectionUtils.containsAny(param.getAddtype(), "环比")) {
                            start = param.parseStartTimeTSOfMs() - TimeUnit.DAYS.toMillis(7);
                            end = param.parseEndTimeTSOfMs() - TimeUnit.DAYS.toMillis(7);
                            MetricData wowData = monitorDataService.getRT(dbId, LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).format(Time.FORMATTER),
                                    LocalDateTime.ofInstant(Instant.ofEpochMilli(end), ZoneId.systemDefault()).format(Time.FORMATTER));
                            appendData(instanceList, wowData, resData, LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).format(Time.FORMATTER));
                        }
                    }
                    continue;
                }

                Instance instance = instanceList.get(0);
                String env = instance.getEnv();
                String type = instance.getType();

                // 1、tags 组装
                List<Triple<String, String, List<String>>> tags = new LinkedList<>();
                // 云实例
                List<String> instanceIdsOfMonitor;
                if (!type.equals("pdb")) {
                    List<String> instanceTypes = new ArrayList<>();
                    List<String> instanceIds = new ArrayList<>();
                    for (Instance inst : instanceList) {
                        String insId = inst.getInstanceId();
                        if (insId.endsWith("-second-backup")) {
                            instanceTypes.add("slave1");
                            instanceIds.add(insId.replace("-second-backup", ""));
                        } else if (insId.endsWith("-backup")) {
                            instanceTypes.add("slave");
                            instanceIds.add(insId.replace("-backup", ""));
                        } else if (insId.startsWith("cdbro") || insId.startsWith("ddcro")) {
                            instanceTypes.add("ro");
                            instanceIds.add(insId);
                        } else {
                            // 主实例四个特殊指标、备库实例两种场景，需要指定InstanceType为备库角色，第一备库、第二备库InstanceType又有所区别
                            if (metric.contains("master_slave_sync_distance") || metric.contains("seconds_behind_master")
                                    || metric.contains("slave_io_running") || metric.contains("slave_sql_running")) {
                                instanceTypes.add("slave");
                            } else {
                                instanceTypes.add("master");
                            }
                            instanceIds.add(insId);
                        }
                    }
                    instanceIdsOfMonitor = instanceIds;
                    tags.add(new ImmutableTriple<>("instanceType", "in", instanceTypes));
                    tags.add(new ImmutableTriple<>("aggregatorType", "=", Collections.singletonList("max")));
                } else {
                    // 自建实例
                    metric = monitorMetaDataService.getMetric(ns, metric);
                    instanceIdsOfMonitor = instanceList.stream().map(Instance::getInstanceId).collect(Collectors.toList());
                }
                tags.add(new ImmutableTriple<>("instanceId", "in", instanceIdsOfMonitor));

                // 2、fieldKeys 组装
                List<String> fieldKeys;
                if (!type.equals("pdb")) {
                    fieldKeys = Collections.singletonList(metric);
                } else {
                    fieldKeys = monitorMetaDataService.getMetricField(ns, param.getMetric());
                }
                // 3、groupBy 组装
                List<String> groupBy = new ArrayList<String>();
                groupBy.add("instanceId");
                if (!type.equals("pdb")) {
                    // 云实例还需要按照 instanceType 分组，云实例的 backup 和 second-backup 需要依靠此来分辨
                    groupBy.add("instanceType");
                }

                // now
                MetricData data = monitorDataService.getDataPoints(ns, metric, tags, levels, fieldKeys, groupBy, null, start, end, env, type);
                appendData(instanceList, data, resData, param.getStart());
                // dod
                if (CollectionUtils.isNotEmpty(param.getAddtype()) && CollectionUtils.containsAny(param.getAddtype(), "同比")) {
                    start = param.parseStartTimeTSOfMs() - TimeUnit.DAYS.toMillis(1);
                    end = param.parseEndTimeTSOfMs() - TimeUnit.DAYS.toMillis(1);
                    MetricData dodData = monitorDataService.getDataPoints(ns, metric, tags, levels, fieldKeys, groupBy, null, start, end, env, type);
                    appendData(instanceList, dodData, resData, LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).format(Time.FORMATTER));
                }
                // wow
                if (CollectionUtils.isNotEmpty(param.getAddtype()) && CollectionUtils.containsAny(param.getAddtype(), "环比")) {
                    start = param.parseStartTimeTSOfMs() - TimeUnit.DAYS.toMillis(7);
                    end = param.parseEndTimeTSOfMs() - TimeUnit.DAYS.toMillis(7);
                    MetricData wowData = monitorDataService.getDataPoints(ns, metric, tags, levels, fieldKeys, groupBy, null, start, end, env, type);
                    appendData(instanceList, wowData, resData, LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).format(Time.FORMATTER));
                }
            }
            return BaseResponse.success(resData);
        } catch (Exception e) {
            log.error("queryDataPoints error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/egret/queryDataPointsOfMachine")
    @ApiOperation("查询物理机指标监控数据")
    public BaseResponse<Map<String, List<BasicMetricData>>> queryDataPointsOfMachine(@RequestBody MonitorParam param) {
        try {
            if (param.getCdbid().size() != 1) {
                return BaseResponse.fail("物理机监控查询时请只选择一个实例");
            }

            Map<String, List<BasicMetricData>> resData = new HashMap<>();
            Instance instance = instanceMapper.selectOneByInstanceId(param.getCdbid().get(0).get("value"));
            if (instance.getType().equals("pdb")) {
                String hostName = instance.getMachineHostname();
                if (StringUtils.isEmpty(hostName)) {
                    return BaseResponse.fail(instance.getInstanceId() + " 没有找到对应 machine_hostname");
                }
                // 1、tags 组装
                List<Triple<String, String, List<String>>> tags = new LinkedList<>();
                tags.add(new ImmutableTriple<>("MONITOR.hostName", "in", Collections.singletonList(hostName)));

                // 2、groupBy 组装
                List<String> groupBy = new ArrayList<>();
                groupBy.add("MONITOR.endpoint");

                List<Triple<String, String, String>> metricTripleList = new ArrayList<>();
                metricTripleList.add(new ImmutableTriple<>("diskIoRatioPerSec", "disk", "io_util"));
                metricTripleList.add(new ImmutableTriple<>("diskIoWaitTime", "disk", "io_await"));
                metricTripleList.add(new ImmutableTriple<>("diskRead", "disk", "io_read_requests_delta"));
                metricTripleList.add(new ImmutableTriple<>("diskWrite", "disk", "io_write_requests_delta"));
                metricTripleList.add(new ImmutableTriple<>("memTotal", "mem", "memtotal"));
                metricTripleList.add(new ImmutableTriple<>("memUsed", "mem", "memused"));
                metricTripleList.add(new ImmutableTriple<>("netPackageIn", "net", "if_in_packets_delta"));
                metricTripleList.add(new ImmutableTriple<>("netPackageOut", "net", "if_out_packets_delta"));
                metricTripleList.add(new ImmutableTriple<>("netFlowIn", "net", "if_in_bytes_delta"));
                metricTripleList.add(new ImmutableTriple<>("netFlowOut", "net", "if_out_bytes_delta"));
                metricTripleList.add(new ImmutableTriple<>("netConn", "TcpExt", "MaxConn_delta"));
                metricTripleList.add(new ImmutableTriple<>("cpuLoad", "cpu", "busy"));

                for (Triple<String, String, String> triple : metricTripleList) {
                    String label = triple.getLeft();
                    String metricName = triple.getMiddle();
                    List<String> fieldKeys = Collections.singletonList(triple.getRight());
                    MetricData metricData = monitorDataService.getDataPoints("MACHINE.HOST", metricName, tags, Arrays.asList("MACHINE", "HOST"),
                            fieldKeys, groupBy, null, param.parseStartTimeTSOfMs(), param.parseEndTimeTSOfMs(), instance.getEnv(), instance.getType());
                    appendMachineData(instance, metricData, resData, param.getStart(), label);
                }
            } else if (instance.getType().equals("cdb")) {
                JSONObject params = new JSONObject();
                params.put("InstanceId", instance.getRole().equals("master") ? instance.getInstanceId() : instance.getInstanceMasterId());
                params.put("Method", "POST");
                params.put("Action", "DescribeDeviceMonitorInfo");
                params.put("Region", "ap-shanghai");
                params.put("Version", "2017-03-20");
                // 响应示例：
                // {
                //    "Response": {
                //        "Cpu": {
                //            "Load": [],
                //            "Rate": [
                //                {
                //                    "CpuCore": 0,
                //                    "Rate": []
                //                }
                //            ]
                //        },
                //        "Disk": {
                //            "CapacityRatio": [
                //                9828319232,
                //                15356598272
                //            ],
                //            "IoRatioPerSec": [],
                //            "IoWaitTime": [],
                //            "Read": [],
                //            "Write": []
                //        },
                //        "Mem": {
                //            "Total": [],
                //            "Used": []
                //        },
                //        "Net": {
                //            "Conn": [],
                //            "FlowIn": [],
                //            "FlowOut": [],
                //            "PackageIn": [],
                //            "PackageOut": []
                //        },
                //        "RequestId": "05665d9e-41a4-4782-be4c-2009c56fcd8f"
                //    }
                // }
                JSONObject response = xcloudProxyService.withParams("QCloud", "V3", "cdb", "").request(params);
                if (response != null && response.getJSONObject("Response") != null) {
                    JSONObject responseIn = response.getJSONObject("Response");

                    List<Triple<String, String, String>> metricTripleList = new ArrayList<>();
                    metricTripleList.add(new ImmutableTriple<>("diskIoRatioPerSec", "Disk", "IoRatioPerSec"));
                    metricTripleList.add(new ImmutableTriple<>("diskIoWaitTime", "Disk", "IoWaitTime"));
                    metricTripleList.add(new ImmutableTriple<>("diskRead", "Disk", "Read"));
                    metricTripleList.add(new ImmutableTriple<>("diskWrite", "Disk", "Write"));
                    metricTripleList.add(new ImmutableTriple<>("memTotal", "Mem", "Total"));
                    metricTripleList.add(new ImmutableTriple<>("memUsed", "Mem", "Used"));
                    metricTripleList.add(new ImmutableTriple<>("netPackageIn", "Net", "PackageIn"));
                    metricTripleList.add(new ImmutableTriple<>("netPackageOut", "Net", "PackageOut"));
                    metricTripleList.add(new ImmutableTriple<>("netFlowIn", "Net", "FlowIn"));
                    metricTripleList.add(new ImmutableTriple<>("netFlowOut", "Net", "FlowOut"));
                    metricTripleList.add(new ImmutableTriple<>("netConn", "Net", "Conn"));
                    metricTripleList.add(new ImmutableTriple<>("cpuLoad", "Cpu", "Load"));
                    metricTripleList.add(new ImmutableTriple<>("cpuRate", "Cpu", "Rate"));

                    for (Triple<String, String, String> triple : metricTripleList) {
                        if (triple.getLeft().equals("cpuRate")) {
                            List<BasicMetricData> datas = new ArrayList<>();
                            for (int i = 0; i < responseIn.getJSONObject("Cpu").getJSONArray("Rate").size(); i++) {
                                String cpuCore = responseIn.getJSONObject("Cpu").getJSONArray("Rate").getJSONObject(i).getString("CpuCore");
                                JSONArray rate = responseIn.getJSONObject("Cpu").getJSONArray("Rate").getJSONObject(i).getJSONArray("Rate");

                                List<TsDataPoint> tsDataPoints = new ArrayList<>();
                                LocalDateTime now = LocalDateTime.now();
                                // 今天的零点零分零秒开始，每次增加300s
                                LocalDateTime time = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0, 0);
                                for (int j = 0; j < rate.size(); j++) {
                                    TsDataPoint tsDataPoint = new TsDataPoint(rate.getDouble(j) > 0 ? rate.getDouble(j) : null, time.format(Time.FORMATTER));
                                    tsDataPoints.add(tsDataPoint);
                                    time = time.plusSeconds(300);
                                }

                                String label = String.format("%s/%s-%d (%s %d核%dG内存%dG磁盘)/%s-cpuCore%s",
                                        instance.getInstanceName(), instance.getIp(), instance.getPort(),
                                        instance.getZone(), instance.getInstanceCpu(), instance.getInstanceMemory() / 1000, instance.getInstanceVolume(), instance.getMachineHostname(), cpuCore);
                                if (tsDataPoints.isEmpty()) {
                                    String feDesc = "数据丢失: " + label;
                                    datas.add(new BasicMetricData(false, "没有查询到数据", feDesc, null));
                                } else {
                                    String feDesc = param.getStart().substring(0, 10) + "-" + label;
                                    datas.add(new BasicMetricData(true, "查询成功", feDesc, tsDataPoints));
                                }
                            }
                            resData.put("cpuRate", datas);
                        } else {
                            JSONArray metricDataArray = responseIn.getJSONObject(triple.getMiddle()).getJSONArray(triple.getRight());

                            MetricData metricData = new MetricData();
                            List<TsDataPoint> tsDataPoints = new ArrayList<>();
                            LocalDateTime now = LocalDateTime.now();
                            // 今天的零点零分零秒开始，每次增加300s
                            LocalDateTime time = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0, 0);
                            for (int i = 0; i < metricDataArray.size(); i++) {
                                TsDataPoint tsDataPoint = new TsDataPoint(metricDataArray.getDouble(i) > 0 ? metricDataArray.getDouble(i) : null, time.format(Time.FORMATTER));
                                tsDataPoints.add(tsDataPoint);
                                time = time.plusSeconds(300);
                            }
                            metricData.add(instance.getInstanceId(), tsDataPoints);

                            appendMachineData(instance, metricData, resData, param.getStart(), triple.getLeft());
                        }
                    }
                }
                return BaseResponse.success(resData);
            } else {
                return BaseResponse.fail("百度云暂不支持物理机监控查询");
            }
            return BaseResponse.success(resData);
        } catch (Exception e) {
            log.error("queryDataPointsOfMachine error", e);
            return BaseResponse.fail(e);
        }
    }

    private void appendData(List<Instance> instanceList, MetricData data, List<BasicMetricData> datas, String startTime) {
        for (Instance instance : instanceList) {
            String label = String.format("%s/%s-%d (%s %d核%dG内存%dG磁盘)/%s/%s",
                    instance.getInstanceName(), instance.getIp(), instance.getPort(),
                    instance.getZone(), instance.getInstanceCpu(), instance.getInstanceMemory() / 1000, instance.getInstanceVolume(), instance.getInstanceId(), instance.getMachineHostname());
            if (data == null || MapUtils.isEmpty(data.getData()) || CollectionUtils.isEmpty(data.getData().get(instance.getInstanceId()))) {
                String feDesc = "数据丢失:" + label;
                datas.add(new BasicMetricData(false, "没有查询到数据", feDesc, null));
            } else {
                String feDesc = startTime.substring(0, 10) + "-" + label;
                datas.add(new BasicMetricData(true, "查询成功", feDesc, data.getData().get(instance.getInstanceId())));
            }
        }
    }

    private void appendMachineData(Instance instance, MetricData data, Map<String, List<BasicMetricData>> dataMap, String startTime, String machineMetricLabel) {
        String label = String.format("%s/%s-%d (%s %d核%dG内存%dG磁盘)/%s/%s",
                instance.getInstanceName(), instance.getIp(), instance.getPort(),
                instance.getZone(), instance.getInstanceCpu(), instance.getInstanceMemory() / 1000, instance.getInstanceVolume(), instance.getInstanceId(), instance.getMachineHostname());
        List<BasicMetricData> datas = new ArrayList<>();
        if (data == null || MapUtils.isEmpty(data.getData()) || CollectionUtils.isEmpty(data.getData().values())) {
            String feDesc = "数据丢失: " + label;
            datas.add(new BasicMetricData(false, "没有查询到数据", feDesc, null));
        } else {
            String feDesc = startTime.substring(0, 10) + "-" + label;
            datas.add(new BasicMetricData(true, "查询成功", feDesc, data.getData().values().iterator().next()));
        }
        dataMap.put(machineMetricLabel, datas);
    }

    @GetMapping("/egret/metrics")
    public GenericResponse dumpMetrics(@RequestParam(value = "ns", defaultValue = "ROC.CDB") String ns) {
        try {
            return new GenericResponse(true, monitorMetaDataService.dumpMetrics(ns));
        } catch (Exception e) {
            log.error("dumpMetrics error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/egret/tags")
    public GenericResponse dumpTags(@RequestParam(value = "ns", defaultValue = "ROC.CDB") String ns,
                                    @RequestParam(value = "metric", defaultValue = "com_select") String metric) {
        try {
            return new GenericResponse(true, monitorMetaDataService.dumpTags(ns, metric));
        } catch (Exception e) {
            log.error("dumpTags error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/egret/tag/values")
    public GenericResponse dumpTagValues(@RequestParam(value = "ns", defaultValue = "ROC.CDB") String ns,
                                         @RequestParam(value = "metric", defaultValue = "com_select") String metric,
                                         @RequestParam(value = "tagKey", defaultValue = "instanceId") String tagKey) {
        try {
            return new GenericResponse(true, monitorMetaDataService.dumpTagValues(ns, metric, tagKey));
        } catch (Exception e) {
            log.error("dumpTagValues error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/egret/fields")
    public GenericResponse dumpFields(@RequestParam(value = "ns", defaultValue = "ROC.CDB") String ns,
                                      @RequestParam(value = "metric", defaultValue = "com_select") String metric) {
        try {
            return new GenericResponse(true, monitorMetaDataService.dumpFields(ns, metric));
        } catch (Exception e) {
            log.error("dumpFields error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/feynman/test")
    public GenericResponse test(@RequestParam(value = "ns", defaultValue = "ROC.CDB") String ns,
                                @RequestParam(value = "metric", defaultValue = "com_select") String metric,
                                @RequestParam(value = "tagKey", defaultValue = "instanceId") String tagKey,
                                @RequestParam(value = "tagValue") String tagValue,
                                @RequestParam(value = "fieldKey", defaultValue = "value") String fieldKey) {
        try {
            return new GenericResponse(true, monitorDataService.getData(ns, metric, tagKey, tagValue, fieldKey));
        } catch (Exception e) {
            log.error("test error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/rt")
    public GenericResponse queryRt(@RequestParam(value = "dbId") String dbId,
                                   @RequestParam(value = "start", required = false) String startStr,
                                   @RequestParam(value = "end", required = false) String endStr) throws Exception {
        try {
            MetricData data = monitorDataService.getRT(dbId, startStr, endStr);
            return new GenericResponse(true, data);
        } catch (Exception e) {
            log.error("queryRt error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/longtx/query")
    public GenericResponse queryLongTx(@RequestParam(value = "userId") String userId,
                                       @RequestParam(value = "instanceId") String instanceId,
                                       @RequestParam(value = "startTime") String startTimeStr,
                                       @RequestParam(value = "endTime") String endTimeStr,
                                       @RequestParam(value = "pageIndex") Long pageIndex,
                                       @RequestParam(value = "pageCnt") Long pageCnt) {
        try {
            Date startTime = DateUtils.parseDate(startTimeStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date endTime = DateUtils.parseDate(endTimeStr, "yyyy-MM-dd'T'HH:mm:ss");
            List<LongTx> txs = monitorLongTxDataService.get(instanceId, userId, pageIndex, pageCnt, startTime, endTime);
            return new GenericResponse(true, txs);
        } catch (Exception e) {
            log.error("queryLongTx error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/longtx/kill")
    public GenericResponse killLongTx(@RequestParam(value = "userId") String userId,
                                      @RequestParam(value = "instanceId") String instanceId,
                                      @RequestParam(value = "threadIds") String threadIds) {
        if (StringUtils.isEmpty(instanceId) || StringUtils.isEmpty(threadIds)) {
            return new GenericResponse(false, String.format("参数无效:%s,%s", instanceId, threadIds));
        }
        try {
            List<String> threadIdList = Arrays.asList(threadIds.split(","));
            Pair<Boolean, String> res = monitorLongTxDataService.killTx(instanceId, threadIdList);
            return new GenericResponse(res.getLeft(), res.getRight());
        } catch (Exception e) {
            log.error("killLongTx error", e);
            return new GenericResponse(false, e.getMessage());
        }
    }
}