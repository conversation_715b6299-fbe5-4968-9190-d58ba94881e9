package com.pinduoduo.mountain.api.controller.dashboard;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dashboard {
    private boolean success;
    private String msg;

    private String name;
    private String color;
    private String address;
    private String metricName;
    private Object value;

    public Dashboard(String name, String address, String color, String metricName) {
        this.name = name;
        this.address = address;
        this.color = color;
        this.metricName = metricName;
    }
}
