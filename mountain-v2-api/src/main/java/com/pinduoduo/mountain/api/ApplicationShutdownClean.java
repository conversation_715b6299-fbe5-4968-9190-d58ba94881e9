package com.pinduoduo.mountain.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pinduoduo.mountain.common.constant.KeyName;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.util.CollectionUtil;
import com.pinduoduo.mountain.common.util.HttpUtil;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.repository.etcd.EtcdKvClient;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.OrderSql;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.OrderSqlMapper;
import io.etcd.jetcd.KeyValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;

/**
 * 应用/程序退出时的清理工作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApplicationShutdownClean {

    private final EtcdKvClient etcdKvClient;
    private final OrderSqlMapper orderSqlMapper;

    public ApplicationShutdownClean(EtcdKvClient etcdKvClient, @Qualifier("orderSqlMapper") OrderSqlMapper orderSqlMapper) {
        this.etcdKvClient = etcdKvClient;
        this.orderSqlMapper = orderSqlMapper;
    }

    public void applicationShutdownClean() {
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            String currentIp = inetAddress.getHostAddress();
            log.info("begin application shutdown clean, current ip: {}", currentIp);

            // 从 etcd 中删除自己的注册
            etcdKvClient.delete(String.format(KeyName.POD_STATUS_FMT, currentIp));

            // 挑选一个 ip 调用他的接口，让他继承自己的任务
            List<KeyValue> keyValues = etcdKvClient.get(KeyName.KEY_POD_PREFIX + "/status", true);
            HashMap<String, String> peerIpStartTime = new HashMap<>(keyValues.size());
            for (KeyValue keyValue : keyValues) {
                // /mountain-v2/pod/status/127.0.0.1
                String key = keyValue.getKey().toString(StandardCharsets.UTF_8);
                String value = keyValue.getValue().toString(StandardCharsets.UTF_8);
                String[] keySplits = key.split("/");
                String keyIp = keySplits[keySplits.length - 1];
                if (!keyIp.equals(currentIp)) {
                    peerIpStartTime.put(keyIp, value);
                }
            }

            if (!peerIpStartTime.isEmpty()) {
                // 获取一个最近刚启动的 pod
                log.info("application shutdown clean() peer ip list: {}", peerIpStartTime);
                String latestPeerIp = CollectionUtil.getKeyOfMaxValue(peerIpStartTime);
                log.info("application shutdown clean() latest ip: {}", latestPeerIp);

                log.info("application shutdown clean() [transferDdlTask] begin");
                transferDdlTask(currentIp, latestPeerIp);
                log.info("application shutdown clean() [transferDdlTask] end");

                log.info("application shutdown clean() all end");
            } else {
                log.error("application shutdown clean() error, peer ip get empty");
            }
        } catch (Exception e) {
            log.error("application shutdown clean() error", e);
        }
    }

    public void transferDdlTask(String fromIp, String toIp) {
        List<OrderSql> orderSqlList = orderSqlMapper.selectByStatus(OrderSql.STATUS_EXECUTING);
        for (OrderSql orderSql : orderSqlList) {
            OrderSql.DdlConfig ddlConfig = StringUtil.jsonDeserialize(orderSql.getDdlConfig(), new TypeReference<OrderSql.DdlConfig>() {
            });
            if (!ddlConfig.getDispatchHost().equals(fromIp)) {
                log.info("application shutdown clean() transferDdlTask() skip workId: {}, dispatch host not curr ip, {} {}", orderSql.getWorkId(), ddlConfig.getDispatchHost(), fromIp);
                continue;
            }
            Long workId = orderSql.getWorkId();

            String url = String.format("http://%s:%d/api/mountainSelfInvocation/transferDdlTask?fromIp=%s&workId=%d", toIp, MountainApplication.SERVER_PORT, fromIp, workId);
            log.info("application shutdown clean() transferDdlTask() workId: {}, url: {}", workId, url);
            String response = HttpUtil.postByApacheHttp(url, null, null);
            log.info("application shutdown clean() transferDdlTask() workId: {}, response: {}", workId, response);

            BaseResponse<Boolean> booleanResponse = StringUtil.jsonDeserialize(response, new TypeReference<BaseResponse<Boolean>>() {
            });
            if (!booleanResponse.isSuccess()) {
                log.error("application shutdown clean() transferDdlTask() failed workId: {}, response: {}", workId, response);
            } else {
                log.info("application shutdown clean() transferDdlTask() success workId: {}", workId);
            }
        }
    }
}
