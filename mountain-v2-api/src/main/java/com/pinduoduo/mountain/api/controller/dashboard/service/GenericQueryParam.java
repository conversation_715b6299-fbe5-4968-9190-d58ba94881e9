package com.pinduoduo.mountain.api.controller.dashboard.service;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.client.utils.DateUtils;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GenericQueryParam {
    private String bizName;
    private String userId;

    private String serviceName;
    private List<String> metricNames;
    private String begin;
    private String end;


    public Long getBeginTime() {
        return DateUtils.parseDate(begin, new String[]{"yyyy-MM-dd HH:mm:ss"}).getTime();
    }
    public Long getEndTime() {
        return DateUtils.parseDate(end, new String[]{"yyyy-MM-dd HH:mm:ss"}).getTime();
    }
}
