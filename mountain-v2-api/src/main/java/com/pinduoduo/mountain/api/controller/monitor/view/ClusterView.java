package com.pinduoduo.mountain.api.controller.monitor.view;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.Cluster;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;

import java.lang.reflect.InvocationTargetException;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ClusterView extends Cluster {
    private String feDesc;

    public ClusterView(Cluster cluster) throws InvocationTargetException, IllegalAccessException {
        BeanUtils.copyProperties(this, cluster);
        this.feDesc = String.format("%s/%s/%s", this.getClusterName(), this.getType(), this.getBizGroupName());
    }

    public String getClusterIdStr() {
        return getClusterId() != null ? getClusterId().toString() : null;
    }
}

