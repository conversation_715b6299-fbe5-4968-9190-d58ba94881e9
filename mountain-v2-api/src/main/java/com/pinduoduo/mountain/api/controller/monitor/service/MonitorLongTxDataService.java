package com.pinduoduo.mountain.api.controller.monitor.service;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.monitor.LongTx;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;

public interface MonitorLongTxDataService {
    List<LongTx> get(String instanceId, String userId, Long pageIndex, Long pageCnt, Date startTime, Date endTime);
    Pair<Boolean, String> killTx(String instanceId, List<String>  threadIds);
}
