package com.pinduoduo.mountain.api.controller.order;

import com.fasterxml.jackson.databind.JsonNode;
import com.pinduoduo.mountain.common.dto.order.FormItem;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.service.order.OrderCommonService;
import com.pinduoduo.mountain.service.teamflow.order.entity.TFOrderBasic;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_ENV;
import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/** date: 2024-10-09 */
@Api("General Work Order Interface")
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v2/order/common")
public class OrderCommonController {

	@Resource
	private OrderCommonService orderCommonService;

	@ApiOperation("Get List of Optional MySQL Configuration Information")
	@GetMapping("/mysqlConfig")
	public BaseResponse<List<FormItem>> getMysqlConfigList(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,
			@RequestHeader(HEADER_ENV) String env) {
		try {
			return BaseResponse.success(orderCommonService.getMysqlConfigList(username));
		} catch (Exception e) {
			log.error("Failed to obtain the list of optional MySQL configuration information", e);
			return BaseResponse
					.fail("Failed to get the list of optional mysql configuration information: + " + e.getMessage());
		}
	}

	@ApiOperation("Get the list of optional keychain client language information")
	@GetMapping("/keychainClientConfig")
	public BaseResponse<JsonNode> getKeychainClientConfigList() {
		try {
			return BaseResponse.success(orderCommonService.getKeychainClientConfigList());
		} catch (Exception e) {
			log.error("Failed to get the list of optional keychain client language information", e);
			return BaseResponse.fail(
					"Failed to get the list of optional keychain client language information: + " + e.getMessage());
		}
	}

	@ApiOperation("Get the list of available zones in the cloud environment")
	@GetMapping("/zone")
	public BaseResponse<Map<String, Object>> getZoneList(@ApiParam("Cloud environments") @RequestParam String setEnv,
			@ApiParam("Type") @RequestParam(defaultValue = "") String type) {
		try {
			Pair<List<FormItem>, String> result = orderCommonService.getZoneList(setEnv, type);
			return BaseResponse.success(new HashMap<String, Object>() {
				{
					put("zone_list", result.getLeft());
					put("maintain_window", result.getRight());
				}
			});
		} catch (Exception e) {
			log.error("Failed to obtain the list of available zones in the cloud environment", e);
			return BaseResponse
					.fail("Failed to obtain the list of available zones in the cloud environment: + " + e.getMessage());
		}
	}

	@ApiOperation("Get Secondary Backup Machine Optional Configurations")
	@GetMapping("doubleBackupAfterConfig")
	public BaseResponse<List<FormItem>> getDoubleBackupAfterConfigList(
			@RequestParam("phyClusterIdStr") String phyClusterIdStr) {
		try {
			List<FormItem> result = new ArrayList<>();
			for (String phyClusterId : phyClusterIdStr.split(",")) {
				result.addAll(orderCommonService.getDoubleBackupAfterConfigList(Long.valueOf(phyClusterId)));
			}
			return BaseResponse.success(result);
		} catch (Exception e) {
			log.error("Failed to obtain the optional configuration of the second standby machine", e);
			return BaseResponse.fail(
					"Failed to obtain the optional configuration of the second standby machine: + " + e.getMessage());
		}
	}

	@ApiOperation("Get Available MySQL Version Configurations")
	@GetMapping("/mysqlVersion")
	public BaseResponse<List<FormItem>> getMysqlVersionList() {
		try {
			List<FormItem> result = new ArrayList<>();
			result.add(new FormItem("MySQL5.7", "MySQL5.7"));
			result.add(new FormItem("MySQL8.0", "MySQL8.0"));
			return BaseResponse.success(result);
		} catch (Exception e) {
			log.error("Failed to obtain the configuration of an optional MySQL version", e);
			return BaseResponse
					.fail("Failed to obtain the configuration of an optional MySQL version: + " + e.getMessage());
		}
	}

	@ApiOperation("Get existing instance rules for physical cluster by physical cluster ID")
	@GetMapping("/phyCluster/mysqlConfig")
	public BaseResponse<List<FormItem>> getPhyClusterMysqlConfigList(@RequestParam("phyClusterIdStr") String phyClusterIdStr) {
		try {
			List<FormItem> result = new ArrayList<>();
			for (String phyClusterId : phyClusterIdStr.split(",")) {
				result.addAll(orderCommonService.getPhyClusterMysqlConfig(Long.valueOf(phyClusterId)));
			}

			return BaseResponse.success(result);
		} catch (Exception e) {
			log.error(
					"Failed to obtain a rule for obtaining an existing instance of a physical cluster based on the ID of the physical cluster",
					e);
			return BaseResponse.fail(
					"Failed to obtain a rule for obtaining an existing instance of a physical cluster based on the ID of the physical cluster: + "
							+ e.getMessage());
		}
	}

	@ApiOperation("获取跨区迁移工单IDC映射组合")
	@GetMapping("/crossIdcMigrateAfterConfig")
	public BaseResponse<List<FormItem>> getCrossIdcMigrateAfterConfig(@ApiParam(value = "物理集群id列表，逗号分隔", required = false) @RequestParam("phyClusterIdStr")  String phyClusterIdStr, @RequestHeader(HEADER_GATEWAY_USERNAME) String username) {
		try {
			List<FormItem> result = orderCommonService.getCrossIdcMigrateAfterConfig(phyClusterIdStr);
			return BaseResponse.success(result);
		} catch (Exception e) {
			log.error("crossIdcMigrateAfterConfig error", e);
			return BaseResponse.fail("获取跨区迁移工单IDC映射组合出错: " + e.getMessage());
		}
	}


}
