package com.pinduoduo.mountain.api.controller.operationlog;

import com.pinduoduo.mountain.api.dto.operationlog.OperationLogDTO;
import com.pinduoduo.mountain.api.service.operationlog.OperationLogService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Api("用户相关操作")
@RestController
@RequestMapping("/api/v2/operationlog")
@Slf4j
public class OperationLogController {

    private final OperationLogService operationLogService;

    public OperationLogController(OperationLogService operationLogService) {
        this.operationLogService = operationLogService;
    }

    /**
     * 搜索用户操作日志列表
     * 1.username（等值匹配）
     *
     * @param username 用户名
     * @return BaseResponse<List < OperationLogDTO>>
     */
    @GetMapping("/list")
    @ApiOperation("获取用户操作日志列表")
    public BaseResponse<PageResult<OperationLogDTO>> operationLogList(
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("用户名") @RequestParam(value = "username", required = false, defaultValue = "") String username,
            @ApiParam("开始时间") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "startTime", required = false, defaultValue = "") LocalDateTime startTime,
            @ApiParam("结束时间") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "endTime", required = false, defaultValue = "") LocalDateTime endTime
    ) {
        try {
            PageResult<OperationLogDTO> result = operationLogService.list(username, startTime, endTime, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("operationLogList error", e);
            return BaseResponse.fail(e);
        }
    }

}
