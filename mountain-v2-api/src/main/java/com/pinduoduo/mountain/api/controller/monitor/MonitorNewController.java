package com.pinduoduo.mountain.api.controller.monitor;

import com.pinduoduo.mountain.api.dto.monitor.InstanceLabelDTO;
import com.pinduoduo.mountain.api.dto.monitor.MonitorLogicDatabaseDTO;
import com.pinduoduo.mountain.api.dto.monitor.MonitorPhysicalClusterDTO;
import com.pinduoduo.mountain.api.dto.monitor.MonitorSearchHistoryDTO;
import com.pinduoduo.mountain.api.model.request.monitor.MonitorRecordHistoryReq;
import com.pinduoduo.mountain.api.service.monitor.MonitorNewService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("由黑子大佬执笔重新设计的深度监控查询交互相关的的接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/monitorNew")
public class MonitorNewController {

    private final MonitorNewService monitorNewService;

    public MonitorNewController(MonitorNewService monitorNewService) {
        this.monitorNewService = monitorNewService;
    }

    @GetMapping("/historyList")
    @ApiOperation("获取用户的搜索查看历史")
    public BaseResponse<List<MonitorSearchHistoryDTO>> historyList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "资源类型, 可选值：logic_database physical_cluster instance logic_database_search_value " +
                    "physical_cluster_search_value instance_search_value 六个，对应 DB、实例、集群、及它们三个分别的用户搜索关键字 六个类型，" +
                    "前三类是近期查询，后三类是近期搜索", required = true) @RequestParam(value = "resourceType", defaultValue = "instance") String resourceType
    ) {
        try {
            List<MonitorSearchHistoryDTO> result = monitorNewService.historyList(username, env, resourceType);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("historyList error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/recordHistory")
    @ApiOperation("记录用户的搜索查看历史")
    public BaseResponse<Boolean> recordHistory(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "请求体", required = true) @Validated @RequestBody MonitorRecordHistoryReq req
    ) {
        try {
            monitorNewService.recordHistory(username, req);
            return BaseResponse.success(true);
        } catch (Exception e) {
            log.error("recordHistory error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/searchLogicDatabaseList")
    @ApiOperation("搜索用户可见的逻辑库")
    public BaseResponse<PageResult<MonitorLogicDatabaseDTO>> searchLogicDatabaseList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<MonitorLogicDatabaseDTO> result = monitorNewService.searchLogicDatabaseList(username, env, searchVal, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("searchLogicDatabaseList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/searchPhysicalClusterList")
    @ApiOperation("搜索用户可见的物理集群")
    public BaseResponse<PageResult<MonitorPhysicalClusterDTO>> searchPhysicalClusterList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<MonitorPhysicalClusterDTO> result = monitorNewService.searchPhysicalClusterList(username, env, searchVal, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("searchPhysicalClusterList error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/searchInstanceList")
    @ApiOperation("搜索用户可见的实例")
    public BaseResponse<PageResult<InstanceLabelDTO>> searchInstanceList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env") String env,
            @ApiParam(value = "实例角色，可选值 master backup ro bg disaster，含义分别是：主实例 备库实例 只读实例 抽数实例 灾备实例", required = true) @RequestParam(value = "role", defaultValue = "master") String role,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<InstanceLabelDTO> result = monitorNewService.searchInstanceList(username, env, role, searchVal, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("searchInstanceList error", e);
            return BaseResponse.fail(e);
        }
    }

}
