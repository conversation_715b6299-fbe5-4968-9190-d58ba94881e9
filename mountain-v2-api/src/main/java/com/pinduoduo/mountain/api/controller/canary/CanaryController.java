package com.pinduoduo.mountain.api.controller.canary;

import com.pinduoduo.mountain.common.constant.ResponseCode;
import com.pinduoduo.mountain.common.model.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Api(value = "金丝雀相关接口")
@RestController
@RequestMapping("/api/health")
@Slf4j
public class CanaryController {

    @ApiOperation("金丝雀健康检查接口")
    @GetMapping("/ready")
    public BaseResponse<String> ready() {
        return new BaseResponse<>(true, ResponseCode.SUCCESS, "", "healthy");
    }

}
