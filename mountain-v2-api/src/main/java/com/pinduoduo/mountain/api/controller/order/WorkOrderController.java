package com.pinduoduo.mountain.api.controller.order;

import com.pinduoduo.mountain.api.dto.order.DmlDTO;
import com.pinduoduo.mountain.api.dto.order.DmlReq;
import com.pinduoduo.mountain.api.service.order.DmlService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_ENV;


/**
 * <AUTHOR>
 */
@Api("工单类前端交互入口")
@Slf4j
@RestController
@RequestMapping("/api/v2/workOrder")
public class WorkOrderController {

    @Resource
    private DmlService dmlService;

    //    public BaseResponse<String> createKeychainOrder(/*
//            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
//            @RequestHeader(HEADER_ENV) String env,
//            @ApiParam(value = "创建keychain请求体", required = true) @Validated @RequestBody LingquKeychainCreateFormDTO body*/
    @ApiOperation("DML校验")
    @PostMapping(value = "/dmlCheck")
    public BaseResponse<List<DmlDTO>> dmlCheck(@RequestHeader(HEADER_ENV) String env, @RequestBody DmlReq req) {
        try {
            return BaseResponse.success(dmlService.dmlCheck(env, req));
        } catch (Exception e) {
            log.error("dmlCheck failed," + e.getMessage());
            return BaseResponse.fail(e);
        }
    }
}
