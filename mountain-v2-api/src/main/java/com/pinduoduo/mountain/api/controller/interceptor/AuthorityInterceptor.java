package com.pinduoduo.mountain.api.controller.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinduoduo.mountain.common.constant.ResponseCode;
import com.pinduoduo.mountain.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;


/**
 * 请求拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("NullableProblems")
public class AuthorityInterceptor implements HandlerInterceptor {

    /**
     * <a href="https://note.pdd.net/doc/607673381261959168?root=269475391849799680#Y69WX6">Nezha SSO 接入</a>
     */
    @Override
    public boolean preHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler
    ) throws IOException {
        BaseResponse<String> respBody = new BaseResponse<>();
        respBody.setSuccess(false);
        respBody.setErrorCode(ResponseCode.UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");

        ObjectMapper objectMapper = new ObjectMapper();

        String username = request.getHeader(HEADER_GATEWAY_USERNAME);

        String un = request.getHeader("gateway-username");
        String requestURI = request.getRequestURI();
        // /api/v2/tf/order/omegaCallback omega流程完成的回调
        if ("/api/v2/tf/order/omegaCallback".equals(requestURI)){
            return true;
        }
        log.info(String.format("[AuthorityInterceptor] %s gateway-username: %s", requestURI, un));
        if (username == null || username.isEmpty()) {
            respBody.setErrorMessage(String.format("gateway-username is null or empty: %s", username));
            response.getWriter().write(objectMapper.writeValueAsString(respBody));
            return false;
        }

        // String remoteAddr = request.getRemoteAddr();
        // String ticket = request.getHeader(HEADER_X_PALS_TICKET);
        // if (NezhaUtil.isNezhaRequest(remoteAddr, ticket)) {
        //     return true;
        // } else {
        //     respBody.setErrorMessage(String.format("not nezha request, remoteAddr: %s, ticket: %s", remoteAddr, ticket));
        //     response.getWriter().write(objectMapper.writeValueAsString(respBody));
        //     return false;
        // }
        return true;
    }

    @Override
    public void postHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView
    ) {
    }

    @Override
    public void afterCompletion(
            HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex
    ) {
    }

}
