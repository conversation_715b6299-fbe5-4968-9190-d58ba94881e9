package com.pinduoduo.mountain.api.controller.monitor.service;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.Cluster;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.LogicDatabase;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.PhysicalCluster;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.monitor.MonitorDBInstance;

import java.util.List;
import java.util.Map;

public interface MonitorMetaDataService {
    List<LogicDatabase> searchLogicDatabases(String key, String biz, String userId,String env);

    List<LogicDatabase> searchLogicClusters(String key, String biz, String userId, String env);

    List<Cluster> searchClusters(String key, String biz,  String userId, String env);

    List<PhysicalCluster> searchPhyClusters(String key, String biz, String env);

    List<MonitorDBInstance> searchInstances(String key, String biz, String env);
    List<Instance> getInstances(LogicDatabase logicDatabase);
    List<Instance> getInstances(Cluster cluster);
    List<Instance> getInstances(PhysicalCluster physicalCluster);
    List<Instance> getInstances(String key, String biz, String userId, String env);
    Instance getInstance(String instanceId);

    List<String> getBigUser();

    boolean isBigUser(String userId);

    List<String> getMountainDBAs();

    boolean isDBA(String userName);

    List<String> getWhites();

    boolean isInWhiles(String userName);

    String getUserBiz(String userName);

    String getMountainWebDomain();

    String getHostName(String dbId);

    String getPdbPrefix();

    String getMonitorDbConfig(String dbId);

    Map<String, String> getMonitorDbConfig();

    String getEnv();

    List<String> getEnvs();
    List<String> getMetricField(String ns, String metric);
    String getMetric(String ns, String metric);

    List<String> dumpMetrics(String ns);
    List<String> dumpTags(String ns, String metric);
    List<String> dumpTagValues(String ns, String metric, String tagKey);
    List<String> dumpFields(String ns, String metric);
}
