package com.pinduoduo.mountain.api.controller;

import com.pinduoduo.mountain.common.constant.ResponseCode;
import com.pinduoduo.mountain.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ControllerAdvice
@RestController
@Slf4j
public class CustomExceptionHandlerController {

    @ExceptionHandler(Exception.class)
    public BaseResponse<String> handleException(Exception ex) {
        log.error("Exception ", ex);

        String errorMessage;
        if (ex instanceof HttpRequestMethodNotSupportedException) {
            errorMessage = "Request method '" + ((HttpRequestMethodNotSupportedException) ex).getMethod() + "' not supported";
            return BaseResponse.fail(ResponseCode.METHOD_NOT_SUPPORTED, errorMessage);
        } else if (ex instanceof MissingServletRequestParameterException) {
            errorMessage = "Required parameter '" + ((MissingServletRequestParameterException) ex).getParameterName() + "' is missing";
            return BaseResponse.fail(ResponseCode.PARAM_ERROR, errorMessage);
        } else if (ex instanceof NoHandlerFoundException) {
            errorMessage = "No handler found for " + ((NoHandlerFoundException) ex).getHttpMethod() + " " + ((NoHandlerFoundException) ex).getRequestURL();
            return BaseResponse.fail(ResponseCode.NOT_FOUND, errorMessage);
        } else if (ex instanceof BindException) {
            List<FieldError> fieldErrors = ((BindException) ex).getBindingResult().getFieldErrors();
            List<String> collect = fieldErrors.stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.toList());
            return BaseResponse.fail(ResponseCode.PARAM_ERROR, collect.toString());
        } else if (ex instanceof MethodArgumentNotValidException) {
            List<FieldError> fieldErrors = ((MethodArgumentNotValidException) ex).getBindingResult().getFieldErrors();
            List<String> collect = fieldErrors.stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.toList());
            return BaseResponse.fail(ResponseCode.PARAM_ERROR, collect.toString());
        }else if(ex instanceof ConstraintViolationException){
            StringBuilder em = new StringBuilder();
            for (ConstraintViolation<?> constraintViolation : ((ConstraintViolationException) ex).getConstraintViolations()) {
                String p = constraintViolation.getPropertyPath().toString();
                em.append(p).append(" ").append(constraintViolation.getMessage()).append(";");
            }
            return BaseResponse.fail(ResponseCode.PARAM_ERROR, em.toString());
        }else {
            return BaseResponse.fail(ResponseCode.FAIL, ex.getMessage());
        }
    }

}
