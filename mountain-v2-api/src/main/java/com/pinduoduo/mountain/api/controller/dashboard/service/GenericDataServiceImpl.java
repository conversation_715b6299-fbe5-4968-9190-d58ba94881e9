package com.pinduoduo.mountain.api.controller.dashboard.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.GenericMetricConf;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.GenericDataServiceMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.commons.text.StringSubstitutor;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GenericDataServiceImpl implements GenericDataService {
    @Autowired
    private GenericMetricConfService genericMetricConfService;
    @Autowired
    private GenericDataServiceMapper mountainV1GenericDataServiceMapper;

    @Override
    public Map<String, Triple<Boolean, String, Object>> get(GenericQueryParam param) {
        List<String> metricNames = param.getMetricNames();
        if (CollectionUtils.isEmpty(metricNames)) {
            return null;
        }

        Map<String, Triple<Boolean, String, Object>> resMap = new HashMap<>();
        List<GenericMetricConf> confs = genericMetricConfService.getMetricConfs(metricNames);
        Map<String, GenericMetricConf> confMap = confs.stream().collect(Collectors.toMap(GenericMetricConf::getName, Function.identity()));
        for (String m : metricNames) {
            if (!confMap.containsKey(m)) {
                resMap.put(m, new ImmutableTriple<>(false, "metricConf is empty or not configured", null));
            }
        }

        for (GenericMetricConf conf : confs) {
            log.info("get generic metric conf: {}", JSON.toJSONString(conf));
            populateParams(param, conf);
            if (CollectionUtils.isNotEmpty(conf.getSubMetrics())) {
                List<GenericMetricConf> subConfs = genericMetricConfService.getMetricConfs(conf.getSubMetrics());
                conf.setSubMetricConfs(subConfs);
                populateParams(param, subConfs);
            }
            if (conf.isInteger()) {
                resMap.put(conf.getName(), new ImmutableTriple<>(true, "success", this.getInteger(conf)));
            }
            if (conf.isDouble()) {
                resMap.put(conf.getName(), new ImmutableTriple<>(true, "success", this.getDouble(conf)));
            }
            if (conf.isString()) {
                resMap.put(conf.getName(), new ImmutableTriple<>(true, "success", this.getString(conf)));
            }
            if (conf.isTs()) {
                resMap.put(conf.getName(), new ImmutableTriple<>(true, "success", this.getTsData(conf)));
            }
            if (conf.isAggregation()) {
                resMap.put(conf.getName(), new ImmutableTriple<>(true, "success", this.getAggregationData(conf)));
            }
        }
        return resMap;
    }

    @Override
    public Integer getInteger(GenericMetricConf conf) {
        log.info("getInteger:name={},sql={}", conf.getName(), conf.getPopulatedSql());
        if (conf.isMountainV1DataSource()) {
            return mountainV1GenericDataServiceMapper.getInteger(conf.getPopulatedSql());
        } else if (conf.isInMemoryComputeDataSource()) {
            if (StringUtils.equalsIgnoreCase("Integer_orderAvgExecutionTimeUsage", conf.getName())) {
                List<GenericMetricConf> subConfs = conf.getSubMetricConfs();
                Long total = 0L;
                Long cardinal = 0L;
                for (GenericMetricConf subConf : subConfs) {
                    Map<String, Long> agg = getAggregationData(subConf);
                    if (MapUtils.isNotEmpty(agg)) {
                        String k = IterableUtils.first(agg.keySet());
                        Long v = agg.get(k);
                        if (StringUtils.isNotEmpty(k) && v != null && v != 0) {
                            total = total + Integer.parseInt(k) * agg.get(k);
                            cardinal = cardinal + agg.get(k);
                        } else {
                            log.error("getInteger,get-sub-metric:metric={},value={}", JSON.toJSONString(subConf), JSON.toJSONString(agg));
                        }
                    } else {
                        log.error("getInteger,get-sub-metric:metric={},value={},agg is null/empty", JSON.toJSONString(subConf), JSON.toJSONString(agg));
                    }
                }
                return total.intValue() / cardinal.intValue();
            }

            if (StringUtils.isEmpty(conf.getSqlConf()) && CollectionUtils.isNotEmpty(conf.getSubMetrics())) {
                List<GenericMetricConf> confs = genericMetricConfService.getMetricConfs(conf.getSubMetrics());
                if (CollectionUtils.isNotEmpty(confs)) {
                    Integer v = 0;
                    for (GenericMetricConf c : confs) {
                        v = v + this.getInteger(c);
                    }
                    return v;
                }
            }
            if (conf.isUseDefaultValue()) {
                return Integer.parseInt(conf.getDefaultValue());
            }
        }
        return null;
    }

    @Override
    public Double getDouble(GenericMetricConf conf) {
        log.info("getDouble:name={},sql={}", conf.getName(), conf.getPopulatedSql());
        if (conf.isMountainV1DataSource()) {
            return mountainV1GenericDataServiceMapper.getDouble(conf.getPopulatedSql());
        } else if (conf.isInMemoryComputeDataSource()) {
            if (conf.isUseDefaultValue()) {
                return Double.parseDouble(conf.getDefaultValue());
            }
        }
        return null;
    }

    @Override
    public String getString(GenericMetricConf conf) {
        log.info("getString:name={},sql={}", conf.getName(), conf.getPopulatedSql());
        if (conf.isMountainV1DataSource()) {
            return mountainV1GenericDataServiceMapper.getString(conf.getPopulatedSql());
        } else if (conf.isInMemoryComputeDataSource()) {
            if (conf.isUseDefaultValue()) {
                return conf.getDefaultValue();
            }
        }
        return null;
    }

    @Override
    public List<TsDataPoint> getTsData(GenericMetricConf conf) {
        log.info("getTsData:name={},sql={}", conf.getName(), conf.getPopulatedSql());
        if (conf.isMountainV1DataSource()) {
            return mountainV1GenericDataServiceMapper.getTs(conf.getPopulatedSql());
        } else if (conf.isInMemoryComputeDataSource()) {
            if (conf.isUseDefaultValue()) {
                return JSON.parseArray(conf.getDefaultValue(), TsDataPoint.class);
            }
        }

        return null;
    }

    @Override
    public Map<String, Long> getAggregationData(GenericMetricConf conf) {
        log.info("getAggregationData:name={},sql={}", conf.getName(), conf.getPopulatedSql());
        List<Map<String, Object>> res = null;
        if (conf.isMountainV1DataSource()) {
            res = mountainV1GenericDataServiceMapper.getAgg(conf.getPopulatedSql());
        } else if (conf.isInMemoryComputeDataSource()) {
            if (conf.isUseDefaultValue()) {
                res = JSON.parseObject(conf.getDefaultValue(), new TypeReference<List<Map<String, Object>>>() {
                });
            }
        }

        if (CollectionUtils.isEmpty(res)) {
            return null;
        }

        Map<String, Long> map = new HashMap<>();
        for (Map<String, Object> row : res) {
            Object v = row.get("v");
            if (v instanceof BigDecimal) {
                map.put((String) row.get("k"), ((BigDecimal) row.get("v")).longValue());
            } else {
                map.put((String) row.get("k"), (Long) row.get("v"));
            }
        }
        return map;
    }

    private void populateParams(GenericQueryParam param, List<GenericMetricConf> conf) {
        if (CollectionUtils.isEmpty(conf)) {
            return;
        }
        for (GenericMetricConf c : conf) {
            populateParams(param, c);
        }

    }

    private void populateParams(GenericQueryParam param, GenericMetricConf conf) {
        Map<String, Object> valuesMap = new HashMap<>();
        if (param.getBegin() != null) {
            valuesMap.put("lc_timeBegin", DateUtils.formatDate(new Date(param.getBeginTime()), "yyyy-MM-dd HH:mm:ss"));
        }
        if (param.getEnd() != null) {
            valuesMap.put("lc_timeEnd", DateUtils.formatDate(new Date(param.getEndTime()), "yyyy-MM-dd HH:mm:ss"));
        }
        valuesMap.put("lc_bizName", param.getBizName());
        valuesMap.put("lc_serviceName", param.getServiceName());
        valuesMap.put("lc_userId", param.getUserId());
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        String populatedSql = sub.replace(conf.getSqlConf());
        List<String> appends = conf.getAppendWhereConditions();
        if (CollectionUtils.isEmpty(appends)) {
            conf.setPopulatedSql(populatedSql);
            log.info("populateParams,sql={}", conf.getPopulatedSql());
            return;
        }

        for (String append : appends) {
            String populatedAppend = sub.replace(append);
            if (!StringUtils.equals(populatedAppend, append)) {
                populatedSql = String.format("%s %s", populatedSql, populatedAppend);
            }
        }
        log.info("populateParams,sql={}", conf.getPopulatedSql());
    }
}