package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.visitservice.dto.CurrentVisitServiceDTO;
import com.pinduoduo.mountain.service.visitservice.dto.InstanceVisitServiceStatisticDTO;
import com.pinduoduo.mountain.service.visitservice.dto.VisitServiceHistoryDTO;
import com.pinduoduo.mountain.service.visitservice.impl.VisitServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("来访服务相关接口")
@RestController
@RequestMapping("/api/v2/visitService")
@Slf4j
public class VisitServiceController {

    private final VisitServiceService visitServiceService;

    public VisitServiceController(VisitServiceService visitServiceService) {
        this.visitServiceService = visitServiceService;
    }

    @ApiOperation("获取逻辑库维度的来访服务-当前")
    @GetMapping("/logicDatabase")
    public BaseResponse<CurrentVisitServiceDTO> logicDatabase(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId
    ) {
        try {
            CurrentVisitServiceDTO result = visitServiceService.logicDatabase(username, logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicDatabase error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取逻辑库维度的来访服务-历史")
    @GetMapping("/logicDatabaseHistory")
    public BaseResponse<PageResult<VisitServiceHistoryDTO>> logicDatabaseHistory(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId,
            @ApiParam(value = "几天以前", required = true) @RequestParam(value = "day") int day,
            @ApiParam(value = "页数") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<VisitServiceHistoryDTO> result = visitServiceService.logicDatabaseHistory(username, logicDbId, day, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicDatabaseHistory error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取逻辑库维度的来访服务-服务访问统计")
    @GetMapping("/logicDatabaseStatistics")
    public BaseResponse<List<InstanceVisitServiceStatisticDTO>> logicDatabaseStatistics(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId
    ) {
        try {
            List<InstanceVisitServiceStatisticDTO> result = visitServiceService.logicDatabaseStatistics(logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicDatabaseStatistics error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取逻辑表维度的来访服务-当前")
    @GetMapping("/logicTable")
    public BaseResponse<CurrentVisitServiceDTO> logicTable(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam("logicTableId") long logicTableId
    ) {
        try {
            CurrentVisitServiceDTO result = visitServiceService.logicTable(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicTable error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取逻辑表维度的来访服务-历史")
    @GetMapping("/logicTableHistory")
    public BaseResponse<PageResult<VisitServiceHistoryDTO>> logicTableHistory(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam("logicTableId") long logicTableId,
            @ApiParam(value = "几天以前", required = true) @RequestParam(value = "day") int day,
            @ApiParam(value = "页数") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        try {
            PageResult<VisitServiceHistoryDTO> result = visitServiceService.logicTableHistory(username, logicTableId, day, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicTableHistory error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取逻辑表维度的来访服务-服务访问统计")
    @GetMapping("/logicTableStatistics")
    public BaseResponse<List<InstanceVisitServiceStatisticDTO>> logicTableStatistics(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑表 ID", required = true) @RequestParam("logicTableId") long logicTableId
    ) {
        try {
            List<InstanceVisitServiceStatisticDTO> result = visitServiceService.logicTableStatistics(username, logicTableId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("logicTableStatistics error", e);
            return BaseResponse.fail(e);
        }
    }

}
