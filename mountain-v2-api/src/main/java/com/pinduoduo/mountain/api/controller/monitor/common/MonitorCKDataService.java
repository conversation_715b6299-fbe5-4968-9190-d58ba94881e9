package com.pinduoduo.mountain.api.controller.monitor.common;

import com.pinduoduo.mountain.repository.clickhouse.entity.ProcessListSqlDetail;
import com.pinduoduo.mountain.repository.clickhouse.entity.SlowQueryDetail;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;

import java.util.Date;
import java.util.List;

public interface MonitorCKDataService {
    String PROCESS_LIST_CNT = "process_list_cnt";
    String SLOW_QUERY_CNT = "slow_query_cnt";

    String getCKPeriod(Date start, Date end);

    Integer getTsPeriod(Date start, Date end);

    List<TsDataPoint> get(String metric, String instId, Date start, Date end);

    Long getProcessListCnt(String instId, Date start, Date end);

    List<TsDataPoint> getProcessList(String instId, Date start, Date end, Integer pageIndex, Integer pageSize);

    ProcessListSqlDetail getProcesslistSQLdetail(String instId, Long fingerprint);

    List<SlowQueryDetail> slowQueryDetail(String instanceId, Date start, Date end);

    ProcessListSqlDetail sqlDetail(String instanceId, Long fingerprint, Date start, Date end);

    Long getProcessListCollectTotal(String instId, Date beginTime, Date endTime);

    List<ProcessListSqlDetail> getProcessListData(String instId, Date beginTime, Date endTime, Integer pageIndex, Integer pageSize);
}
