package com.pinduoduo.mountain.api.service.callback;

import com.pinduoduo.mountain.api.model.request.callback.OmegaApproveFlowCallbackReq;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.OrderSql;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.OrderSqlMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OmegaCallbackService {

    private final OrderSqlMapper orderSqlMapper;

    public OmegaCallbackService(OrderSqlMapper orderSqlMapper) {
        this.orderSqlMapper = orderSqlMapper;
    }

    /**
     * <a href="https://note.pdd.net/doc/496344297989447680?root=496344150063153152#8cn2BZ">状态说明，参见文档</a>
     */
    public void ddlApproveFlowCallback(OmegaApproveFlowCallbackReq omegaApproveFlowCallbackReq) {
        // 5 审批被拒
        // 6 工单关闭
        // 7 工单审批超时
        // 8 子单执行人驳回
        // 9 异常
        // 10 用户撤销
        // 统一到 mountain 上转为 [omega已关闭] 状态
        if (omegaApproveFlowCallbackReq.getStatus() >= 5 && omegaApproveFlowCallbackReq.getStatus() <= 10) {
            orderSqlMapper.updateOmegaStatusByOmegaId(OrderSql.STATUS_OMEGA_CLOSED, omegaApproveFlowCallbackReq.getId());
        }
    }

    public void ddlStatusSync(String workId) {
        orderSqlMapper.updateOmegaStatusByWorkId(OrderSql.STATUS_OMEGA_APPROVED, Long.valueOf(workId));
    }
}
