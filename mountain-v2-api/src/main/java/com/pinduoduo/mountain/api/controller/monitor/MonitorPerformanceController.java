package com.pinduoduo.mountain.api.controller.monitor;


import com.pinduoduo.mountain.api.controller.monitor.common.MonitorCKDataService;
import com.pinduoduo.mountain.api.controller.monitor.common.MonitorDataService;
import com.pinduoduo.mountain.repository.clickhouse.entity.ProcessListSqlDetail;
import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.InstanceMapper;
import com.site.lookup.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v2/monitor/performance")
public class MonitorPerformanceController {
    @Autowired
    private MonitorDataService monitorDataService;
    @Autowired
    private MonitorCKDataService monitorCKDataService;
    @Autowired
    private InstanceMapper instanceMapper;

    @GetMapping("/monitor_analysis/cpu_slow_processlist_list")
    public GenericResponse queryLogicClusters(@RequestParam("instanceId") String instanceId,
                                              @RequestParam("start") String startStr,
                                              @RequestParam("end") String endStr,
                                              @RequestParam("userId") String user,
                                              @RequestParam("env") String env) {
        try {
            Instance instance = instanceMapper.selectOneByInstanceId(instanceId);
            Date start = DateUtils.parseDate(startStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = DateUtils.parseDate(endStr, "yyyy-MM-dd'T'HH:mm:ss");
            Integer tsPeriod = monitorCKDataService.getTsPeriod(start, end);
            MetricData cpuTs = monitorDataService.getDataPoints(
                    "ROC.CDB",
                    "cpu_use_rate",
                    Arrays.asList(new ImmutableTriple<>("instanceId", "=", Arrays.asList(instanceId))),
                    Arrays.asList("ROC", "CDB"),
                    Arrays.asList("cpu_use_rate_delta"),
                    Arrays.asList("instanceId"),
                    tsPeriod,
                    start.getTime(), end.getTime(), instance.getEnv(), instance.getType());
            List<Map<String, Object>> datas = new LinkedList<>();
            List<TsDataPoint> cpuRate = cpuTs.getTsDataPoint(instanceId);
            if (CollectionUtils.isEmpty(cpuRate)) {
                return new GenericResponse(true, datas);
            }

            List<TsDataPoint> slowCnt = monitorCKDataService.get(monitorCKDataService.SLOW_QUERY_CNT, instanceId, start, end);
            Map<String, TsDataPoint> slowMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(slowCnt)) {
                slowMap = slowCnt.stream().filter(Objects::nonNull).collect(Collectors.toMap(TsDataPoint::getTime, Function.identity()));
            }
            List<TsDataPoint> processCnt = monitorCKDataService.get(monitorCKDataService.PROCESS_LIST_CNT, instanceId, start, end);
            Map<String, TsDataPoint> processMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(processCnt)) {
                processMap = processCnt.stream().filter(Objects::nonNull).collect(Collectors.toMap(TsDataPoint::getTime, Function.identity()));
            }
            for (int i = 0; i < cpuRate.size(); ++i) {
                Map<String, Object> dataMap = new HashMap<>();
                String key = cpuRate.get(i).getTime();
                dataMap.put("cpu", cpuRate.get(i).getValue());
                dataMap.put("time", cpuRate.get(i).getTime());
                dataMap.put("slow", slowMap.get(key) == null ? 0 : slowMap.get(key).getValue());
                dataMap.put("processlist", processMap.get(key) == null ? 0 : processMap.get(key).getValue());
                datas.add(dataMap);
            }
            return new GenericResponse(true, datas);
        } catch (Exception e) {
            return new GenericResponse(false, String.format("%s, %s", Arrays.toString(e.getStackTrace()), e.getMessage()));
        }
    }

    @GetMapping("/processlist/processlistGatherTime/cnt")
    public GenericResponse processlistGatherTimeCnt(@RequestParam("instanceId") String instanceId,
                                                    @RequestParam("start") String startStr,
                                                    @RequestParam("end") String endStr) {
        try {
            Date start = DateUtils.parseDate(startStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = DateUtils.parseDate(endStr, "yyyy-MM-dd'T'HH:mm:ss");
            return new GenericResponse(true, monitorCKDataService.getProcessListCnt(instanceId, start, end));
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/processlist/processlistGatherTime")
    public GenericResponse processlistGatherTime(@RequestParam("instanceId") String instanceId,
                                                 @RequestParam("start") String startStr,
                                                 @RequestParam("end") String endStr,
                                                 @RequestParam("pageIndex") Integer pageIndex,
                                                 @RequestParam("pageSize") Integer pageSize,
                                                 @RequestParam("userId") String user,
                                                 @RequestParam("env") String env) {
        try {
            Date start = DateUtils.parseDate(startStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = DateUtils.parseDate(endStr, "yyyy-MM-dd'T'HH:mm:ss");
            return new GenericResponse(true, monitorCKDataService.getProcessList(instanceId, start, end, pageIndex, pageSize));
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/processlist/processlistFetchTime")

    public GenericResponse processlistFetchTime(@RequestParam("instanceId") String instanceId,
                                                @RequestParam("cond") String cond,
                                                @RequestParam("closecat") Long closecat,
                                                @RequestParam(value = "collect", required = false) String collectStr,
                                                @RequestParam(value = "collectTs", required = false) Long collectTsStr,
                                                @RequestParam("pageIndex") Integer pageIndex,
                                                @RequestParam("pageSize") Integer pageSize,
                                                @RequestParam("userId") String user,
                                                @RequestParam("env") String env) {
        try {
            Date collect = null;
            if (collectTsStr != null) {
                collect = new Date(collectTsStr);
            } else if (StringUtils.isNotEmpty(collectStr)) {
                collect = DateUtils.parseDate(collectStr, "yyyy-MM-dd'T'HH:mm:ss");
            } else {
                return new GenericResponse(false, "时间不能为空");
            }
            Date start = DateUtils.addMinutes(collect, -1);
            Date end = DateUtils.addMinutes(collect, 1);
            Long cnt = monitorCKDataService.getProcessListCollectTotal(instanceId, start, end);
            List<ProcessListSqlDetail> data = monitorCKDataService.getProcessListData(instanceId, start, end, pageIndex, pageSize);
            Map<String, Object> res = new HashMap<>();
            res.put("current_batch", cnt);
            res.put("data", data);
            return new GenericResponse(true, res);
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/processlist/processlistFetchTimeCnt")
    public GenericResponse processlistFetchTimeCnt(@RequestParam("instanceId") String instanceId,
                                                   @RequestParam("cond") String cond,
                                                   @RequestParam("closecat") Long closecat,
                                                   @RequestParam(value = "collect", required = false) String collectStr,
                                                   @RequestParam(value = "collectTs", required = false) Long collectTsStr,
                                                   @RequestParam("userId") String user,
                                                   @RequestParam("env") String env) {
        try {
            Date collect = null;
            if (collectTsStr != null) {
                collect = new Date(collectTsStr);
            } else if (StringUtils.isNotEmpty(collectStr)) {
                collect = DateUtils.parseDate(collectStr, "yyyy-MM-dd'T'HH:mm:ss");
            } else {
                return new GenericResponse(false, "时间不能为空");
            }
            Date start = DateUtils.addMinutes(collect, -1);
            Date end = DateUtils.addMinutes(collect, 1);
            Long cnt = monitorCKDataService.getProcessListCollectTotal(instanceId, start, end);
            return new GenericResponse(true, cnt);
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }


    @GetMapping("/monitor_analysis/processlistsqldetail")
    public GenericResponse processlistsqldetail(@RequestParam("instanceId") String instanceId,
                                                @RequestParam("fingerprint") Long fingerprint,
                                                @RequestParam("start") String startStr,
                                                @RequestParam("end") String endStr,
                                                @RequestParam("userId") String user,
                                                @RequestParam("env") String env) {
        try {
            Date start = DateUtils.parseDate(startStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = DateUtils.parseDate(endStr, "yyyy-MM-dd'T'HH:mm:ss");
            return new GenericResponse(true, monitorCKDataService.getProcesslistSQLdetail(instanceId, fingerprint));
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/monitor_analysis/slow_query_detail")
    public GenericResponse slow_query_detail(@RequestParam("instanceId") String instanceId,
                                             @RequestParam("start") String startStr,
                                             @RequestParam("end") String endStr,
                                             @RequestParam("userId") String user,
                                             @RequestParam("env") String env) {
        try {
            Date start = DateUtils.parseDate(startStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = DateUtils.parseDate(endStr, "yyyy-MM-dd'T'HH:mm:ss");
            return new GenericResponse(true, monitorCKDataService.slowQueryDetail(instanceId, start, end));
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/monitor_analysis/sqldetail")
    public GenericResponse sqldetail(@RequestParam("instanceId") String instanceId,
                                     @RequestParam("fingerprint") Long fingerprint,
                                     @RequestParam("start") String startStr,
                                     @RequestParam("end") String endStr,
                                     @RequestParam("userId") String user,
                                     @RequestParam("env") String env) {
        try {
            Date start = DateUtils.parseDate(startStr, "yyyy-MM-dd'T'HH:mm:ss");
            Date end = DateUtils.parseDate(endStr, "yyyy-MM-dd'T'HH:mm:ss");
            return new GenericResponse(true, monitorCKDataService.sqlDetail(instanceId, fingerprint, start, end));
        } catch (Exception e) {
            return new GenericResponse(false, e.getMessage());
        }
    }

    @GetMapping("/test")
    public String getTest() {
        return "test";
    }
}