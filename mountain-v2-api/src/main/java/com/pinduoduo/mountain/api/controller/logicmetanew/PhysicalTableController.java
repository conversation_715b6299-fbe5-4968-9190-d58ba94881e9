package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.metadata.dto.*;
import com.pinduoduo.mountain.service.metadata.impl.PhysicalTableService;
import com.pinduoduo.mountain.service.metadata.request.PhysicalTableInfoByThirdPartyReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("物理表相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/physicalTable")
public class PhysicalTableController {

    private final PhysicalTableService physicalTableService;

    public PhysicalTableController(PhysicalTableService physicalTableService) {
        this.physicalTableService = physicalTableService;
    }

    @GetMapping("/listByLogicDbIdAndInstance")
    @ApiOperation("获取物理表的信息列表")
    public BaseResponse<PageResult<PhysicalTableInfoDTO>> listByLogicDbIdAndInstance(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam(value = "logicDbId") long logicDbId,
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "表类型，0 业务表和临时表一起返回 1 业务表、2 临时表、3 硬链接文件表", defaultValue = "0") @RequestParam(value = "tableType", required = false, defaultValue = "0") int tableType,
            @ApiParam(value = "排序字段，可选值 AUTO_INCREMENT 自增ID值、DATA_LENGTH 表大小、DATA_FREE 碎片大小，当 type = 3 时，这里只能传 DATA_LENGTH 表大小", defaultValue = "") @RequestParam(value = "orderBy", required = false, defaultValue = "") String orderBy,
            @ApiParam(value = "排序字段，可选值 ASC 升序 DESC 降序，只有传 orderBy 时 sortType 参数才生效", defaultValue = "") @RequestParam(value = "sortType", required = false, defaultValue = "") String sortType,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "模糊搜索物理表名", required = true) @RequestParam(value = "searchVal", defaultValue = "") String searchVal
    ) {
        try {
            if (orderBy != null && !orderBy.isEmpty()) {
                orderBy = orderBy.toUpperCase();
                if (!orderBy.equalsIgnoreCase("AUTO_INCREMENT") && !orderBy.equalsIgnoreCase("DATA_LENGTH") && !orderBy.equalsIgnoreCase("DATA_FREE")) {
                    return BaseResponse.fail(String.format("不支持的 orderBy 参数 [%s]", orderBy));
                }
                if (tableType == 3) {
                    if (!orderBy.equalsIgnoreCase("DATA_LENGTH")) {
                        return BaseResponse.fail("硬链接文件表只支持的按照表大小排序");
                    }
                }
                if (sortType != null && !sortType.isEmpty()) {
                    if (!sortType.equalsIgnoreCase("ASC") && !sortType.equalsIgnoreCase("DESC")) {
                        return BaseResponse.fail(String.format("不支持的 sortType 参数 [%s]", sortType));
                    }
                }
            }
            if ((orderBy == null || orderBy.isEmpty()) && (sortType != null && !sortType.isEmpty())) {
                return BaseResponse.fail("orderBy 参数为空时，sortType 参数传递无效");
            }

            PageResult<PhysicalTableInfoDTO> result = physicalTableService.listByLogicDbIdAndInstance(username, logicDbId, instanceId, tableType, orderBy, sortType, pageNum, pageSize, searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("listByLogicDbIdAndInstance error", e);
            return BaseResponse.fail(e);
        }
    }

    @PostMapping("/infoByThirdParty")
    @ApiOperation("根据第三方平台查询到的物理表的其他信息，例如读写qps、是否有抽数任务")
    public BaseResponse<List<PhysicalTableInfoByThirdPartyDTO>> infoByThirdParty(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "物理表请求信息", required = true) @RequestBody PhysicalTableInfoByThirdPartyReq req
    ) {
        try {
            List<PhysicalTableInfoByThirdPartyDTO> result = physicalTableService.infoByThirdParty(username, req);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("infoByThirdParty error", e);
            return BaseResponse.fail(e);
        }
    }


    @GetMapping("/tableUseAnalysis")
    @ApiOperation("获取物理表的表使用分析")
    public BaseResponse<PageResult<PhysicalTableUseAnalysisDTO>> tableUseAnalysis(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库名", required = true) @RequestParam(value = "tableSchema") String tableSchema,
            @ApiParam(value = "物理表名", required = true) @RequestParam(value = "tableName") String tableName,
            @ApiParam(value = "排序字段，可选值 COUNT_STAR 访问总行数", defaultValue = "") @RequestParam(value = "orderBy", required = false, defaultValue = "") String orderBy,
            @ApiParam(value = "排序字段，可选值 ASC 升序 DESC 降序，只有传 orderBy 时 sortType 参数才生效", defaultValue = "") @RequestParam(value = "sortType", required = false, defaultValue = "") String sortType,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        try {
            if (orderBy != null && !orderBy.isEmpty()) {
                orderBy = orderBy.toUpperCase();
                if (!orderBy.equalsIgnoreCase("COUNT_STAR")) {
                    return BaseResponse.fail(String.format("不支持的 orderBy 参数 [%s]", orderBy));
                }

                if (sortType != null && !sortType.isEmpty()) {
                    if (!sortType.equalsIgnoreCase("ASC") && !sortType.equalsIgnoreCase("DESC")) {
                        return BaseResponse.fail(String.format("不支持的 sortType 参数 [%s]", sortType));
                    }
                }
            }
            if ((orderBy == null || orderBy.isEmpty()) && (sortType != null && !sortType.isEmpty())) {
                return BaseResponse.fail("orderBy 参数为空时，sortType 参数传递无效");
            }

            PageResult<PhysicalTableUseAnalysisDTO> result = physicalTableService.tableUseAnalysis(username, instanceId, tableSchema, tableName, orderBy, sortType, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("tableUseAnalysis error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/tableIndexUseAnalysis")
    @ApiOperation("获取物理表的索引使用分析")
    public BaseResponse<PageResult<PhysicalTableIndexUseAnalysisDTO>> tableIndexUseAnalysis(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库名", required = true) @RequestParam(value = "tableSchema") String tableSchema,
            @ApiParam(value = "物理表名", required = true) @RequestParam(value = "tableName") String tableName,
            @ApiParam(value = "索引名") @RequestParam(value = "indexName",  required = false, defaultValue = "") String indexName,
            @ApiParam(value = "页数", required = true) @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "单页个数", required = true) @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        try {
            PageResult<PhysicalTableIndexUseAnalysisDTO> result = physicalTableService.tableIndexUseAnalysis(username, instanceId, tableSchema, tableName, indexName, pageNum, pageSize);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("tableIndexUseAnalysis error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/tableMetaInfo")
    @ApiOperation("获取物理表的元信息/统计信息")
    public BaseResponse<TableMetaNewInfoDTO> tableMetaInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库名", required = true) @RequestParam(value = "tableSchema") String tableSchema,
            @ApiParam(value = "物理表名", required = true) @RequestParam(value = "tableName") String tableName) {
        try {
            TableMetaNewInfoDTO result = physicalTableService.tableMetaInfo(username, instanceId, tableSchema, tableName);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("tableMetaInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/tableIndexInfo")
    @ApiOperation("获取物理表的索引统计信息")
    public BaseResponse<List<TableIndexNewInfoDTO>> tableIndexInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库名", required = true) @RequestParam(value = "tableSchema") String tableSchema,
            @ApiParam(value = "物理表名", required = true) @RequestParam(value = "tableName") String tableName) {
        try {
            List<TableIndexNewInfoDTO> result = physicalTableService.tableIndexInfo(username, instanceId, tableSchema, tableName);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("tableIndexInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/tableIndexList")
    @ApiOperation("获取物理表的索引列表")
    public BaseResponse<List<TableIndexDTO>> tableIndexList(
            @ApiParam(value = "实例 ID", required = true) @RequestParam(value = "instanceId") String instanceId,
            @ApiParam(value = "物理库名", required = true) @RequestParam(value = "tableSchema") String tableSchema,
            @ApiParam(value = "物理表名", required = true) @RequestParam(value = "tableName") String tableName) {
        try {
            List<TableIndexDTO> result = physicalTableService.tableIndexList(instanceId, tableSchema, tableName);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("tableIndexList error", e);
            return BaseResponse.fail(e);
        }
    }
}
