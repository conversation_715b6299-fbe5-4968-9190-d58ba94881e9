package com.pinduoduo.mountain.api.controller.monitor;

import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicMetricData {
    @ApiModelProperty("指标是否成功")
    private boolean success;
    @ApiModelProperty("指标查询状态信息")
    private String msg;
    @ApiModelProperty("指标 label")
    private String feDesc;
    @ApiModelProperty("指标数据")
    private List<TsDataPoint> data;
}
