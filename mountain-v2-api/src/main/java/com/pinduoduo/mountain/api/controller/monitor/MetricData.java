package com.pinduoduo.mountain.api.controller.monitor;

import com.pinduoduo.mountain.repository.mysql.cdbmonitor.entity.TsDataPoint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetricData {
    private Map<String, List<TsDataPoint>> data = new HashMap<>();

    public void add(String instanceId, List<TsDataPoint> dataPoint) {
        data.put(instanceId, dataPoint);
    }

    public List<TsDataPoint> getTsDataPoint(String instanceId) {
        return data.get(instanceId);
    }
}
