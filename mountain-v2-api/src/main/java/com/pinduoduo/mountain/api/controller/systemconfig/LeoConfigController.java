package com.pinduoduo.mountain.api.controller.systemconfig;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.thirdparty.leo.LeoService;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.ParameterModifyLog;
import com.pinduoduo.mountain.service.systemconfig.impl.SystemConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("leo相关接口（非Mountain系统配置部分）")
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v2/leo")
public class LeoConfigController {
    private final LeoService leoService;

    public LeoConfigController(LeoService leoService) {
        this.leoService = leoService;
    }

    @GetMapping("/getLeoConfig")
    @ApiOperation("获取leo的配置")
    public BaseResponse<String> getLeoConfig(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "leo名称", required = true)  @RequestParam(value = "leoName") String leoName
    ) {
        try {
            String result = leoService.getProperty(leoName).getValue();
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getModifyHistory error", e);
            return BaseResponse.fail(e);
        }
    }
}
