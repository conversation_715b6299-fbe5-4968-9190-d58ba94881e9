package com.pinduoduo.mountain.api.service.dbtracking;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.pinduoduo.mountain.common.constant.Time;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.repository.mysql.pddmountain.entity.DbtrackingDtls;
import com.pinduoduo.mountain.repository.mysql.pddmountain.entity.DbtrackingProject;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.DbtrackingDtlsMapper;
import com.pinduoduo.mountain.repository.mysql.pddmountain.mapper.DbtrackingProjectMapper;
import com.yiran.arch.leo.util.LeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DbtrackingService {

    private final DbtrackingProjectMapper dbtrackingProjectMapper;
    private final DbtrackingDtlsMapper dbtrackingDtlsMapper;

    public DbtrackingService(@Qualifier("dbtrackingProjectMapper") DbtrackingProjectMapper dbtrackingProjectMapper, @Qualifier("dbtrackingDtlsMapper") DbtrackingDtlsMapper dbtrackingDtlsMapper) {
        this.dbtrackingProjectMapper = dbtrackingProjectMapper;
        this.dbtrackingDtlsMapper = dbtrackingDtlsMapper;
    }

    public Pair<Boolean, String> loadDtlData(String projectCode, String parseType, String loadType, String updateType, List<JsonNode> dtlList, DbtrackingProject dbtrackingProject) {
        try {
            if (dbtrackingProject == null) {
                dbtrackingProject = dbtrackingProjectMapper.selectByProjectCode(projectCode);
                if (dbtrackingProject == null) {
                    return Pair.of(false, String.format("未查询到项目信息（编号：%s），请先在问题项管理页面添加项目！", projectCode));
                }
            }

            String dtlsColumnDefinition = dbtrackingProject.getDtlsColumnDefinition();
            if (dtlsColumnDefinition == null) {
                return Pair.of(false, String.format("项目（%s）未初始化，请先进行初始化定义，再上传/更新数据！", projectCode));
            }

            /*
             dtls_column_definition eg:
             {
                "column_mapping": [
                    {
                        "bizKey": "business",
                        "name_cn": "业务线",
                        "name_en": "business"
                    },
                    {
                        "bizKey": "service_name",
                        "name_cn": "服务",
                        "name_en": "service_name"
                    }, {}......
               ],
                "required_columns": {
                    "owner": "owner",
                    "business": "business",
                    "service_name": "service_name",
                    "primary_identity_column": "pdd_id",
                    "second_identity_columns": "ins_name"
                }
             }
             */
            JsonNode dtlsColumnDefinitionJson = StringUtil.getObjectMapper().readTree(dtlsColumnDefinition);
            if (!dtlList.isEmpty()) {
                // 检查新上报的数据的列，是否和定义的问题跟进中的一致
                List<String> newColumns = new ArrayList<>(10);
                Iterator<String> fieldNames = dtlList.get(0).fieldNames();
                while (fieldNames.hasNext()) {
                    newColumns.add(fieldNames.next());
                }

                List<String> definedColumns = new ArrayList<>(10);
                for (JsonNode columnMappingJson : dtlsColumnDefinitionJson.get("column_mapping")) {
                    definedColumns.add(columnMappingJson.get("name_en").asText());
                }
                Pair<Boolean, String> checkColumnsResult = checkColumns(projectCode, parseType, loadType, definedColumns, newColumns);
                if (!checkColumnsResult.getLeft()) {
                    return checkColumnsResult;
                }
            }

            if ("update".equals(loadType)) {
                JsonNode requiredColumns = dtlsColumnDefinitionJson.get("required_columns");
                List<DbtrackingDtls> loadDataList = generateLoadDataList(projectCode, requiredColumns, dtlList);
                dbtrackingDtlsMapper.updateUpdatedFlagByProjectCode(projectCode, 0);
                if ("full".equals(updateType)) {
                    Set<String> statusUnfinishedOwnerSet = LeoUtils.getJsonProperty("mountain-v2-api.dbtracking_detail_status_unfinished_owner", new TypeReference<Set<String>>() {
                    });
                    Set<String> statusSet = LeoUtils.getJsonProperty("mountain-v2-api.dbtracking_detail_status", new TypeReference<Set<String>>() {
                    });
                    Set<String> finishedStatus = new HashSet<>(statusSet);
                    finishedStatus.removeAll(statusUnfinishedOwnerSet);
                    if (dbtrackingProject.getIsResetTmpWhite() == 0) {
                        finishedStatus.add("临时加班");
                    }
                    String finishedStatusInSql = String.format("'%s'", String.join("', '", finishedStatus));
                    dbtrackingDtlsMapper.updateStatusAndConfirmResultAndLastDealResult("已完成", "", "\n", String.format("system, 将状态重置为“已完成”, %s", LocalDateTime.now().format(Time.FORMATTER)), projectCode, finishedStatusInSql, 0);
                    // 已经被标记为“已完成”的，如果本次全量更新导入的数据还存在时，重置为“未处理”
                    dbtrackingDtlsMapper.updateStatusAndConfirmResultAndLastDealResult("未处理", "", "\n", String.format("system, 将状态重置为“未处理”, %s", LocalDateTime.now().format(Time.FORMATTER)), projectCode, "'已完成'", 1);
                } else if ("incr".equals(updateType)) {
                    dbtrackingDtlsMapper.updateStatusAndConfirmResultAndLastDealResult("未处理", "", "\n", String.format("system, 将状态重置为“未处理”, %s", LocalDateTime.now().format(Time.FORMATTER)), projectCode, "'已完成'", 1);
                } else {
                    return Pair.of(false, String.format("不支持的 updateType 类型 %s ！", updateType));
                }

                for (DbtrackingDtls dbtrackingDtls : loadDataList) {
                    dbtrackingDtlsMapper.insertIntoOnDuplicateKeyUpdate(dbtrackingDtls);
                }
                dbtrackingDtlsMapper.updateUpdatedFlagByProjectCode(projectCode, 0);
                return Pair.of(true, "数据更新成功");
            } else {
                return Pair.of(false, String.format("不支持的 loadType 类型 %s ！", loadType));
            }
        } catch (JsonProcessingException e) {
            log.error(String.format("loadDtlData error projectCode %s, parseType %s, loadType %s, updateType %s dtlList %s, DbtrackingProject %s", projectCode, parseType, loadType, updateType, dtlList, dbtrackingProject), e);
            throw new RuntimeException(e);
        }
    }

    private List<DbtrackingDtls> generateLoadDataList(String projectCode, JsonNode requiredColumns, List<JsonNode> dtlList) {
        String businessColumn = requiredColumns.get("business").asText();
        String serviceNameColumn = requiredColumns.get("service_name").asText();
        String ownerColumn = requiredColumns.get("owner").asText();
        String primaryIdentityColumn = requiredColumns.get("primary_identity_column").asText();
        String secondIdentityColumn = requiredColumns.get("second_identity_columns").asText();

        List<String> secondIdentityColumns = StringUtils.isBlank(secondIdentityColumn) ? Collections.emptyList() : Arrays.asList(secondIdentityColumn.split(","));
        List<DbtrackingDtls> loadDataList = new ArrayList<>(10);
        for (JsonNode dtlJsonNode : dtlList) {
            String dtlDataJson = dtlJsonNode.toString();
            String business = dtlJsonNode.get(businessColumn).asText();
            String serviceName = dtlJsonNode.get(serviceNameColumn).asText();
            String owner = dtlJsonNode.get(ownerColumn).asText();
            String primaryIdentity = dtlJsonNode.get(primaryIdentityColumn).asText();

            HashMap<String, JsonNode> secondIdentityMap = new HashMap<>(10);
            if (!secondIdentityColumns.isEmpty()) {
                for (String sic : secondIdentityColumns) {
                    secondIdentityMap.put(sic, dtlJsonNode.get(sic));
                }
            }
            String secondIdentity = null;
            String secondIdentityMd5 = "";
            if (!secondIdentityMap.isEmpty()) {
                secondIdentity = StringUtil.jsonSerialize(secondIdentityMap);
                secondIdentityMd5 = StringUtil.calculateMd5(secondIdentity);
            }
            DbtrackingDtls dbtrackingDtls = new DbtrackingDtls();
            dbtrackingDtls.setProjectCode(projectCode);
            dbtrackingDtls.setBusiness(business);
            dbtrackingDtls.setServiceName(serviceName);
            dbtrackingDtls.setPrimaryIdentity(primaryIdentity);
            dbtrackingDtls.setSecondIdentity(secondIdentity);
            dbtrackingDtls.setSecondIdentityHash(secondIdentityMd5);
            dbtrackingDtls.setOwner(owner);
            dbtrackingDtls.setDtlData(dtlDataJson);
            dbtrackingDtls.setStatus("待处理");
            dbtrackingDtls.setUpdatedFlag(1);
            loadDataList.add(dbtrackingDtls);
        }
        return loadDataList;
    }

    public Pair<Boolean, String> checkColumns(String projectCode, String parseType, String loadType, List<String> oldColumns, List<String> newColumns) {
        Set<String> oldColumnSet = new HashSet<>(oldColumns);
        Set<String> newColumnSet = new HashSet<>(newColumns);
        if (!oldColumnSet.equals(newColumnSet)) {
            if ("init".equals(loadType)) {
                // 初始化的时候，导入数据时需要校验
                if ("load-data".equals(parseType)) {
                    return Pair.of(false, String.format("Project %s 字段映射定义的列，和解析时的不一致！\n" +
                            "解析时的列：%s；\n" +
                            "字段映射定义的列：%s；\n" +
                            "请先将字段映射定义的列名和个数调整为和解析时一致，再更新！", projectCode, newColumns, oldColumns));
                }
            } else if ("update".equals(loadType)) {
                // 更新的时候，解析的时候需要校验
                if ("parse-data".equals(parseType)) {
                    return Pair.of(false, String.format("Project %s 数据更新的列，和初始化定义的不一致！\n" +
                            "此次更新的列：%s；\n" +
                            "初始化定义的列：%s；\n" +
                            "请先将列名和个数调整为和初始化定义的一致，再更新！", projectCode, newColumns, oldColumns));
                }
            } else {
                log.error("checkColumns 不支持的 loadType 类型 projectCode {} parseType {} loadType {} oldColumns {} newColumns {}", projectCode, parseType, loadType, oldColumns, newColumns);
                return Pair.of(false, String.format("不支持的 loadType 类型 %s！", loadType));
            }
        }
        return Pair.of(true, "");
    }
}
