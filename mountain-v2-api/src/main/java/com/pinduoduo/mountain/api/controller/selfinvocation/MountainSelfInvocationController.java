package com.pinduoduo.mountain.api.controller.selfinvocation;

import com.pinduoduo.mountain.api.service.selfinvocation.MountainSelfInvocationService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO nezha 配置只能 mountain-v2-api 这个服务调用
 *
 * <AUTHOR>
 */
@Api("mountain 自己调用自己的相关接口")
@Slf4j
@RestController
@RequestMapping("/api/mountainSelfInvocation")
public class MountainSelfInvocationController {

    private final MountainSelfInvocationService mountainSelfInvocationService;

    public MountainSelfInvocationController(MountainSelfInvocationService mountainSelfInvocationService) {
        this.mountainSelfInvocationService = mountainSelfInvocationService;
    }

    @PostMapping("/transferDdlTask")
    @ApiOperation("接收另一个机器上的 ddl 分发任务")
    public BaseResponse<Boolean> transferDdlTask(
            @ApiParam(value = "来源机器地址", required = true) @RequestParam(value = "fromIp") String fromIp,
            @ApiParam(value = "ddl 工单 id", required = true) @RequestParam(value = "workId") Long workId
    ) {
        try {
            Boolean result = mountainSelfInvocationService.transferDdlTask(fromIp, workId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("transferDdlTask error", e);
            return BaseResponse.fail(e);
        }
    }
}
