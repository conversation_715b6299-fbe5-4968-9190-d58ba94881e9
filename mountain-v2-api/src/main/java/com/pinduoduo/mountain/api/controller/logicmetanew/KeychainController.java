package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pinduoduo.mountain.common.dto.keychain.KeychainDetailDTO;
import com.pinduoduo.mountain.common.dto.leo.AccessAppInfoDTO;
import com.pinduoduo.mountain.common.dto.leo.LeoAccessInfoDTO;
import com.pinduoduo.mountain.common.model.keychain.GenerateKeychainRulesDTO;
import com.pinduoduo.mountain.common.thirdparty.leo.LeoService;
import com.pinduoduo.mountain.common.thirdparty.leo.response.LeoBaseResponse;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.service.keychain.dto.CheckKeychainRulesDTO;
import com.pinduoduo.mountain.common.model.keychain.CheckResult;
import com.pinduoduo.mountain.service.keychain.dto.DryRunKeychainRulesDTO;
import com.pinduoduo.mountain.service.keychain.dto.KeychainMainPageDTO;
import com.pinduoduo.mountain.service.keychain.dto.ModifyKeychainParametersDTO;
import com.pinduoduo.mountain.api.model.request.keychain.ModifyKeychainBusinessReq;
import com.pinduoduo.mountain.api.model.request.keychain.ModifyKeychainOwnerReq;
import com.pinduoduo.mountain.api.model.request.keychain.ModifyKeychainParametersReq;
import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.common.thirdparty.zebrette.response.ZebretteBaseResponse;
import com.pinduoduo.mountain.service.keychain.impl.KeychainOperationService;
import com.yiran.arch.leo.util.LeoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("Keychain-related Interfaces")
@Slf4j
@RestController
@RequestMapping("/api/v2/keychain")
public class KeychainController {
    private final KeychainOperationService keychainOperationService;
    private final LeoService leoService;

    public KeychainController(KeychainOperationService keychainOperationService, LeoService leoService) {
        this.keychainOperationService = keychainOperationService;
        this.leoService = leoService;
    }

    @GetMapping("/keychainInfoList")
    @ApiOperation("获取Keychain首页列表")
    public BaseResponse<PageResult<KeychainMainPageDTO>> list(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam("Environment") @RequestParam(value = "env") String env,
            @ApiParam("Page Number") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam("Number of items per page") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("Business Line") @RequestParam(value = "business", required = false, defaultValue = "") String business,
            @ApiParam("Service") @RequestParam(value = "service", required = false, defaultValue = "") String service,
            @ApiParam("shardType") @RequestParam(value = "shardType", required = false, defaultValue = "") String shardType,
            @ApiParam("keychainType") @RequestParam(value = "keychainType", required = false, defaultValue = "") String keychainType,
            @ApiParam("Search Keyword, Fuzzy Search") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal
    ) {
        try {
            PageResult<KeychainMainPageDTO> result = keychainOperationService.keychainList(username, env, pageNum, pageSize, business,service, shardType,keychainType,searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("keychainInfoList list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/keychainInfo")
    @ApiOperation("查询 keychain 元数据")
    public BaseResponse<KeychainDetailDTO> keychainInfo(@RequestParam("keychain") String keychain) {
        try {
            KeychainDetailDTO keychainDetailDTO = keychainOperationService.getKeychainInfo(keychain);
            return BaseResponse.success(keychainDetailDTO);
        } catch (Exception e) {
            log.error("getShardInfo error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/allPrivileges")
    @ApiOperation("获取Keychain的所有账户权限")
    public BaseResponse<HashMap<String,Object>> allPrivileges(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam("keychain") @RequestParam(value = "keychain") String keychain
    ) {
        try {
            HashMap<String,Object> result = keychainOperationService.allPrivileges(keychain);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("allPrivileges error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/moreMasterKeychainCluster")
    @ApiOperation("获取多活切换Keychain的集群信息，接口先保留")
    public BaseResponse<String> moreMasterKeychainCluster(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam("keychain") @RequestParam(value = "keychain") String keychain
    ) {
        try {
            String result = keychainOperationService.getMoreMasterKeychainCluster(keychain);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("moreMasterKeychainCluster error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("修改Keychain业务线")
    @PostMapping("/modifyKeychainBusiness")
    public BaseResponse<String> modifyKeychainBusiness(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody ModifyKeychainBusinessReq modifyKeychainBusinessReq
    ) {
        try {
            ZebretteBaseResponse<Boolean> modifyResult = keychainOperationService.modifyKeychainBusiness(username,modifyKeychainBusinessReq.getKeychain(), modifyKeychainBusinessReq.getBusinessCname());
            if(modifyResult.getSuccess()){
                return BaseResponse.success("更新成功！");
            }else{
                return BaseResponse.fail("更新失败！"+modifyResult.getMessage());
            }
        } catch (Exception e) {
            log.error("modifyKeychainBusiness error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("修改KeychainOwner")
    @PostMapping("/modifyKeychainOwner")
    public BaseResponse<String> modifyKeychainOwner(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody ModifyKeychainOwnerReq modifyKeychainOwnerReq
    ) {
        try {
            ZebretteBaseResponse<Object> modifyResult = keychainOperationService.modifyKeychainOwner(username,modifyKeychainOwnerReq.getKeychain(), modifyKeychainOwnerReq.getOwnerList());
            if(modifyResult.getSuccess()){
                return BaseResponse.success("更新成功！");
            }else{
                return BaseResponse.fail("更新失败！"+modifyResult.getMessage());
            }

        } catch (Exception e) {
            log.error("modifyKeychainOwner error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("修改Keychain属性")
    @PostMapping("/modifyKeychainParameters")
    public BaseResponse<String> modifyKeychainParameters(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody ModifyKeychainParametersReq modifyKeychainParametersReq
    ) {
        try {
            ZebretteBaseResponse<ModifyKeychainParametersDTO> modifyResult = keychainOperationService.modifyKeychainParameters(username,modifyKeychainParametersReq.getKeychain(), modifyKeychainParametersReq.getParameters(), modifyKeychainParametersReq.getPoolType(), modifyKeychainParametersReq.getProperties(),modifyKeychainParametersReq.getWarmUp(),modifyKeychainParametersReq.getScanAll());
            if(modifyResult.getSuccess()){
                return BaseResponse.success("更新成功！");
            }else{
                return BaseResponse.fail("更新失败！"+modifyResult.getMessage());
            }

        } catch (Exception e) {
            log.error("modifyKeychainParameters error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("Keychain未使用巡检加白")
    @PostMapping("/whiteUnuseKeychainInspect")
    public BaseResponse<String> updateWhiteFlagByKeychain(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody String keychain
    ) {
        try {
            Boolean modifyResult = keychainOperationService.whiteUnuseKeychainInspect(keychain);
            if(modifyResult){
                return BaseResponse.success("加白成功！");
            }else{
                return BaseResponse.fail("加白失败！");
            }

        } catch (Exception e) {
            log.error("updateWhiteFlagByKeychain error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取用户所能看到的所有的 keychain")
    @GetMapping("/userRelatedKeychainList")
    public BaseResponse<List<String>> userRelatedKeychainList(
            @ApiParam(value = "nezha sso 注入的用户名") @RequestHeader(value = HEADER_GATEWAY_USERNAME, required = false) String username,
            @ApiParam(value = "搜索字段，没有搜索字段传空字符串", required = true) @RequestParam(value = "searchValue",defaultValue = "") String searchValue,
            @ApiParam(value = "环境", required = true) @RequestParam(value = "env",required = true) String env
    ) {
        try {
            List<String> result = keychainOperationService.userRelatedKeychainList(username, env,searchValue);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("businessRelatedList error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("Keychain路由规则连库/不连库校验")
    @PostMapping("/checkValidRoutingRules")
    public BaseResponse<CheckResult> checkKeychainRoutingRules(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody CheckKeychainRulesDTO checkKeychainRulesDTO
    ) {
        try {
            CheckResult checkResult = keychainOperationService.checkKeychainRoutingRules(checkKeychainRulesDTO);
            return BaseResponse.success(checkResult);
        } catch (Exception e) {
            log.error("checkKeychainRoutingRules error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("Keychain路由规则DryRun")
    @PostMapping("/dryRunRules")
    public BaseResponse<String> dryRunKeychainRoutingRules(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody DryRunKeychainRulesDTO dryRunKeychainRulesDTO
    ) {
        try {
            String dryrunResult = keychainOperationService.dryRunKeychainRoutingRules(dryRunKeychainRulesDTO);
            return BaseResponse.success(dryrunResult);
        } catch (Exception e) {
            log.error("dryRunKeychainRules error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("随机生成Keychain路由规则")
    @PostMapping("/generateRandomKeychain")
    public BaseResponse<String> generateRandomKeychainRoutingRules(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @RequestBody GenerateKeychainRulesDTO generateKeychainRulesDTO
    ) {
        try {
            String keychainRoutingRulesResult = keychainOperationService.generateRandomKeychainRoutingRules(generateKeychainRulesDTO);
            return BaseResponse.success(keychainRoutingRulesResult);
        } catch (Exception e) {
            log.error("generateRandomKeychain error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/accessAppInfo")
    @ApiOperation("Retrieve App Information for Keychain Access")
    public BaseResponse<List<AccessAppInfoDTO>> keychainAccessAppInfo(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam("Environment") @RequestParam(value = "env") String env,
            @ApiParam("keychain") @RequestParam(value = "keychain", defaultValue = "") String keychain
    ) {
        try {
            String hours = LeoUtils.getStringProperty("mountain-v2-api.leo_keychain_access_hour","24");
            String accessInfo = leoService.getAccessInfo(keychain, hours);
            LeoBaseResponse<LeoAccessInfoDTO> resp = StringUtil.jsonDeserialize(accessInfo, new TypeReference<LeoBaseResponse<LeoAccessInfoDTO>>(){});
            log.info(String.format("keychainAccessAppInfo resp: %s", resp));
            List<AccessAppInfoDTO>  accessInfos = resp.getData().getAppNames();
            return BaseResponse.success(accessInfos);
        } catch (Exception e) {
            log.error("accessAppInfo list error", e);
            return BaseResponse.fail(e);
        }
    }

}
