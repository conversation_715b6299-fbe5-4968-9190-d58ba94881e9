package com.pinduoduo.mountain.api.service.emergencycenter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pinduoduo.arch.leo.AppEnvUtil;
import com.pinduoduo.mountain.api.dto.emergencycenter.MountainWeightAndFlowConfigDTO;
import com.pinduoduo.mountain.api.dto.emergencycenter.NewFlowControlConfigUpdateRequest;
import com.pinduoduo.mountain.api.dto.emergencycenter.NewWeightAndFlowConfigDTO;
import com.pinduoduo.mountain.api.dto.emergencycenter.WeightAndFlowConfigDTO;
import com.pinduoduo.mountain.common.constant.PlatformConstant;
import com.pinduoduo.mountain.common.dto.keychain.KeychainDTO;
import com.pinduoduo.mountain.common.thirdparty.zebrette.response.ZebretteBaseResponse;
import com.pinduoduo.mountain.common.util.HttpUtil;
import com.pinduoduo.mountain.common.util.StringUtil;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.InstanceMapper;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.KeychainMapper;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FlowControlService {

    @Value("${mountain-api.zebrette_baseurl}")
    private String zebraBaseUrlProd;

    @Value("${mountain-api.zebrette_baseurl_fft}")
    private String zebraBaseUrlFFT;

    @Value("${mountain-api.zebrette_token}")
    private String token;

    private final String localEnv = AppEnvUtil.getPddEnv();
    private final List<String> testEnv = Arrays.asList("tsh","tsh1","ffttest","ctsh1");
    private final KeychainMapper keychainMapper;
    private final InstanceMapper instanceMapper;

    public FlowControlService(KeychainMapper keychainMapper, InstanceMapper instanceMapper) {
        this.keychainMapper = keychainMapper;
        this.instanceMapper = instanceMapper;
    }


    public WeightAndFlowConfigDTO getOldWeightAndFlowByKeychain(String keychain){
        String path = "/openapi/flow_control/old/weight_and_flow?leoKey=" + keychain;
        HashMap<String,String> headers = getZebraBasicHeaders(keychain);
        String baseEnvUrl = getZebraBasicUrl(keychain);

        log.info(String.format("getOldWeightAndFlowByKeychain request: %s", baseEnvUrl.replace("/api/v2","") + path));
        String respJson = HttpUtil.get(baseEnvUrl.replace("/api/v2","") + path, headers, null);
        log.info(String.format("getOldWeightAndFlowByKeychain response: %s", respJson));

        TypeReference<ZebretteBaseResponse<WeightAndFlowConfigDTO>> typeReference = new TypeReference<ZebretteBaseResponse<WeightAndFlowConfigDTO>>() {};
        ZebretteBaseResponse<WeightAndFlowConfigDTO> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("getOldWeightAndFlowByKeychain response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("getOldWeightAndFlowByKeychain Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();

    }

    public Boolean updateOldWeightAndFlow(String keychain, WeightAndFlowConfigDTO request){
        String path = "/openapi/flow_control/old/weight_and_flow?leoKey=" + keychain;

        HashMap<String,String> headers = getZebraBasicHeaders(keychain);
        String baseEnvUrl = getZebraBasicUrl(keychain);

        String requestBodyJson = StringUtil.jsonSerialize(request);
        log.info(String.format("updateOldWeightAndFlow request: %s，postBody：%s", baseEnvUrl.replace("/api/v2","") + path,requestBodyJson));
        String respJson = HttpUtil.postWithError(baseEnvUrl.replace("/api/v2","") + path, headers, requestBodyJson);

        log.info(String.format("updateOldWeightAndFlow response: %s", respJson));

        TypeReference<ZebretteBaseResponse<Boolean>> typeReference = new TypeReference<ZebretteBaseResponse<Boolean>>() {};
        ZebretteBaseResponse<Boolean> updateReslt = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!updateReslt.getSuccess()) {
            log.error("updateWeightAndFlow response error, errorCode: {}, message: {}", updateReslt.getErrorCode(), updateReslt.getMessage());
            throw new RuntimeException(String.format("updateWeightAndFlow Exception! %s", updateReslt.getMessage()));
        }
        return updateReslt.getResult();

    }

    public Boolean judgeKeychainUseNewConfig(String keychain){
        String path = "/openapi/flow_control/new/use_new_config?leoKey=" + keychain;

        HashMap<String,String> headers = getZebraBasicHeaders(keychain);
        String baseEnvUrl = getZebraBasicUrl(keychain);

        log.info(String.format("useNewConfig request: %s", baseEnvUrl.replace("/api/v2","") + path));
        String respJson = HttpUtil.get(baseEnvUrl.replace("/api/v2","") + path, headers, null);

        log.info(String.format("useNewConfig response: %s", respJson));

        TypeReference<ZebretteBaseResponse<Boolean>> typeReference = new TypeReference<ZebretteBaseResponse<Boolean>>() {};
        ZebretteBaseResponse<Boolean> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("useNewConfig response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("useNewConfig Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();

    }

    public NewWeightAndFlowConfigDTO getNewWeightAndFlowByKeychain(String keychain){
        String path = "/openapi/flow_control/new/get_latest_config?leoKey=" + keychain;

        HashMap<String,String> headers = getZebraBasicHeaders(keychain);
        String baseEnvUrl = getZebraBasicUrl(keychain);

        log.info(String.format("getNewWeightAndFlowByKeychain request: %s", baseEnvUrl.replace("/api/v2","") + path));
        String respJson = HttpUtil.get(baseEnvUrl.replace("/api/v2","") + path, headers, null);
        log.info(String.format("getNewWeightAndFlowByKeychain response: %s", respJson));

        TypeReference<ZebretteBaseResponse<NewWeightAndFlowConfigDTO>> typeReference = new TypeReference<ZebretteBaseResponse<NewWeightAndFlowConfigDTO>>() {};
        ZebretteBaseResponse<NewWeightAndFlowConfigDTO> configResult = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!configResult.getSuccess()) {
            log.error("getNewWeightAndFlowByKeychain response error, errorCode: {}, message: {}", configResult.getErrorCode(), configResult.getMessage());
            throw new RuntimeException(String.format("getNewWeightAndFlowByKeychain Exception! %s", configResult.getMessage()));
        }
        return configResult.getResult();
    }

    public Boolean updateNewWeightAndFlow(NewFlowControlConfigUpdateRequest request){
        String path = "/openapi/flow_control/new/update";

        HashMap<String,String> headers = getZebraBasicHeaders(request.getLeoKey());
        String baseEnvUrl = getZebraBasicUrl(request.getLeoKey());

        String requestBodyJson = StringUtil.jsonSerialize(request);
        log.info(String.format("updateNewWeightAndFlow request: %s，postBody：%s", baseEnvUrl.replace("/api/v2","") + path,requestBodyJson));
        String respJson = HttpUtil.postWithError(baseEnvUrl.replace("/api/v2","") + path, headers, requestBodyJson);

        log.info(String.format("updateNewWeightAndFlow response: %s", respJson));

        TypeReference<ZebretteBaseResponse<Boolean>> typeReference = new TypeReference<ZebretteBaseResponse<Boolean>>() {};
        ZebretteBaseResponse<Boolean> updateReslt = StringUtil.jsonDeserialize(respJson, typeReference);

        if (!updateReslt.getSuccess()) {
            log.error("updateNewWeightAndFlow response error, errorCode: {}, message: {}", updateReslt.getErrorCode(), updateReslt.getMessage());
            throw new RuntimeException(String.format("updateNewWeightAndFlow Exception! %s", updateReslt.getMessage()));
        }
        return updateReslt.getResult();

    }


    public MountainWeightAndFlowConfigDTO getWeightAndFlowByKeychain(String keychain){
        MountainWeightAndFlowConfigDTO result = new MountainWeightAndFlowConfigDTO();
        result.setKeychain(keychain);

        Boolean useNewConfigFlag = judgeKeychainUseNewConfig(keychain);
        log.info(String.format("getWeightAndFlowByKeychain useNewConfigFlag: %s", useNewConfigFlag));
        if (!useNewConfigFlag){
            WeightAndFlowConfigDTO zebraResult = getOldWeightAndFlowByKeychain(keychain);
            // 这里不能使用：BeanUtils.copyProperties(result,zebraResult);因为：
            // 这只会把 zebraResult.getWeightAndFlowConfigs() 的 【引用】 拷贝到 result.setWeightAndFlowConfigs(...)。
            // BeanUtils.copyProperties 并不会自动转换 List 中元素的类型！

            List<MountainWeightAndFlowConfigDTO.WeightAndFlowConfig> convertedList = zebraResult.getWeightAndFlowConfigs().stream()
                    .map(zItem -> {
                        MountainWeightAndFlowConfigDTO.WeightAndFlowConfig mItem = new MountainWeightAndFlowConfigDTO.WeightAndFlowConfig();
                        try {
                            BeanUtils.copyProperties(mItem, zItem);
                        } catch (Exception e) {
                            throw new RuntimeException("转换 config 失败", e);
                        }
                        return mItem;
                    })
                    .collect(Collectors.toList());
            result.setWeightAndFlowConfigs(convertedList);
            result.setDynamicWeightAndFlowControlOnlyWorksOnWhitelist(zebraResult.isDynamicWeightAndFlowControlOnlyWorksOnWhitelist());

            log.info(String.format("粗粒度转化成Mountain的对象为：%s", result));
            HashMap<String,String> ipPortRoleMap = new HashMap<>();
            HashMap<String,String> ipPortSetMap = new HashMap<>(); //有时候Zebra传过来的数据set是空的，我们自己去查一下
            HashMap<String,Integer> ipPortNumMap = new HashMap<>();
            result.getWeightAndFlowConfigs().forEach(item->{
                // 将Zebra返回的结果转化为Mountain页面需要的结果
                if (ipPortRoleMap.containsKey(item.getIp())){
                    item.setRole(ipPortRoleMap.get(item.getIp()));
                    item.setInstanceNum(ipPortNumMap.get(item.getIp()));
                }else{
                    String ip = item.getIp().split(":")[0];
                    int port = Integer.parseInt(item.getIp().split(":")[1]);
                    List<Instance> instanceList = instanceMapper.selectAllByIpPort(ip,port);
                    Instance instance = instanceList.get(0);
                    item.setRole(instance.getRole());
                    item.setInstanceNum(instanceList.size());
                    ipPortSetMap.put(item.getIp(),instance.getSet());
                    ipPortRoleMap.put(item.getIp(),instance.getRole());
                    ipPortNumMap.put(item.getIp(),instanceList.size());

                }
                item.setSet(ipPortSetMap.get(item.getIp()));
                item.setPddSet(ipPortSetMap.get(item.getIp()));
                if(item.getInitialWeight()!= item.getEffectiveWeight()){
                    item.setModified(true);
                }
            });
            result.getWeightAndFlowConfigs().removeIf(item->"write".equals(item.getType()));//把Write的过滤一下

        }else{
            final List<MountainWeightAndFlowConfigDTO.WeightAndFlowConfig> mountainConfigs = result.getWeightAndFlowConfigs();
            NewWeightAndFlowConfigDTO zebraResult = getNewWeightAndFlowByKeychain(keychain);
            final NewWeightAndFlowConfigDTO.ControlConfigVO controlConfigVO = zebraResult.getControlConfigVoList().get(0);

            if(controlConfigVO.getShowType()==2){
                // showType=1,普通细粒度；showType=2，特殊细粒度。showType=2时指的是多活Keychain，不允许修改流量配比。
                throw new RuntimeException("不允许对多活Keychain进行流量配比！有疑问请联系DBA进行人工评估和调整。");
            }

            final List<String> groupDataSourceNameSet = controlConfigVO.getGroupDataSourceNameSet();
            final List<String> dbSets = controlConfigVO.getDbSets();
            final Map<String, NewWeightAndFlowConfigDTO.SubDbConfig> serviceDefaullConfig = controlConfigVO.getServiceFlowControlConfigMap().get("服务默认配置").getSubDbConfigMap();

            HashMap<String,String> ipPortRoleMap = new HashMap<>();
            HashMap<String,Integer> ipPortNumMap = new HashMap<>();

            groupDataSourceNameSet.forEach(dbName->{
                if(dbName.equals("分库默认配置")){
                    return;
                }
                final Map<String, NewWeightAndFlowConfigDTO.DbSetFlowControlConfigVO> dbSetFlowControlConfigVoMap = serviceDefaullConfig.get(dbName).getDbSetFlowControlConfigVoMap();
                /*
                新版流量配比的数据结构形如：
                 "sicilia_7": {
                        "subDb": "sicilia_7",
                        "modified": false,
                        "dbSetFlowControlConfigVoMap": {
                            "sh2": {
                                "dbSet": "sh2",
                                "modified": false,
                                "weight": 1,
                                "dsIdMap": {
                                    "sicilia_7.read1": 1,
                                    "sicilia_7.read2": 1,
                                    "sicilia_7.read3": 1
                                },
                                "dsIdAndIpMap": {
                                    "sicilia_7.read1": "*************:3306",
                                    "sicilia_7.read2": "*************:3306",
                                    "sicilia_7.read3": "************:3306"
                                }
                            }
                        }
                    },
                 * */
                for (String setName : dbSets) {
                    final NewWeightAndFlowConfigDTO.DbSetFlowControlConfigVO dbSetFlowControlConfigVO = dbSetFlowControlConfigVoMap.get(setName);
                    // 多活Keychain的话不用关心流量配比，河伯那边是另一个配置，优先级比流量配比高。非多活Keychain就一个区，不用关心这个区的weight是多少。但是Zebra大佬建议，为了以防万一线上有问题，如果有改过Set的这个值，先拦截，找Zebra大佬手动处理。
                    if (controlConfigVO.getShowType()==2 && (dbSetFlowControlConfigVO.getModified()||dbSetFlowControlConfigVO.getWeight()!=1)){
                        throw  new RuntimeException(String.format("多活Keychain的分库：%s 所属区：%s的权重被改动过，Mountain管控不允许修改，请联系DBA人工评估和调整。", dbName,setName));
                    }
                    final Map<String, String> dsIdAndIpMap = dbSetFlowControlConfigVO.getDsIdAndIpMap();
                    dsIdAndIpMap.forEach((dsId,ipPort)->{
                        MountainWeightAndFlowConfigDTO.WeightAndFlowConfig item = new MountainWeightAndFlowConfigDTO.WeightAndFlowConfig();
                        item.setSn(Integer.parseInt(dsId.split("\\.")[1].replace("read","")));
                        if(controlConfigVO.getDataSourceType().equals("group")){
                            item.setDbName(dsId.split("\\.")[0]);
                        }else{
                            item.setDbName(dbName);
                        }
                        item.setDatabaseName(dsId);
                        item.setActualDbName(dbName);
                        item.setType("read");
                        item.setIp(ipPort);
                        if (ipPortRoleMap.containsKey(ipPort)){
                            item.setRole(ipPortRoleMap.get(ipPort));
                            item.setInstanceNum(ipPortNumMap.get(ipPort));
                        }else{
                            String ip = ipPort.split(":")[0];
                            int port = Integer.parseInt(ipPort.split(":")[1]);
                            List<Instance> instanceList = instanceMapper.selectAllByIpPort(ip,port);
                            Instance instance = instanceList.get(0);
                            item.setRole(instance.getRole());
                            item.setInstanceNum(instanceList.size());
                            ipPortRoleMap.put(ipPort,instance.getRole());
                            ipPortNumMap.put(ipPort,instanceList.size());
                        }
                        item.setEffectiveWeight(dbSetFlowControlConfigVO.getDsIdMap().getOrDefault(dsId,1));//没配的默认给1
                        item.setSet(setName);
                        item.setWriteConnection(false);
                        item.setReadConnection(true);
                        item.setPddSet(setName);
                        mountainConfigs.add(item);
                    });
                }
            });
            // 排序：先 dbName 再 sn
            result.getWeightAndFlowConfigs().sort(
                    Comparator.comparing(MountainWeightAndFlowConfigDTO.WeightAndFlowConfig::getActualDbName)
                            .thenComparing(MountainWeightAndFlowConfigDTO.WeightAndFlowConfig::getSn)
            );
            result.setWeightAndFlowConfigs(mountainConfigs);
            result.setUseNewConfigFlag(true);

        }
        log.info(String.format("getWeightAndFlowByKeychain 的结果为：%s.", result));
        return result;
    }

    /*

    测试： 多活Keychain：zebrette-auth-center.htj_zebra-benchmark_storage-infra_multiset_wide_test_0_dal_master-slave_shard.3a8fcaed
    旧版shard：zebrette-auth-center.htj_neumann-api_neumann-slave_neumann-task_order_neumann_shard_0_dal_master-slave_shard.303c162a
    旧版单实例：zebrette-auth-center.htj_cmdb_jialuo_amd_sb_test_op_dal_master-slave_normal.c6e2d112
    新版单实例：zebrette-auth-center.htj_cmdb_jialuo_aaaaaa_op_dal_master-slave_normal.9f6e07e4
    新版shard：zebrette-auth-center.htj_cmdb_jialuo_op_user_db_0_dal_master-slave_shard.a1d21296
    * */


    public Boolean updateWeightAndFlow(MountainWeightAndFlowConfigDTO mountainDTO){

        Boolean result = null;
        String keychain = mountainDTO.getKeychain();

        // 接口级别判断一下Keychain是不是多活Keychain，多活Keychain不允许修改读流量配比规则
        final KeychainDTO keychainDTO = keychainMapper.selectByKeychain(keychain);
        if(keychainDTO.getKeychainType().equals("moreMaster")){
            throw new RuntimeException("不允许对多活Keychain进行流量配比！有疑问请联系DBA进行人工评估和调整。");
        }

        final Boolean allZeroResult = checkEffectiveWeightIsAllZero(mountainDTO);
        if(!allZeroResult){
            throw new RuntimeException("检测到有库存在读流量配比全为0的情况，不允许这样修改，请检查后重试！");
        }

        boolean useNewConfigFalg = mountainDTO.isUseNewConfigFlag();
        if(!useNewConfigFalg){
            //如果用的是旧版
            log.info(String.format("[updateWeightAndFlow]Keychian：%s 将走旧版更新逻辑！", keychain));
            WeightAndFlowConfigDTO zebraRequest = new WeightAndFlowConfigDTO();
            List<WeightAndFlowConfigDTO.WeightAndFlowConfig> convertedList = mountainDTO.getWeightAndFlowConfigs().stream()
                    .map(mItem -> {
                        WeightAndFlowConfigDTO.WeightAndFlowConfig zItem = new WeightAndFlowConfigDTO.WeightAndFlowConfig();
                        try {
                            BeanUtils.copyProperties(zItem, mItem);//目标在前，源在后
                        } catch (Exception e) {
                            throw new RuntimeException("Mountain转换Zebra config 失败", e);
                        }
                        return zItem;
                    })
                    .collect(Collectors.toList());
            zebraRequest.setWeightAndFlowConfigs(convertedList);
            zebraRequest.setDynamicWeightAndFlowControlOnlyWorksOnWhitelist(mountainDTO.isDynamicWeightAndFlowControlOnlyWorksOnWhitelist());
            result = updateOldWeightAndFlow(keychain,zebraRequest);
            return result;

        }else{
            // 如果Keychain走的是新版更新逻辑
            log.info(String.format("[updateWeightAndFlow]Keychain: %s 将走新版更新逻辑！", keychain));

            NewWeightAndFlowConfigDTO zebraOriResult = getNewWeightAndFlowByKeychain(keychain);
            NewWeightAndFlowConfigDTO zebraResult = getNewWeightAndFlowByKeychain(keychain);
            final NewWeightAndFlowConfigDTO.ControlConfigVO controlConfigVO = zebraResult.getControlConfigVoList().get(0);
            final Map<String, NewWeightAndFlowConfigDTO.SubDbConfig> serviceDefaullConfig = controlConfigVO.getServiceFlowControlConfigMap().get("服务默认配置").getSubDbConfigMap();
            final NewWeightAndFlowConfigDTO.SubDbConfig defaultDbOriConfig = serviceDefaullConfig.get("分库默认配置");

            NewFlowControlConfigUpdateRequest zebraRequest = new NewFlowControlConfigUpdateRequest();
            zebraRequest.setLeoKey(keychain);
            NewWeightAndFlowConfigDTO.ServiceFlowControlConfig serviceFlowControlConfig = new NewWeightAndFlowConfigDTO.ServiceFlowControlConfig();

            // 先获取一下分库和集群的map,如：{"sicilia_7":["sh2","sh5"],"sicilia_8":["sh2","sh5"]}， 不过由于多活集群不允许修改读流量配比，所以列表里面只会有一个值
            Map<String,List<String>> dbNameSetNameMap = new HashMap<>();
            mountainDTO.getWeightAndFlowConfigs().forEach(item -> {
                String dbName = item.getActualDbName();
                String setName = item.getSet();

                List<String> sets = dbNameSetNameMap.computeIfAbsent(dbName, k -> new ArrayList<>());
                if (!sets.contains(setName)) {
                    sets.add(setName);
                }
            });

            serviceFlowControlConfig.setServiceName("默认服务配置");


            Set<String> modifiedDbNameList = dbNameSetNameMap.keySet();
            Map<String, NewWeightAndFlowConfigDTO.SubDbConfig> subDbConfigMap = new HashMap<>();
            subDbConfigMap.put("分库默认配置",defaultDbOriConfig);
            for (String db : modifiedDbNameList) {
                NewWeightAndFlowConfigDTO.SubDbConfig subDbConfig = new NewWeightAndFlowConfigDTO.SubDbConfig();
                subDbConfig.setSubDb(db);
                subDbConfig.setModified(true);

                Map<String, NewWeightAndFlowConfigDTO.DbSetFlowControlConfigVO> dbSetFlowControlConfigVoMap = new HashMap<>();

                List<String> setList = dbNameSetNameMap.get(db);
                for (String setName : setList) {
                    NewWeightAndFlowConfigDTO.DbSetFlowControlConfigVO dbConfig= new NewWeightAndFlowConfigDTO.DbSetFlowControlConfigVO();
                    dbConfig.setDbSet(setName);
                    dbConfig.setModified(true);
                    dbConfig.setWeight(serviceDefaullConfig.get(db).getDbSetFlowControlConfigVoMap().get(setName).getWeight());

                    Map<String,Integer> dsIdMap = new HashMap<>();
                    Map<String,String> dsIdAndIpMap = new HashMap<>();
                    mountainDTO.getWeightAndFlowConfigs().forEach(item ->{
                       if (item.getActualDbName().equals(db)){
                           dsIdMap.putIfAbsent(item.getDatabaseName(),item.getEffectiveWeight());
                           dsIdAndIpMap.putIfAbsent(item.getDatabaseName(),item.getIp());
                       }
                    });
                    dbConfig.setDsIdMap(dsIdMap);
                    dbConfig.setDsIdAndIpMap(dsIdAndIpMap);

                    dbSetFlowControlConfigVoMap.putIfAbsent(setName,dbConfig);
                }

                subDbConfig.setDbSetFlowControlConfigVoMap(dbSetFlowControlConfigVoMap);
                subDbConfigMap.putIfAbsent(db,subDbConfig);
            }

            serviceFlowControlConfig.setSubDbConfigMap(subDbConfigMap);

            HashMap<String, NewWeightAndFlowConfigDTO.ServiceFlowControlConfig> defaultMap = new HashMap<>();
            defaultMap.put("服务默认配置",serviceFlowControlConfig);
            zebraRequest.setServiceFlowControlConfigMap(defaultMap);
            result = updateNewWeightAndFlow(zebraRequest);
            return result;
        }
    }

    public String getZebraBasicUrl (String keychain){

        // Zebra的测试和线上的Keychain都存在线上环境，他们的胡桃街环境和线上环境是两个db，相当于和Mountain的现状一模一样。所以，我们Mountain的不管是主站线上还是主站胡桃街环境，都应该调Zebra的prod.srv.pdd.net环境，本地开发才调Zebra的testing.srv.pdd.net
        String baseEnvUrl = zebraBaseUrlProd;
        KeychainDTO keychainDTO = keychainMapper.selectByKeychain(keychain);
        String keychainEnv = keychainDTO.getEnv();

        if(keychainEnv.startsWith("fft")){
            baseEnvUrl = this.zebraBaseUrlFFT;
        }
        return baseEnvUrl;
    }

    public HashMap<String, String> getZebraBasicHeaders (String keychain){
        HashMap<String, String> headers = new HashMap<>();
        // 如果是本地调试，需要把headers加上，htj走srv域名 网关会带上认证消息
        if(localEnv.equals(PlatformConstant.ENV_DEV)||localEnv.equals(PlatformConstant.ENV_TESTING)){
            headers.put("appid", "mountain-api");
        }
        String hostGroup = System.getenv("CANARY_HOST_GROUP_NAME");
        if (hostGroup != null && hostGroup.contains("staging")) {
            headers.put("x-canary-staging","staging");
        }
        return headers;
    }

    public Boolean checkEffectiveWeightIsAllZero(MountainWeightAndFlowConfigDTO mountainDTO){
        List<MountainWeightAndFlowConfigDTO.WeightAndFlowConfig> configList = mountainDTO.getWeightAndFlowConfigs();
        /*
        groupingBy(...)：先按 actualDbName 分组
        values().stream()：拿到每个库对应的配置列表
        group.stream().allMatch(...)：判断这个库的所有权重是否都为 0
        noneMatch(...)：判断有没有这样的“全为 0”的库
        * */
        boolean result = configList.stream()
                .collect(Collectors.groupingBy(MountainWeightAndFlowConfigDTO.WeightAndFlowConfig::getActualDbName))
                .values().stream()
                .noneMatch(group -> group.stream().allMatch(c -> c.getEffectiveWeight() == 0));
        return result;
    }
}
