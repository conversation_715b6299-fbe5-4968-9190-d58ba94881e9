package com.pinduoduo.mountain.api.controller.logicmetanew;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.service.metadata.dto.ClusterBaseInfoDTO;
import com.pinduoduo.mountain.service.metadata.dto.ClusterTopologyDTO;
import com.pinduoduo.mountain.service.metadata.dto.InstanceNewBaseInfoDTO;
import com.pinduoduo.mountain.service.metadata.dto.PhysicalClusterInfoDTO;
import com.pinduoduo.mountain.service.metadata.impl.ClusterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

/**
 * <AUTHOR>
 */
@Api("新逻辑库-集群相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v2/cluster")
public class ClusterController {

    private final ClusterService clusterService;

    public ClusterController(ClusterService clusterService) {
        this.clusterService = clusterService;
    }

    @GetMapping("/list")
    @ApiOperation("获取逻辑库下的集群列表")
    public BaseResponse<List<ClusterBaseInfoDTO>> list(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId
    ) {
        try {
            List<ClusterBaseInfoDTO> result = clusterService.list(username, logicDbId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("list error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/clusterTopology")
    @ApiOperation("集群的拓扑图")
    public BaseResponse<ClusterTopologyDTO> clusterTopology(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId,
            @ApiParam(value = "集群 ID", required = true) @RequestParam("clusterId") long clusterId
    ) {
        try {
            ClusterTopologyDTO result = clusterService.clusterTopology(username, logicDbId, clusterId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("clusterTopology error", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/physicalClusterList")
    @ApiOperation("集群下属的物理集群列表")
    public BaseResponse<List<PhysicalClusterInfoDTO>> physicalClusterList(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "逻辑库 ID", required = true) @RequestParam("logicDbId") long logicDbId,
            @ApiParam(value = "集群 ID", required = true) @RequestParam("clusterId") long clusterId
    ) {
        try {
            List<PhysicalClusterInfoDTO> result = clusterService.physicalClusterList(username, logicDbId, clusterId);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("physicalClusterList error", e);
            return BaseResponse.fail(e);
        }
    }

    /**
     * 搜索集群内的主实例
     * 1.instance_id（等值匹配）
     * 2.ip:port（等值匹配）
     * 3.ip（前缀匹配）
     * 4.instance_name（模糊匹配）
     *
     * @param username  用户名
     * @param searchVal 搜索关键字
     * @return BaseResponse<List < InstanceNewBaseInfoDTO>>
     */
    @ApiOperation("搜索主实例信息")
    @GetMapping("/searchMasterInstance")
    public BaseResponse<List<InstanceNewBaseInfoDTO>> searchMasterInstance(
            @RequestHeader(HEADER_GATEWAY_USERNAME) String username,
            @ApiParam(value = "集群 id", required = true) @RequestParam(value = "clusterId") long clusterId,
            @ApiParam(value = "搜索关键字") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal
    ) {
        try {
            List<InstanceNewBaseInfoDTO> result = clusterService.searchMasterInstance(username, clusterId, searchVal);
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("searchMasterInstance error", e);
            return BaseResponse.fail(e);
        }
    }
}
