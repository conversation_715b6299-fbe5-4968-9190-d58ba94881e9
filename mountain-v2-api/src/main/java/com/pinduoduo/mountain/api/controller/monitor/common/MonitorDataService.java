package com.pinduoduo.mountain.api.controller.monitor.common;

import com.pinduoduo.arch.egret.contract.dto.MetricDataDto;
import com.pinduoduo.mountain.api.controller.monitor.MetricData;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;

public interface MonitorDataService {
    /**
     * @param metric：       指标列表
     * @param startTime:    开始时间
     * @param endTime，结束时间， 如果开始、结束时间都为空，则表示查询当前最新值，否则表示时间范围查询
     * @return 数据点位
     **/
    MetricData getDataPoints(String ns,
                             String metric,
                             List<Triple<String, String, List<String>>> tags,
                             List<String> levels,
                             List<String> fieldKeys,
                             List<String> groupBy,
                             Integer granularity,
                             Long startTime,
                             Long endTime, String instanceEnv, String instanceType);


    MetricData getRT(String dbId, String start, String end);

    List<List<MetricDataDto>> getData(String ns, String metric, String tagKey, String tagValue, String fieldKey);
}
