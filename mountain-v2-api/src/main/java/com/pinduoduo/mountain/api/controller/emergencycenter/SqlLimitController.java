package com.pinduoduo.mountain.api.controller.emergencycenter;

import com.pinduoduo.mountain.api.dto.emergencycenter.SQLFlowLimitConfig;
import com.pinduoduo.mountain.api.model.request.emergencycenter.KeychainReq;
import com.pinduoduo.mountain.api.model.request.emergencycenter.KeychainFlowLimitConfigReq;
import com.pinduoduo.mountain.api.model.request.emergencycenter.OnlySqlStringReq;
import com.pinduoduo.mountain.api.service.emergencycenter.SqlLimitService;
import com.pinduoduo.mountain.common.model.BaseResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/api/v2/sqlLimit")
public class SqlLimitController {
    private final SqlLimitService sqlLimitService;


    public SqlLimitController(SqlLimitService sqlLimitService) {
        this.sqlLimitService = sqlLimitService;
    }


    @ApiOperation("获取当前Keychain的所有限流规则")
    @GetMapping("/getAllLimitRules")
    public BaseResponse<List<SQLFlowLimitConfig>> getAllLimitRules(
            @ApiParam(value = "Keychain名", required = true) @RequestParam(value = "keychain") String keychain
    ) {
        try {
            List<SQLFlowLimitConfig> result = sqlLimitService.getAllSqlLimitRulesByKeychain(keychain);
            log.info(String.format("SQL限流规则为：%s", result));
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getAllLimitRules error", e);
            return BaseResponse.fail(e);
        }
    }

    @ApiOperation("获取sqlId")
    @PostMapping(value = "/getSqlIdV2")
    public BaseResponse<String> calculateSqlIdV2(
            @ApiParam(value = "完整sql语句,形式必须是sql:'xxx'的形式", required = true) @Validated @RequestBody Map<String, String> requestBody) {
        try {
            String sql = requestBody.get("sql");
            if(sql==null || sql.isEmpty()){
                return BaseResponse.fail("参数传入失败！必须是{\"sql\":\"xxx\"}的形式");
            }
            return BaseResponse.success(this.sqlLimitService.calculateSqlIdV2(sql));
        } catch (Exception e) {
            log.error("calculateSqlIdV2 error,", e);
            return BaseResponse.fail("计算v2版本的sqlId信息失败", e);
        }
    }

    @ApiOperation("获取sqlId和参数化后的SQL")
    @PostMapping(value = "/getSqlIdV2andPreparingSql")
    public BaseResponse<HashMap<String,String>> getSqlIdV2andPreparingSql(
            @ApiParam(value = "sql语句", required = true) @Validated @RequestBody OnlySqlStringReq sqlStringReq) {
        try {
            return BaseResponse.success(this.sqlLimitService.calculateSqlIdV2andPreparingSql(sqlStringReq.getSqlString()));
        } catch (Exception e) {
            log.error("getSqlIdV2andPreparingSql error,", e);
            return BaseResponse.fail("获取sqlId和参数化后的SQL失败", e);
        }
    }

    @ApiOperation("新增或更新一条限流规则")
    @PostMapping(value = "/addOrUpdateFlowLimit")
    public BaseResponse<List<SQLFlowLimitConfig>> addOrUpdateFlowLimit(
            @ApiParam(value = "增加或更新Keychain限流规则请求体", required = true) @Validated @RequestBody KeychainFlowLimitConfigReq requestBody) {
        try {
            return BaseResponse.success(this.sqlLimitService.addOrUpdateKeychainSqlLimitRule(requestBody));
        } catch (Exception e) {
            log.error("addOrUpdateFlowLimit error,", e);
            return BaseResponse.fail("增加或更新限流规则出错！", e);
        }
    }

    @ApiOperation("删除限流规则")
    @PostMapping(value = "/deleteFlowLimit")
    public BaseResponse<List<SQLFlowLimitConfig>> deleteFlowLimit(
            @ApiParam(value = "删除Keychain限流规则请求体", required = true) @Validated @RequestBody KeychainFlowLimitConfigReq requestBody) {
        try {
            return BaseResponse.success(this.sqlLimitService.deleteKeychainSqlLimitRule(requestBody));
        } catch (Exception e) {
            log.error("deleteFlowLimit error,", e);
            return BaseResponse.fail("删除限流规则出错！", e);
        }
    }

    @ApiOperation("一键关闭指定keychain所有规则")
    @PostMapping(value = "/disableAllFlowLimit")
    public BaseResponse<Boolean> disableAllFlowLimit(
            @ApiParam(value = "一键关闭指定keychain所有规则请求体", required = true) @Validated @RequestBody KeychainReq requestBody) {
        try {
            return BaseResponse.success(this.sqlLimitService.disableAllKeychainSqlLimitRule(requestBody));
        } catch (Exception e) {
            log.error("disableAllFlowLimit error,", e);
            return BaseResponse.fail("一键关闭指定keychain所有规则出错！", e);
        }
    }

    @ApiOperation("获取输入SQL字符串的Where条件列")
    @PostMapping(value = "/getSQLWhereFields")
    public BaseResponse<List<String>> getSQLWhereFields(
            @ApiParam(value = "获取SQL的Where条件列", required = true) @Validated @RequestBody OnlySqlStringReq sqlString) {
        try {
            return BaseResponse.success(this.sqlLimitService.getSQLWhereFields(sqlString.getSqlString()));
        } catch (Exception e) {
            log.error("getSQLWhereFields error,", e);
            return BaseResponse.fail("获取SQL字符串的Where条件列出错！", e);
        }
    }

    @ApiOperation("获取当前Keychain的所有物理库表键值对")
    @GetMapping("/getAllPhysicalTableByKeychain")
    public BaseResponse<HashMap<String, List<String>>> getAllPhysicalTableByKeychain(
            @ApiParam(value = "Keychain名", required = true) @RequestParam(value = "keychain") String keychain
    ) {
        try {
            HashMap<String, List<String>> result = sqlLimitService.getAllPhysicalDbAndTablesByKeychain(keychain);
            log.info(String.format("getAllPhysicalTableByKeychain的结果为：%s", result));
            return BaseResponse.success(result);
        } catch (Exception e) {
            log.error("getAllPhysicalTableByKeychain error", e);
            return BaseResponse.fail(e);
        }
    }



}
