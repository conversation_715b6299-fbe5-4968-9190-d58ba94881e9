package com.pinduoduo.mountain.api.controller.systemconfig;

import com.pinduoduo.mountain.common.model.BaseResponse;
import com.pinduoduo.mountain.common.model.PageResult;
import com.pinduoduo.mountain.service.systemconfig.dto.ConfigModifyTaskDTO;
import com.pinduoduo.mountain.service.systemconfig.dto.SystemConfigInfoDTO;
import com.pinduoduo.mountain.service.systemconfig.dto.SystemConfigOperationDTO;
import com.pinduoduo.mountain.service.systemconfig.dto.SystemConfigTaskSubmitResultDTO;
import com.pinduoduo.mountain.service.systemconfig.impl.SystemConfigService;
import com.pinduoduo.mountain.service.systemconfig.request.SystemConfigTaskExecuteReq;
import com.pinduoduo.mountain.service.systemconfig.request.SystemConfigTaskReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import java.util.List;

import static com.pinduoduo.mountain.common.constant.Http.HEADER_GATEWAY_USERNAME;

@Api("System Config")
@Slf4j
@RestController
@RequestMapping("/api/v2/system/config")
public class SystemConfigController {

    private final SystemConfigService systemConfigService;

    public SystemConfigController(SystemConfigService systemConfigService) {
        this.systemConfigService = systemConfigService;
    }

    @GetMapping("/list")
    @ApiOperation("retrieve system config list")
    public BaseResponse<PageResult<SystemConfigInfoDTO>> list(@ApiParam(value = "search val") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
                                                        @ApiParam(value = "page") @Min(value = 1, message = "page") @RequestParam(value = "page", required = false, defaultValue = "1") @Validated Integer page,
                                                        @ApiParam(value = "page size") @Min(value = 0, message = "page size") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        try {
            PageResult<SystemConfigInfoDTO> systemConfigList = this.systemConfigService.getSystemConfigList(searchVal,page,pageSize);
            return BaseResponse.success(systemConfigList);
        } catch (Exception e) {
            log.error("/api/v2/system/config/list error: ", e);
            return BaseResponse.fail(e);
        }
    }

    @GetMapping("/task/list")
    @ApiOperation("retrieve system task list")
    public BaseResponse<PageResult<ConfigModifyTaskDTO>> retrieveTaskList(@ApiParam(value = "searchVal") @RequestParam(value = "searchVal", required = false, defaultValue = "") String searchVal,
                                                 @ApiParam(value = "page") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                 @ApiParam(value = "page size") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize){
        PageResult<ConfigModifyTaskDTO> modifyTask = this.systemConfigService.getConfigModifyTask(searchVal, page, pageSize);
        return BaseResponse.success(modifyTask);
    }


    @PostMapping("/execute")
    @ApiOperation("execute system config task")
    public BaseResponse<List<SystemConfigOperationDTO>> executeTask(@ApiParam(value = "system config task group id", required = true) @Validated @RequestBody SystemConfigTaskExecuteReq req) {
        List<SystemConfigOperationDTO> dtos = this.systemConfigService.executeTask(req.getTaskGroupId());
        boolean success = true;
        StringBuilder errorMsg = new StringBuilder();
        for(SystemConfigOperationDTO dto : dtos) {
            if (!dto.getResult()){
                success = false;
                errorMsg.append(dto.getMessage()).append("\n");
            }
        }
        if (dtos.isEmpty()) {
            return BaseResponse.fail("Already executed system config task");
        }
        if (success){
            return BaseResponse.success(dtos);
        }
        return BaseResponse.fail(errorMsg.toString());
    }

    /**
     * submit task for system config
     *
     * @return
     */
    @PostMapping("/submit")
    @ApiOperation("submit system config task")
    public BaseResponse<SystemConfigTaskSubmitResultDTO> submit(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,@ApiParam(value = "system config task body", required = true) @Validated @RequestBody SystemConfigTaskReq req) {
        SystemConfigTaskSubmitResultDTO resultDTO = this.systemConfigService.submitTask(username, req);
        if(resultDTO.getResult()){
            return BaseResponse.success(resultDTO);
        }
        return BaseResponse.fail(resultDTO.getMessage());
    }


    @DeleteMapping("/{taskId}/remove")
    @ApiOperation("remove system config task by taskId, not taskGroupId")
    public BaseResponse<ConfigModifyTaskDTO> remove(@RequestHeader(HEADER_GATEWAY_USERNAME) String username,@ApiParam(value = "system config task body", required = true) @PathVariable("taskId") Long taskId) {
        log.info("{} remove system config task, id: {}", username,taskId);
        ConfigModifyTaskDTO dto = this.systemConfigService.removeTask(taskId);
        if (dto == null){
            return  BaseResponse.fail("not  found system config task "+taskId);
        }
        return BaseResponse.success(dto);
    }

}
