package com.pinduoduo.mountain.api.controller.dashboard.service;

import com.pinduoduo.mountain.common.model.cmdb.CmdbBusinessDTO;
import com.pinduoduo.mountain.common.thirdparty.cmdb.CmdbService;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.LogicDatabaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class UserBizServiceImpl implements UserBizService {
    @Autowired
    private LogicDatabaseMapper logicDatabaseMapper;
    @Autowired
    private CmdbService cmdbService;

    /**
     * 获取用户相关的业务线
     */
    @Override
    public List<CmdbBusinessDTO> getBiz(String user) {
        return cmdbService.getRelatedBusinessListByUser(user);
    }

    @Override
    public List<String> getAccountBiz(String user) {
        if (StringUtils.isEmpty(user)) {
            return Collections.emptyList();
        }
        return logicDatabaseMapper.getBiz(user);
    }
}