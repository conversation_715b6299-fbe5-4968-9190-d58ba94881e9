package com.pinduoduo.mountain.api.controller.monitor.service;

import com.pinduoduo.mountain.common.constant.Time;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ApiModel("监控查询请求体")
public class MonitorParam {
    @ApiModelProperty(value = "查询指标，只对 /egret/queryDataPoints 接口有效", required = true)
    private String metric;
    @ApiModelProperty(value = "开始时间", required = true)
    private String start;
    @ApiModelProperty(value = "结束时间", required = true)
    private String end;
    @ApiModelProperty(value = "物理机监控查询时只能传一个实例", required = true)
    private List<Map<String, String>> cdbid;
    @ApiModelProperty(value = "是否有同比、环比指标，只对 /egret/queryDataPoints 接口有效", required = true)
    private List<String> addtype;

    public Long parseStartTimeTSOfMs() {
        LocalDateTime localDateTime = LocalDateTime.parse(start, Time.FORMATTER);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public Long parseEndTimeTSOfMs() {
        LocalDateTime localDateTime = LocalDateTime.parse(end, Time.FORMATTER);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
