package com.pinduoduo.mountain.api.controller.monitor.service;

import com.pinduoduo.mountain.api.controller.monitor.common.TornadoService;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.Instance;
import com.pinduoduo.mountain.repository.mysql.mountain.entity.monitor.LongTx;
import com.pinduoduo.mountain.repository.mysql.mountain.mapper.CoreInnodbTransactionMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class MonitorLongTxDataServiceImpl implements MonitorLongTxDataService {
    @Autowired
    private TornadoService tornadoService;
    ;
    @Autowired
    private CoreInnodbTransactionMapper coreInnodbTransactionMapper;
    ;
    @Autowired
    private MonitorMetaDataService monitorMetaDataService;

    @Override
    public List<LongTx> get(String instanceId, String userId, Long pageIndex, Long pageCnt, Date startTime, Date endTime) {
        Instance instance = monitorMetaDataService.getInstance(instanceId);
        if (instance == null) {
            return Collections.emptyList();
        }
        return coreInnodbTransactionMapper.select(instance.getIp(), instance.getPort().toString(), startTime, endTime, (pageIndex - 1) * pageCnt, pageCnt);
    }

    @Override
    public Pair<Boolean, String> killTx(String instanceId, List<String> threadIds) {
        return tornadoService.killTx(instanceId, threadIds);
    }
}
