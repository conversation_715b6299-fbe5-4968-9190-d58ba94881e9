package com.pinduoduo.mountain.api.controller.monitor.service;

import com.pinduoduo.mountain.api.controller.monitor.MetricData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MonitorTsData {
    MetricData data = new MetricData();
    MetricData dodData = new MetricData();
    MetricData wowData = new MetricData();
    MetricData backupInstanceData = new MetricData();

    public void setSeriesData(String type, MetricData data) {
        if (StringUtils.equals(type, "now")) {
            this.data = data;
        } else if (StringUtils.equals(type, "同比")) {
            this.dodData = data;
        } else if (StringUtils.equals(type, "环比")) {
            this.wowData = data;
        } else if (StringUtils.equals(type, "备机")) {
            this.backupInstanceData = data;
        } else {
            this.data = data;
        }
    }

    public MonitorTsData(MetricData data) {
        this.data = data;
    }
}