<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="FATAL" shutdownHook="disable">
    <Properties>
        <Property name="PID">????</Property>
        <Property name="APP_NAME">mountain-v2-api</Property>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xEx</Property>
        <Property name="LOG_LEVEL_PATTERN">%p</Property>
        <Property name="LOG_PATTERN">[$${env:HOST:-localhost}][${sys:app.name:-mountain-v2-api}][%d{yyyy-MM-dd HH:mm:ss.SSS}][${LOG_LEVEL_PATTERN}][${sys:PID}][%t][%-c{1.}] : [%X{logId}][%X{PTRACER-TRACE-UUID}][%X{PTRACER-TRFC}]%m%n${LOG_EXCEPTION_CONVERSION_WORD}
        </Property>
        <Property name="LOG_PATH">./logs</Property>
    </Properties>
    <DynamicThresholdFilter key="logLevel" defaultThreshold="ERROR" onMatch="ACCEPT" onMismatch="DENY">
        <KeyValuePair key="TRACE" value="TRACE"/>
        <KeyValuePair key="DEBUG" value="DEBUG"/>
        <KeyValuePair key="INFO" value="INFO"/>
        <KeyValuePair key="WARN" value="WARN"/>
        <KeyValuePair key="ERROR" value="ERROR"/>
        <KeyValuePair key="FATAL" value="FATAL"/>
        <KeyValuePair key="OFF" value="OFF"/>
    </DynamicThresholdFilter>
    <Appenders>

        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
        <Async name="AsyncConsole" bufferSize="32768" blocking="false">
            <AppenderRef ref="Console"/>
        </Async>
        <Kafka name="Kafka" topic="${sys:log4j2.kafka.topic}" syncSend="false">
            <Property name="bootstrap.servers">${sys:log4j2.kafka.bootstrap.servers}</Property>
            <Property name="acks">1</Property>
            <Property name="batch.size">32768</Property>
            <Property name="linger.ms">500</Property>
            <Property name="compression.type">snappy</Property>
            <Property name="retries">3</Property>
            <Property name="reconnect.backoff.ms">20000</Property>
            <Property name="retry.backoff.ms">20000</Property>
            <PatternLayout pattern="${LOG_PATTERN}" charset="utf-8"/>
        </Kafka>
        <Async name="AsyncKafka" bufferSize="32768" blocking="false">
            <AppenderRef ref="Kafka"/>
        </Async>

        <CatAppender name="CatAppender"/>
    </Appenders>
    <Loggers>
        <Logger name="org.apache.catalina.startup.DigesterFactory" level="error"/>
        <Logger name="org.apache.catalina.util.LifecycleBase" level="error"/>
        <Logger name="org.apache.coyote.http11.Http11NioProtocol" level="warn"/>
        <logger name="org.apache.sshd.common.util.SecurityUtils" level="warn"/>
        <Logger name="org.apache.tomcat.util.net.NioSelectorPool" level="warn"/>
        <Logger name="org.crsh.plugin" level="warn"/>
        <logger name="org.crsh.ssh" level="warn"/>
        <Logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="error"/>
        <Logger name="org.hibernate.validator.internal.util.Version" level="warn"/>
        <logger name="org.springframework.boot.actuate.autoconfigure.CrshAutoConfiguration" level="warn"/>
        <logger name="org.springframework.boot.actuate.endpoint.jmx" level="warn"/>
        <logger name="org.thymeleaf" level="warn"/>
        <logger name="com.pdd.data.tracker" level="warn"/>
        <!-- 只使用console打印kafka日志 -->
        <logger name="org.apache.kafka" additivity="false">
            <AppenderRef ref="AsyncConsole" level="warn"/>
            <AppenderRef ref="CatAppender"/>
        </logger>
        <Root level="info">
            <AppenderRef ref="AsyncConsole" level="warn"/>
            <AppenderRef ref="AsyncKafka" level="${sys:log4j2.kafka.level:-info}"/>
            <AppenderRef ref="CatAppender"/>
        </Root>
    </Loggers>
</Configuration>