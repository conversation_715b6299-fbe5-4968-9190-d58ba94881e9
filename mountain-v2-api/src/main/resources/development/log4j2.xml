<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" shutdownHook="disable">
    <Properties>
        <Property name="PID">????</Property>
        <Property name="APP_NAME">mountain-v2-api</Property>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xEx</Property>
        <Property name="LOG_LEVEL_PATTERN">%p</Property>
        <Property name="LOG_PATTERN">[$${env:HOST:-localhost}][${sys:app.name:-mountain-v2-api}][%d{yyyy-MM-dd HH:mm:ss.SSS}][${LOG_LEVEL_PATTERN}][${sys:PID}][%t][%-c{1.}] : [%X{logId}][%X{PTRACER-TRACE-UUID}][%X{PTRACER-TRFC}]%m%n${LOG_EXCEPTION_CONVERSION_WORD}
        </Property>
        <Property name="LOG_PATH">./logs</Property>
    </Properties>
    <DynamicThresholdFilter key="logLevel" defaultThreshold="ERROR" onMatch="ACCEPT" onMismatch="DENY">
        <KeyValuePair key="TRACE" value="TRACE"/>
        <KeyValuePair key="DEBUG" value="DEBUG"/>
        <KeyValuePair key="INFO" value="INFO"/>
        <KeyValuePair key="WARN" value="WARN"/>
        <KeyValuePair key="ERROR" value="ERROR"/>
        <KeyValuePair key="FATAL" value="FATAL"/>
        <KeyValuePair key="OFF" value="OFF"/>
    </DynamicThresholdFilter>
    <Appenders>

        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
        <Async name="AsyncConsole" bufferSize="16384" blocking="false">
            <AppenderRef ref="Console"/>
        </Async>
        <CatAppender name="CatAppender"/>
    </Appenders>
    <Loggers>
        <Logger name="org.apache.catalina.startup.DigesterFactory" level="error"/>
        <Logger name="org.apache.catalina.util.LifecycleBase" level="error"/>
        <Logger name="org.apache.coyote.http11.Http11NioProtocol" level="warn"/>
        <logger name="org.apache.sshd.common.util.SecurityUtils" level="warn"/>
        <Logger name="org.apache.tomcat.util.net.NioSelectorPool" level="warn"/>
        <Logger name="org.crsh.plugin" level="warn"/>
        <logger name="org.crsh.ssh" level="warn"/>
        <Logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="error"/>
        <Logger name="org.hibernate.validator.internal.util.Version" level="warn"/>
        <logger name="org.springframework.boot.actuate.autoconfigure.CrshAutoConfiguration" level="warn"/>
        <logger name="org.springframework.boot.actuate.endpoint.jmx" level="warn"/>
        <logger name="org.thymeleaf" level="warn"/>
        <Root level="info">
            <AppenderRef ref="AsyncConsole" level="${sys:log4j2.kafka.level:-info}"/>
            <AppenderRef ref="CatAppender"/>
        </Root>
    </Loggers>
</Configuration>