<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.teamflow.TFJobDOMapper">
    <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="job_id" jdbcType="BIGINT" property="jobId"/>
        <result column="work_id" jdbcType="BIGINT" property="workId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="closed_at" jdbcType="TIMESTAMP" property="closedAt"/>
        <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl"/>
        <result column="apply_user" jdbcType="VARCHAR" property="applyUser"/>
        <result column="order_source" jdbcType="VARCHAR" property="orderSource"/>
        <result column="worker_ip" jdbcType="VARCHAR" property="workerIp"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="app_version" jdbcType="VARCHAR" property="appVersion"/>
        <result column="last_exec_user" jdbcType="VARCHAR" property="lastExecUser"/>
        <result column="last_exec_desc" jdbcType="VARCHAR" property="lastExecDesc"/>
        <result column="last_exec_time" jdbcType="TIMESTAMP" property="lastExecTime"/>
        <result column="worker_thread_name" jdbcType="VARCHAR" property="workerThreadName"/>
        <result column="last_suc_step" jdbcType="VARCHAR" property="lastSucStep"/>
        <result column="next_exe_step" jdbcType="VARCHAR" property="nextExeStep"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
        <result column="last_exec_Type" jdbcType="VARCHAR" property="lastExecType"/>
        <result column="component_type" jdbcType="VARCHAR" property="componentType"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOWithBLOBs">
        <!--
        MyBatis 经典设计模式之一：BaseResultMap + 扩展BLOB字段的分离。
        可能有多个 SQL 查询都只需要用到 BaseResultMap（不带 BLOB 字段），比如列表查询、分页查询、轻量接口等。
        把 BLOB 字段放一起会导致每次都查大字段，没必要还浪费内存。所以它们应该按需加载，而不是每次都查。
        技术上完全可以把 BLOB 字段也写进 BaseResultMap。

        -->
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <result column="entity" jdbcType="LONGVARCHAR" property="entity"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="manual_close_message" jdbcType="LONGVARCHAR" property="manualCloseMessage"/>
        <result column="result_info" jdbcType="LONGVARCHAR" property="resultInfo"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, job_id, work_id, title, description, operation_type, status, closed_at, callback_url, apply_user,
        order_source, worker_ip, created_at, updated_at, app_version,
        last_exec_user, last_exec_desc, last_exec_time, worker_thread_name,
        last_suc_step, next_exe_step, env, last_exec_Type, component_type,
        tenant_name
    </sql>
    <sql id="Blob_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        entity, content, manual_close_message, result_info
    </sql>
    <select id="selectByExampleWithBLOBs"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOExample"
            resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tf_job
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from tf_job
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tf_job
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByJobId" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tf_job
        where job_id = #{jobId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_job
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_job
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insertTfJob" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOWithBLOBs"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_job (job_id,work_id,title, description,operation_type,
        status, closed_at, callback_url,apply_user,order_source, worker_ip, created_at,
        updated_at, app_version, last_exec_user,
        last_exec_desc, last_exec_time,
        worker_thread_name, last_suc_step, next_exe_step,env,
        last_exec_Type, component_type, tenant_name,
        entity, content, manual_close_message,
        result_info)
        values (#{jobId,jdbcType=BIGINT},#{workId,jdbcType=BIGINT},#{title,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{operationType,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR}, #{closedAt,jdbcType=TIMESTAMP}, #{callbackUrl,jdbcType=VARCHAR}, #{applyUser,jdbcType=VARCHAR},
        #{orderSource,jdbcType=VARCHAR},#{workerIp,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP},
        #{updatedAt,jdbcType=TIMESTAMP}, #{appVersion,jdbcType=VARCHAR}, #{lastExecUser,jdbcType=VARCHAR},
        #{lastExecDesc,jdbcType=VARCHAR}, #{lastExecTime,jdbcType=TIMESTAMP},
        #{workerThreadName,jdbcType=VARCHAR}, #{lastSucStep,jdbcType=VARCHAR}, #{nextExeStep,jdbcType=VARCHAR},
        #{env,jdbcType=VARCHAR}, #{lastExecType,jdbcType=VARCHAR}, #{componentType,jdbcType=VARCHAR}, #{tenantName,jdbcType=VARCHAR},
        #{entity,jdbcType=LONGVARCHAR}, #{content,jdbcType=LONGVARCHAR}, #{manualCloseMessage,jdbcType=LONGVARCHAR},
        #{resultInfo,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOWithBLOBs"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">
                job_id,
            </if>
            <if test="workId != null">
                work_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="operationType != null">
                operation_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="closedAt != null">
                closed_at,
            </if>
            <if test="callbackUrl != null">
                callback_url,
            </if>
            <if test="applyUser != null">
                apply_user,
            </if>
            <if test="orderSource != null">
                order_source,
            </if>
            <if test="workerIp != null">
                worker_ip,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="appVersion != null">
                app_version,
            </if>
            <if test="lastExecUser != null">
                last_exec_user,
            </if>
            <if test="lastExecDesc != null">
                last_exec_desc,
            </if>
            <if test="lastExecTime != null">
                last_exec_time,
            </if>
            <if test="workerThreadName != null">
                worker_thread_name,
            </if>
            <if test="lastSucStep != null">
                last_suc_step,
            </if>
            <if test="nextExeStep != null">
                next_exe_step,
            </if>
            <if test="env != null">
                env,
            </if>
            <if test="lastExecType != null">
                last_exec_Type,
            </if>
            <if test="componentType != null">
                component_type,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="entity != null">
                entity,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="manualCloseMessage != null">
                manual_close_message,
            </if>
            <if test="resultInfo != null">
                result_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">
                #{jobId,jdbcType=BIGINT},
            </if>
            <if test="workId != null">
                #{workId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                #{operationType,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="closedAt != null">
                #{closedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackUrl != null">
                #{callbackUrl,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="orderSource != null">
                #{orderSource,jdbcType=VARCHAR},
            </if>
            <if test="workerIp != null">
                #{workerIp,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersion != null">
                #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="lastExecUser != null">
                #{lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="lastExecDesc != null">
                #{lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastExecTime != null">
                #{lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workerThreadName != null">
                #{workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="lastSucStep != null">
                #{lastSucStep,jdbcType=VARCHAR},
            </if>
            <if test="nextExeStep != null">
                #{nextExeStep,jdbcType=VARCHAR},
            </if>
            <if test="env != null">
                #{env,jdbcType=VARCHAR},
            </if>
            <if test="lastExecType != null">
                #{lastExecType,jdbcType=VARCHAR},
            </if>
            <if test="componentType != null">
                #{componentType,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="entity != null">
                #{entity,jdbcType=LONGVARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="manualCloseMessage != null">
                #{manualCloseMessage,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultInfo != null">
                #{resultInfo,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from tf_job
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_job
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.jobId != null">
                job_id = #{record.jobId,jdbcType=BIGINT},
            </if>
            <if test="record.workId != null">
                work_id = #{record.workId,jdbcType=BIGINT},
            </if>
            <if test="record.title != null">
                title = #{record.title,jdbcType=VARCHAR},
            </if>
            <if test="record.description != null">
                description = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.operationType != null">
                operation_type = #{record.operationType,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.closedAt != null">
                closed_at = #{record.closedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.callbackUrl != null">
                callback_url = #{record.callbackUrl,jdbcType=VARCHAR},
            </if>
            <if test="record.applyUser != null">
                apply_user = #{record.applyUser,jdbcType=VARCHAR},
            </if>
            <if test="record.orderSource != null">
                order_source = #{record.orderSource,jdbcType=VARCHAR},
            </if>
            <if test="record.workerIp != null">
                worker_ip = #{record.workerIp,jdbcType=VARCHAR},
            </if>
            <if test="record.createdAt != null">
                created_at = #{record.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedAt != null">
                updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.appVersion != null">
                app_version = #{record.appVersion,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecUser != null">
                last_exec_user = #{record.lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecDesc != null">
                last_exec_desc = #{record.lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecTime != null">
                last_exec_time = #{record.lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.workerThreadName != null">
                worker_thread_name = #{record.workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="record.lastSucStep != null">
                last_suc_step = #{record.lastSucStep,jdbcType=VARCHAR},
            </if>
            <if test="record.nextExeStep != null">
                next_exe_step = #{record.nextExeStep,jdbcType=VARCHAR},
            </if>
            <if test="record.env != null">
                env = #{record.env,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecType != null">
                last_exec_Type = #{record.lastExecType,jdbcType=VARCHAR},
            </if>
            <if test="record.componentType != null">
                component_type = #{record.componentType,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantName != null">
                tenant_name = #{record.tenantName,jdbcType=VARCHAR},
            </if>
            <if test="record.entity != null">
                entity = #{record.entity,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.content != null">
                content = #{record.content,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.manualCloseMessage != null">
                manual_close_message = #{record.manualCloseMessage,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.resultInfo != null">
                result_info = #{record.resultInfo,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_job
        set id = #{record.id,jdbcType=BIGINT},
        job_id = #{record.jobId,jdbcType=BIGINT},
        work_id = #{record.workId,jdbcType=BIGINT},
        title = #{record.title,jdbcType=VARCHAR},
        description = #{record.description,jdbcType=VARCHAR},
        operation_type = #{record.operationType,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=VARCHAR},
        closed_at = #{record.closedAt,jdbcType=TIMESTAMP},
        callback_url = #{record.callbackUrl,jdbcType=VARCHAR},
        apply_user = #{record.applyUser,jdbcType=VARCHAR},
        order_source = #{record.orderSource,jdbcType=VARCHAR},
        worker_ip = #{record.workerIp,jdbcType=VARCHAR},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        app_version = #{record.appVersion,jdbcType=VARCHAR},
        last_exec_user = #{record.lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{record.lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{record.lastExecTime,jdbcType=TIMESTAMP},
        worker_thread_name = #{record.workerThreadName,jdbcType=VARCHAR},
        last_suc_step = #{record.lastSucStep,jdbcType=VARCHAR},
        next_exe_step = #{record.nextExeStep,jdbcType=VARCHAR},
        env = #{record.env,jdbcType=VARCHAR},
        last_exec_Type = #{record.lastExecType,jdbcType=VARCHAR},
        component_type = #{record.componentType,jdbcType=VARCHAR},
        tenant_name = #{record.tenantName,jdbcType=VARCHAR},
        entity = #{record.entity,jdbcType=LONGVARCHAR},
        content = #{record.content,jdbcType=LONGVARCHAR},
        manual_close_message = #{record.manualCloseMessage,jdbcType=LONGVARCHAR},
        result_info = #{record.resultInfo,jdbcType=LONGVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_job
        set id = #{record.id,jdbcType=BIGINT},
        job_id = #{record.jobId,jdbcType=BIGINT},
        work_id = #{record.workId,jdbcType=BIGINT},
        title = #{record.title,jdbcType=VARCHAR},
        description = #{record.description,jdbcType=VARCHAR},
        operation_type = #{record.operationType,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=VARCHAR},
        closed_at = #{record.closedAt,jdbcType=TIMESTAMP},
        callback_url = #{record.callbackUrl,jdbcType=VARCHAR},
        apply_user = #{record.applyUser,jdbcType=VARCHAR},
        order_source = #{record.orderSource,jdbcType=VARCHAR},
        worker_ip = #{record.workerIp,jdbcType=VARCHAR},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        app_version = #{record.appVersion,jdbcType=VARCHAR},
        last_exec_user = #{record.lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{record.lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{record.lastExecTime,jdbcType=TIMESTAMP},
        worker_thread_name = #{record.workerThreadName,jdbcType=VARCHAR},
        last_suc_step = #{record.lastSucStep,jdbcType=VARCHAR},
        next_exe_step = #{record.nextExeStep,jdbcType=VARCHAR},
        env = #{record.env,jdbcType=VARCHAR},
        last_exec_Type = #{record.lastExecType,jdbcType=VARCHAR},
        component_type = #{record.componentType,jdbcType=VARCHAR},
        tenant_name = #{record.tenantName,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_job
        <set>
            <if test="record.jobId != null">
                job_id = #{record.jobId,jdbcType=BIGINT},
            </if>
            <if test="record.workId != null">
                work_id = #{record.workId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="closedAt != null">
                closed_at = #{closedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackUrl != null">
                callback_url = #{callbackUrl,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null">
                apply_user = #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="orderSource != null">
                order_source = #{orderSource,jdbcType=VARCHAR},
            </if>
            <if test="workerIp != null">
                worker_ip = #{workerIp,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersion != null">
                app_version = #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="lastExecUser != null">
                last_exec_user = #{lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="lastExecDesc != null">
                last_exec_desc = #{lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastExecTime != null">
                last_exec_time = #{lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workerThreadName != null">
                worker_thread_name = #{workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="lastSucStep != null">
                last_suc_step = #{lastSucStep,jdbcType=VARCHAR},
            </if>
            <if test="nextExeStep != null">
                next_exe_step = #{nextExeStep,jdbcType=VARCHAR},
            </if>
            <if test="env != null">
                env = #{env,jdbcType=VARCHAR},
            </if>
            <if test="lastExecType != null">
                last_exec_Type = #{lastExecType,jdbcType=VARCHAR},
            </if>
            <if test="componentType != null">
                component_type = #{componentType,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                tenant_name = #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="entity != null">
                entity = #{entity,jdbcType=LONGVARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="manualCloseMessage != null">
                manual_close_message = #{manualCloseMessage,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultInfo != null">
                result_info = #{resultInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where work_id = #{workId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDOWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_job
        set title = #{title,jdbcType=VARCHAR},
        job_id = #{jobId,jdbcType=BIGINT},
        work_id = #{workId,jdbcType=BIGINT},
        description = #{description,jdbcType=VARCHAR},
        operation_type = #{operationType,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        closed_at = #{closedAt,jdbcType=TIMESTAMP},
        callback_url = #{callbackUrl,jdbcType=VARCHAR},
        apply_user = #{applyUser,jdbcType=VARCHAR},
        order_source = #{orderSource,jdbcType=VARCHAR},
        worker_ip = #{workerIp,jdbcType=VARCHAR},
        created_at = #{createdAt,jdbcType=TIMESTAMP},
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
        app_version = #{appVersion,jdbcType=VARCHAR},
        last_exec_user = #{lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{lastExecTime,jdbcType=TIMESTAMP},
        worker_thread_name = #{workerThreadName,jdbcType=VARCHAR},
        last_suc_step = #{lastSucStep,jdbcType=VARCHAR},
        next_exe_step = #{nextExeStep,jdbcType=VARCHAR},
        env = #{env,jdbcType=VARCHAR},
        last_exec_Type = #{lastExecType,jdbcType=VARCHAR},
        component_type = #{componentType,jdbcType=VARCHAR},
        tenant_name = #{tenantName,jdbcType=VARCHAR},
        entity = #{entity,jdbcType=LONGVARCHAR},
        content = #{content,jdbcType=LONGVARCHAR},
        manual_close_message = #{manualCloseMessage,jdbcType=LONGVARCHAR},
        result_info = #{resultInfo,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFJobDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_job
        set title = #{title,jdbcType=VARCHAR},
        job_id = #{jobId,jdbcType=BIGINT},
        work_id = #{workId,jdbcType=BIGINT},
        description = #{description,jdbcType=VARCHAR},
        operation_type = #{operationType,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        closed_at = #{closedAt,jdbcType=TIMESTAMP},
        callback_url = #{callbackUrl,jdbcType=VARCHAR},
        apply_user = #{applyUser,jdbcType=VARCHAR},
        order_source = #{orderSource,jdbcType=VARCHAR},
        worker_ip = #{workerIp,jdbcType=VARCHAR},
        created_at = #{createdAt,jdbcType=TIMESTAMP},
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
        app_version = #{appVersion,jdbcType=VARCHAR},
        last_exec_user = #{lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{lastExecTime,jdbcType=TIMESTAMP},
        worker_thread_name = #{workerThreadName,jdbcType=VARCHAR},
        last_suc_step = #{lastSucStep,jdbcType=VARCHAR},
        next_exe_step = #{nextExeStep,jdbcType=VARCHAR},
        env = #{env,jdbcType=VARCHAR},
        last_exec_Type = #{lastExecType,jdbcType=VARCHAR},
        component_type = #{componentType,jdbcType=VARCHAR},
        tenant_name = #{tenantName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateWorkerIpInBatch">
        update tf_job
        set
        <if test="ip != null">
            worker_ip = #{ip},
        </if>
        status = #{nextStatus}
        where status = #{preStatus}
        and job_id in
        <foreach item="item" collection="jobIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getJobsByStatus" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from tf_job
        where status = #{status} limit #{offset}, #{cnt}
    </select>

    <select id="getJobsByWorker" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from tf_job where worker_ip = #{ip} and status = #{status} limit #{offset}, #{cnt}
    </select>
</mapper>