<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.teamflow.TFOrderDOMapper">
    <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="business" jdbcType="VARCHAR" property="business"/>
        <result column="service" jdbcType="VARCHAR" property="service"/>
        <result column="work_id" jdbcType="BIGINT" property="workId"/>
        <result column="omega_id" jdbcType="BIGINT" property="omegaId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
        <result column="entity" jdbcType="LONGVARCHAR" property="entity"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="verifyCode" jdbcType="LONGVARCHAR" property="content"/>
        <result column="apply_user" jdbcType="VARCHAR" property="applyUser"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="closed_at" jdbcType="TIMESTAMP" property="closedAt"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, business, service, work_id, omega_id, title, description, operation_type, entity, content, verify_code, apply_user,
        status, closed_at, created_at, updated_at, is_deleted, env
    </sql>
    <select id="selectByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDOExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from tf_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from tf_order
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_order
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDOExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDO"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_order (business, service, work_id,
        omega_id, title, description,
        operation_type, entity, content, verify_code, apply_user, status,
        closed_at, created_at, updated_at,
        is_deleted, env)
        values (#{business,jdbcType=VARCHAR}, #{service,jdbcType=VARCHAR}, #{workId,jdbcType=BIGINT},
        #{omegaId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
        #{operationType,jdbcType=VARCHAR}, #{entity,jdbcType=LONGVARCHAR}, #{content,jdbcType=LONGVARCHAR},
                #{verifyCode,jdbcType=LONGVARCHAR}, #{applyUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
        #{closedAt,jdbcType=TIMESTAMP}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=BIT}, #{env,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDO"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="business != null">
                business,
            </if>
            <if test="service != null">
                service,
            </if>
            <if test="workId != null">
                work_id,
            </if>
            <if test="omegaId != null">
                omega_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="operationType != null">
                operation_type,
            </if>
            <if test="entity != null">
                entity,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="verifyCode != null">
                verify_code,
            </if>
            <if test="applyUser != null">
                apply_user,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="closedAt != null">
                closed_at,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="env != null">
                env,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="business != null">
                #{business,jdbcType=VARCHAR},
            </if>
            <if test="service != null">
                #{service,jdbcType=VARCHAR},
            </if>
            <if test="workId != null">
                #{workId,jdbcType=BIGINT},
            </if>
            <if test="omegaId != null">
                #{omegaId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                #{operationType,jdbcType=VARCHAR},
            </if>
            <if test="entity != null">
                #{entity,jdbcType=LONGVARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="verifyCode != null">
                #{verifyCode,jdbcType=LONGVARCHAR},
            </if>
            <if test="applyUser != null">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="closedAt != null">
                #{closedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BIT},
            </if>
            <if test="env != null">
                #{env,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDOExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from tf_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.business != null">
                business = #{record.business,jdbcType=VARCHAR},
            </if>
            <if test="record.service != null">
                service = #{record.service,jdbcType=VARCHAR},
            </if>
            <if test="record.workId != null">
                work_id = #{record.workId,jdbcType=BIGINT},
            </if>
            <if test="record.omegaId != null">
                omega_id = #{record.omegaId,jdbcType=BIGINT},
            </if>
            <if test="record.title != null">
                title = #{record.title,jdbcType=VARCHAR},
            </if>
            <if test="record.description != null">
                description = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.operationType != null">
                operation_type = #{record.operationType,jdbcType=VARCHAR},
            </if>
            <if test="record.entity != null">
                entity = #{record.entity,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.content != null">
                content = #{record.content,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.verifyCode != null">
                verify_code = #{record.verifyCode,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.applyUser != null">
                apply_user = #{record.applyUser,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.closedAt != null">
                closed_at = #{record.closedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createdAt != null">
                created_at = #{record.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedAt != null">
                updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.isDeleted != null">
                is_deleted = #{record.isDeleted,jdbcType=BIT},
            </if>
            <if test="record.env != null">
                env = #{record.env,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order
        set id = #{record.id,jdbcType=BIGINT},
        business = #{record.business,jdbcType=VARCHAR},
        service = #{record.service,jdbcType=VARCHAR},
        work_id = #{record.workId,jdbcType=BIGINT},
        omega_id = #{record.omegaId,jdbcType=BIGINT},
        title = #{record.title,jdbcType=VARCHAR},
        description = #{record.description,jdbcType=VARCHAR},
        operation_type = #{record.operationType,jdbcType=VARCHAR},
        entity = #{record.entity,jdbcType=LONGVARCHAR},
        content = #{record.content,jdbcType=LONGVARCHAR},
        verify_code = #{record.verifyCode,jdbcType=LONGVARCHAR},
        apply_user = #{record.applyUser,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=VARCHAR},
        closed_at = #{record.closedAt,jdbcType=TIMESTAMP},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        is_deleted = #{record.isDeleted,jdbcType=BIT},
        env = #{record.env,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order
        <set>
            <if test="business != null">
                business = #{business,jdbcType=VARCHAR},
            </if>
            <if test="service != null">
                service = #{service,jdbcType=VARCHAR},
            </if>
            <if test="workId != null">
                work_id = #{workId,jdbcType=BIGINT},
            </if>
            <if test="omegaId != null">
                omega_id = #{omegaId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=VARCHAR},
            </if>
            <if test="entity != null">
                entity = #{entity,jdbcType=LONGVARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="verifyCode != null">
                content = #{verifyCode,jdbcType=LONGVARCHAR},
            </if>
            <if test="applyUser != null">
                apply_user = #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="closedAt != null">
                closed_at = #{closedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIT},
            </if>
            <if test="env != null">
                env = #{env,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order
        set business = #{business,jdbcType=VARCHAR},
        service = #{service,jdbcType=VARCHAR},
        work_id = #{workId,jdbcType=BIGINT},
        omega_id = #{omegaId,jdbcType=BIGINT},
        title = #{title,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        operation_type = #{operationType,jdbcType=VARCHAR},
        entity = #{record.entity,jdbcType=LONGVARCHAR},
        content = #{record.content,jdbcType=LONGVARCHAR},
        verify_code = #{record.verifyCode,jdbcType=LONGVARCHAR},
        apply_user = #{applyUser,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        closed_at = #{closedAt,jdbcType=TIMESTAMP},
        created_at = #{createdAt,jdbcType=TIMESTAMP},
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=BIT},
        env = #{env,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  selectByMulExample-->
    <select id="selectByMulExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderDOExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from tf_order
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <!--  List<Map<String, Integer>> selectStatsByStatus(@Param("userName") String userName, @Param("env")String env);-->
    <select id="selectStatsByStatus" resultType="java.util.Map">
        select status, count(*) as v
        from tf_order
        <where>
            env = #{env}
            <if test="userName != null">
                and apply_user = #{userName}
            </if>
            <if test="!hasGlobalReadPerms">
                <trim prefix="and (" suffix=")" prefixOverrides="or">
                    <if test="businesses!=null">
                        business in
                        <foreach collection="businesses" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                    <if test="services!=null">
                        or service in
                        <foreach collection="services" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                </trim>
            </if>
            <if test="createBegin != null and createEnd != null">
                and created_at between #{createBegin,jdbcType=TIMESTAMP} and #{createEnd,jdbcType=TIMESTAMP}
            </if>
        </where>
        group by status
    </select>
    <!--  List<Map<String, Integer>> selectStatsByType(@Param("userName") String userName, @Param("env")String env);-->
    <select id="selectStatsByType" resultType="java.util.Map">
        select operation_type, count(*) as v
        from tf_order
        <where>
            env = #{env}
            <if test="userName != null">
                and apply_user = #{userName}
            </if>
            <if test="!hasGlobalReadPerms">
                <trim prefix="and (" suffix=")" prefixOverrides="or">
                    <if test="businesses!=null">
                        business in
                        <foreach collection="businesses" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                    <if test="services!=null">
                        or service in
                        <foreach collection="services" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                </trim>
            </if>
            <if test="createBegin != null and createEnd != null">
                and created_at between #{createBegin,jdbcType=TIMESTAMP} and #{createEnd,jdbcType=TIMESTAMP}
            </if>
        </where>
        group by operation_type
    </select>

    <select id="search" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tf_order
        <where>
            <if test="user != null">
                apply_user = #{user}
            </if>
            <if test="keyword != null and keyword != ''">
                and (title like #{keywordPattern} or operation_type = #{keyword} or work_id = #{keyword})
            </if>
            <if test="env != null">
                and env = #{env}
            </if>
            <if test="statuses != null">
                and status in
                <foreach collection="statuses" item="listItem" open="(" separator="," close=")">
                    #{listItem}
                </foreach>
            </if>
            <if test="opTypes != null">
                and operation_type in
                <foreach collection="opTypes" item="listItem" open="(" separator="," close=")">
                    #{listItem}
                </foreach>
            </if>
            <if test="!hasGlobalReadPerms || filterConditionByUser">
                <trim prefix="and (" suffix=")" prefixOverrides="or">
                    <if test="businesses!=null">
                        business in
                        <foreach collection="businesses" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                    <if test="services!=null">
                        or service in
                        <foreach collection="services" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                </trim>
            </if>
            <if test="createBegin != null and createEnd != null">
                and created_at between #{createdAt,jdbcType=TIMESTAMP} and #{createEnd,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by id desc
        <if test="limitOffset != null and limitCnt != null">
            limit #{limitOffset}, #{limitCnt}
        </if>
    </select>

    <select id="selectByWorkId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tf_order
        <where>
            <if test="workId != null">
                work_id = #{workId}
            </if>
        </where>
        order by id desc
        <if test="limitOffset != null and limitCnt != null">
            limit #{limitOffset}, #{limitCnt}
        </if>
    </select>

    <select id="searchCount" resultType="java.lang.Long">
        select
        count(*)
        from tf_order
        <where>
            <if test="user != null">
                apply_user = #{user}
            </if>
            <if test="keyword != null and keyword != ''">
                and (title like #{keywordPattern} or operation_type = #{keyword} or work_id = #{keyword})
            </if>
            <if test="env != null">
                and env = #{env}
            </if>
            <if test="statuses != null">
                and status in
                <foreach collection="statuses" item="listItem" open="(" separator="," close=")">
                    #{listItem}
                </foreach>
            </if>
            <if test="opTypes != null">
                and operation_type in
                <foreach collection="opTypes" item="listItem" open="(" separator="," close=")">
                    #{listItem}
                </foreach>
            </if>
            <if test="!hasGlobalReadPerms || filterConditionByUser">
                <trim prefix="and (" suffix=")" prefixOverrides="or">
                    <if test="businesses!=null">
                        business in
                        <foreach collection="businesses" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                    <if test="services!=null">
                        or service in
                        <foreach collection="services" item="listItem" open="(" separator="," close=")">
                            #{listItem}
                        </foreach>
                    </if>
                </trim>
            </if>
            <if test="createBegin != null and createEnd != null">
                and created_at between #{createdAt,jdbcType=TIMESTAMP} and #{createEnd,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>