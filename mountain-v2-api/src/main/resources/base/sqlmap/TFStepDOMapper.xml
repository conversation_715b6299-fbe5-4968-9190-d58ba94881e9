<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.teamflow.TFStepDOMapper">
    <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="step_id" jdbcType="BIGINT" property="stepId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="definition" jdbcType="VARCHAR" property="definition" />
        <result column="job_id" jdbcType="BIGINT" property="jobId" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="worker_ip" jdbcType="VARCHAR" property="workerIp" />
        <result column="started_at" jdbcType="TIMESTAMP" property="startedAt" />
        <result column="ended_at" jdbcType="TIMESTAMP" property="endedAt" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
        <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
        <result column="last_exec_user" jdbcType="VARCHAR" property="lastExecUser" />
        <result column="last_exec_desc" jdbcType="VARCHAR" property="lastExecDesc" />
        <result column="last_exec_time" jdbcType="TIMESTAMP" property="lastExecTime" />
        <result column="app_version_executed" jdbcType="VARCHAR" property="appVersionExecuted" />
        <result column="worker_thread_name" jdbcType="VARCHAR" property="workerThreadName" />
        <result column="next" jdbcType="VARCHAR" property="next" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <result column="input_params" jdbcType="LONGVARCHAR" property="inputParams" />
        <result column="output_params" jdbcType="LONGVARCHAR" property="outputParams" />
        <result column="result_info" jdbcType="LONGVARCHAR" property="resultInfo" />
        <result column="inputs" jdbcType="LONGVARCHAR" property="inputs" />
        <result column="outputs" jdbcType="LONGVARCHAR" property="outputs" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, step_id, job_id, name, definition, status, worker_ip, started_at,
        ended_at, created_at, updated_at, app_version, last_exec_user, last_exec_desc, last_exec_time,
        app_version_executed, worker_thread_name, next, type, tenant_name
    </sql>
    <sql id="Blob_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        input_params, output_params, result_info, inputs, outputs
    </sql>
    <select id="selectByExampleWithBLOBs" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOExample" resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from tf_step
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOExample" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from tf_step
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from tf_step
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_step
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_step
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOWithBLOBs" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_step (job_id, name, definition, status,
        worker_ip, started_at, ended_at,
        created_at, updated_at, app_version,
        last_exec_user, last_exec_desc, last_exec_time,
        app_version_executed, worker_thread_name, next,type, tenant_name,
        input_params, output_params,
        result_info, inputs, outputs
        )
        values (#{stepId,jdbcType=BIGINT},  #{name,jdbcType=VARCHAR}, #{definition,jdbcType=VARCHAR}, #{jobId,jdbcType=BIGINT},  #{status,jdbcType=VARCHAR},
        #{workerIp,jdbcType=VARCHAR}, #{startedAt,jdbcType=TIMESTAMP}, #{endedAt,jdbcType=TIMESTAMP},
        #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{appVersion,jdbcType=VARCHAR},
        #{lastExecUser,jdbcType=VARCHAR}, #{lastExecDesc,jdbcType=VARCHAR}, #{lastExecTime,jdbcType=TIMESTAMP},
        #{appVersionExecuted,jdbcType=VARCHAR},#{workerThreadName,jdbcType=VARCHAR}, #{next,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{tenantName,jdbcType=VARCHAR},
        #{inputParams,jdbcType=LONGVARCHAR}, #{outputParams,jdbcType=LONGVARCHAR},
        #{resultInfo,jdbcType=LONGVARCHAR}, #{inputs,jdbcType=LONGVARCHAR}, #{outputs,jdbcType=LONGVARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOWithBLOBs" useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stepId != null">
                step_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="definition != null">
                definition,
            </if>
            <if test="jobId != null">
                job_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="workerIp != null">
                worker_ip,
            </if>
            <if test="startedAt != null">
                started_at,
            </if>
            <if test="endedAt != null">
                ended_at,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="appVersion != null">
                app_version,
            </if>
            <if test="lastExecUser != null">
                last_exec_user,
            </if>
            <if test="lastExecDesc != null">
                last_exec_desc,
            </if>
            <if test="lastExecTime != null">
                last_exec_time,
            </if>
            <if test="appVersionExecuted != null">
                app_version_executed,
            </if>
            <if test="workerThreadName != null">
                worker_thread_name,
            </if>
            <if test="next != null">
                next,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="inputParams != null">
                input_params,
            </if>
            <if test="outputParams != null">
                output_params,
            </if>
            <if test="resultInfo != null">
                result_info,
            </if>
            <if test="inputs != null">
                inputs,
            </if>
            <if test="outputs != null">
                outputs,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stepId != null">
                #{stepId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="definition != null">
                #{definition,jdbcType=VARCHAR},
            </if>
            <if test="jobId != null">
                #{jobId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="workerIp != null">
                #{workerIp,jdbcType=VARCHAR},
            </if>
            <if test="startedAt != null">
                #{startedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="endedAt != null">
                #{endedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersion != null">
                #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="lastExecUser != null">
                #{lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="lastExecDesc != null">
                #{lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastExecTime != null">
                #{lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersionExecuted != null">
                #{appVersionExecuted,jdbcType=VARCHAR},
            </if>
            <if test="workerThreadName != null">
                #{workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="next != null">
                #{next,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="inputParams != null">
                #{inputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="outputParams != null">
                #{outputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultInfo != null">
                #{resultInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="inputs != null">
                #{inputs,jdbcType=LONGVARCHAR},
            </if>
            <if test="outputs != null">
                #{outputs,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOExample" resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from tf_step
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_step
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.stepId != null">
                step_id = #{record.stepId,jdbcType=BIGINT},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.definition != null">
                definition = #{record.definition,jdbcType=VARCHAR},
            </if>
            <if test="record.jobId != null">
                job_id = #{record.jobId,jdbcType=BIGINT},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.workerIp != null">
                worker_ip = #{record.workerIp,jdbcType=VARCHAR},
            </if>
            <if test="record.startedAt != null">
                started_at = #{record.startedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.endedAt != null">
                ended_at = #{record.endedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createdAt != null">
                created_at = #{record.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedAt != null">
                updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.appVersion != null">
                app_version = #{record.appVersion,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecUser != null">
                last_exec_user = #{record.lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecDesc != null">
                last_exec_desc = #{record.lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="record.lastExecTime != null">
                last_exec_time = #{record.lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.appVersionExecuted != null">
                app_version_executed = #{record.appVersionExecuted,jdbcType=VARCHAR},
            </if>
            <if test="record.workerThreadName != null">
                worker_thread_name = #{record.workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="record.next != null">
                next = #{record.next,jdbcType=VARCHAR},
            </if>
            <if test="record.type != null">
                type = #{record.type,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantName != null">
                tenant_name = #{record.tenantName,jdbcType=VARCHAR},
            </if>
            <if test="record.inputParams != null">
                input_params = #{record.inputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.outputParams != null">
                output_params = #{record.outputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.resultInfo != null">
                result_info = #{record.resultInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.inputs != null">
                inputs = #{record.inputs,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.outputs != null">
                outputs = #{record.outputs,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_step
        set id = #{record.id,jdbcType=BIGINT},
        step_id = #{record.setpId,jdbcType=BIGINT},
        name = #{record.name,jdbcType=VARCHAR},
        definition = #{record.definition,jdbcType=VARCHAR},
        job_id = #{record.jobId,jdbcType=BIGINT},
        status = #{record.status,jdbcType=VARCHAR},
        worker_ip = #{record.workerIp,jdbcType=VARCHAR},
        started_at = #{record.startedAt,jdbcType=TIMESTAMP},
        ended_at = #{record.endedAt,jdbcType=TIMESTAMP},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        app_version = #{record.appVersion,jdbcType=VARCHAR},
        last_exec_user = #{record.lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{record.lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{record.lastExecTime,jdbcType=TIMESTAMP},
        app_version_executed = #{record.appVersionExecuted,jdbcType=VARCHAR},
        worker_thread_name = #{record.workerThreadName,jdbcType=VARCHAR},
        next = #{record.next,jdbcType=VARCHAR},
        type = #{record.type,jdbcType=VARCHAR},
        tenant_name = #{record.tenantName,jdbcType=VARCHAR},
        input_params = #{record.inputParams,jdbcType=LONGVARCHAR},
        output_params = #{record.outputParams,jdbcType=LONGVARCHAR},
        result_info = #{record.resultInfo,jdbcType=LONGVARCHAR},
        inputs = #{record.inputs,jdbcType=LONGVARCHAR},
        outputs = #{record.outputs,jdbcType=LONGVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_step
        set id = #{record.id,jdbcType=BIGINT},
        step_id = #{record.setpId,jdbcType=BIGINT},
        name = #{record.name,jdbcType=VARCHAR},
        definition = #{record.definition,jdbcType=VARCHAR},
        job_id = #{record.jobId,jdbcType=BIGINT},
        status = #{record.status,jdbcType=VARCHAR},
        worker_ip = #{record.workerIp,jdbcType=VARCHAR},
        started_at = #{record.startedAt,jdbcType=TIMESTAMP},
        ended_at = #{record.endedAt,jdbcType=TIMESTAMP},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        app_version = #{record.appVersion,jdbcType=VARCHAR},
        last_exec_user = #{record.lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{record.lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{record.lastExecTime,jdbcType=TIMESTAMP},
        worker_thread_name = #{record.workerThreadName,jdbcType=VARCHAR},
        app_version_executed = #{record.appVersionExecuted,jdbcType=VARCHAR},
        next = #{record.next,jdbcType=VARCHAR},
        type = #{record.type,jdbcType=VARCHAR},
        tenant_name = #{record.tenantName,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_step
        <set>
            <if test="record.stepId != null">
                step_id = #{record.stepId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="definition != null">
                definition = #{definition,jdbcType=VARCHAR},
            </if>
            <if test="record.jobId != null">
                job_id = #{record.jobId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="workerIp != null">
                worker_ip = #{workerIp,jdbcType=VARCHAR},
            </if>
            <if test="startedAt != null">
                started_at = #{startedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="endedAt != null">
                ended_at = #{endedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersion != null">
                app_version = #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="lastExecUser != null">
                last_exec_user = #{lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="lastExecDesc != null">
                last_exec_desc = #{lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastExecTime != null">
                last_exec_time = #{lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersionExecuted != null">
                app_version_executed = #{appVersionExecuted,jdbcType=VARCHAR},
            </if>
            <if test="workerThreadName != null">
                worker_thread_name = #{workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="next != null">
                next = #{next,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                tenant_name = #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="inputParams != null">
                input_params = #{inputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="outputParams != null">
                output_params = #{outputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultInfo != null">
                result_info = #{resultInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="inputs != null">
                inputs = #{inputs,jdbcType=LONGVARCHAR},
            </if>
            <if test="outputs != null">
                outputs = #{outputs,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOWithBLOBs">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_step
        set step_id = #{record.setpId,jdbcType=BIGINT},
        name = #{record.name,jdbcType=VARCHAR},
        definition = #{record.definition,jdbcType=VARCHAR},
        job_id = #{record.jobId,jdbcType=BIGINT},
        status = #{status,jdbcType=VARCHAR},
        worker_ip = #{workerIp,jdbcType=VARCHAR},
        started_at = #{startedAt,jdbcType=TIMESTAMP},
        ended_at = #{endedAt,jdbcType=TIMESTAMP},
        created_at = #{createdAt,jdbcType=TIMESTAMP},
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
        app_version = #{appVersion,jdbcType=VARCHAR},
        last_exec_user = #{lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{lastExecTime,jdbcType=TIMESTAMP},
        app_version_executed = #{appVersionExecuted,jdbcType=VARCHAR},
        worker_thread_name = #{workerThreadName,jdbcType=VARCHAR},
        next = #{next,jdbcType=VARCHAR},
        type = #{type,jdbcType=VARCHAR},
        tenant_name = #{tenantName,jdbcType=VARCHAR},
        input_params = #{inputParams,jdbcType=LONGVARCHAR},
        output_params = #{outputParams,jdbcType=LONGVARCHAR},
        result_info = #{resultInfo,jdbcType=LONGVARCHAR},
        inputs = #{inputs,jdbcType=LONGVARCHAR},
        outputs = #{outputs,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_step
        set step_id = #{record.setpId,jdbcType=BIGINT},
        name = #{record.name,jdbcType=VARCHAR},
        definition = #{record.definition,jdbcType=VARCHAR},
        job_id = #{record.jobId,jdbcType=BIGINT},
        status = #{status,jdbcType=VARCHAR},
        worker_ip = #{workerIp,jdbcType=VARCHAR},
        started_at = #{startedAt,jdbcType=TIMESTAMP},
        ended_at = #{endedAt,jdbcType=TIMESTAMP},
        created_at = #{createdAt,jdbcType=TIMESTAMP},
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
        app_version = #{appVersion,jdbcType=VARCHAR},
        last_exec_user = #{lastExecUser,jdbcType=VARCHAR},
        last_exec_desc = #{lastExecDesc,jdbcType=VARCHAR},
        last_exec_time = #{lastExecTime,jdbcType=TIMESTAMP},
        app_version_executed = #{appVersionExecuted,jdbcType=VARCHAR},
        worker_thread_name = #{workerThreadName,jdbcType=VARCHAR},
        next = #{next,jdbcType=VARCHAR},
        type = #{type,jdbcType=VARCHAR},
        tenant_name = #{tenantName,jdbcType=VARCHAR},
        where id = #{id,jdbcType=BIGINT}
    </update>


    <!--    insertOnDuplicateKey-->
    <insert id="insertOnDuplicateKey"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepDOWithBLOBs">
        insert into tf_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stepId != null">
                step_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="definition != null">
                definition,
            </if>
            <if test="jobId != null">
                job_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="workerIp != null">
                worker_ip,
            </if>
            <if test="startedAt != null">
                started_at,
            </if>
            <if test="endedAt != null">
                ended_at,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="appVersion != null">
                app_version,
            </if>
            <if test="lastExecUser != null">
                last_exec_user,
            </if>
            <if test="lastExecDesc != null">
                last_exec_desc,
            </if>
            <if test="lastExecTime != null">
                last_exec_time,
            </if>
            <if test="appVersionExecuted != null">
                app_version_executed,
            </if>
            <if test="workerThreadName != null">
                worker_thread_name,
            </if>
            <if test="next != null">
                next,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="inputParams != null">
                input_params,
            </if>
            <if test="outputParams != null">
                output_params,
            </if>
            <if test="resultInfo != null">
                result_info,
            </if>
            <if test="inputs != null">
                inputs,
            </if>
            <if test="outputs != null">
                outputs,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stepId != null">
                #{stepId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="definition != null">
                #{definition,jdbcType=VARCHAR},
            </if>
            <if test="jobId != null">
                #{jobId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="workerIp != null">
                #{workerIp,jdbcType=VARCHAR},
            </if>
            <if test="startedAt != null">
                #{startedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="endedAt != null">
                #{endedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersion != null">
                #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="lastExecUser != null">
                #{lastExecUser,jdbcType=VARCHAR},
            </if>
            <if test="lastExecDesc != null">
                #{lastExecDesc,jdbcType=VARCHAR},
            </if>
            <if test="lastExecTime != null">
                #{lastExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="appVersionExecuted != null">
                #{appVersionExecuted,jdbcType=VARCHAR},
            </if>
            <if test="workerThreadName != null">
                #{workerThreadName,jdbcType=VARCHAR},
            </if>
            <if test="next != null">
                #{next,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="inputParams != null">
                #{inputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="outputParams != null">
                #{outputParams,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultInfo != null">
                #{resultInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="inputs != null">
                #{inputs,jdbcType=LONGVARCHAR},
            </if>
            <if test="outputs != null">
                #{outputs,jdbcType=LONGVARCHAR},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        status = #{status,jdbcType=VARCHAR},
        worker_ip = #{workerIp,jdbcType=VARCHAR},
        app_version = #{appVersion,jdbcType=VARCHAR},
        input_params = null,
        output_params = null,
        result_info = null
    </insert>

    <!--    updateStatus-->
    <update id="updateStatus">
        update tf_step
        set status = #{nextStatus}
        where status = #{preStatus}
        and step_id in
        <foreach item="item" collection="stepIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>