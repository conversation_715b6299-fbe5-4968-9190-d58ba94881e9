<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.teamflow.TFStepLogDOMapper">
  <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="job_id" jdbcType="BIGINT" property="jobId" />
    <result column="step_id" jdbcType="BIGINT" property="stepId" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="msg" jdbcType="LONGVARCHAR" property="msg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, job_id, step_id, step_name, created_at, updated_at, job_name
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    msg
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tf_step_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tf_step_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tf_step_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tf_step_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tf_step_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tf_step_log (job_id, step_id, step_name,
    created_at, updated_at, job_name, msg)
    values (#{jobId,jdbcType=BIGINT}, #{stepId,jdbcType=BIGINT}, #{stepName,jdbcType=VARCHAR},
    #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{jobName,jdbcType=VARCHAR}, #{msg,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tf_step_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jobId != null">
        job_id,
      </if>
      <if test="stepId != null">
        step_id,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="jobName != null">
        job_name,
      </if>
      <if test="msg != null">
        msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jobId != null">
        #{jobId,jdbcType=BIGINT},
      </if>
      <if test="stepId != null">
        #{stepId,jdbcType=BIGINT},
      </if>
      <if test="stepName != null">
        #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="jobName != null">
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tf_step_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tf_step_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.jobId != null">
        job_id = #{record.jobId,jdbcType=BIGINT},
      </if>
      <if test="record.stepId != null">
        step_id = #{record.stepId,jdbcType=BIGINT},
      </if>
      <if test="record.stepName != null">
        step_name = #{record.stepName,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.jobName != null">
        job_name = #{record.jobName,jdbcType=VARCHAR},
      </if>
      <if test="record.msg != null">
        msg = #{record.msg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tf_step_log
    set id = #{record.id,jdbcType=BIGINT},
    job_id = #{record.jobId,jdbcType=BIGINT},
    step_id = #{record.stepId,jdbcType=BIGINT},
    step_name = #{record.stepName,jdbcType=VARCHAR},
    created_at = #{record.createdAt,jdbcType=TIMESTAMP},
    updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
    job_name = #{record.jobName,jdbcType=VARCHAR},
    msg = #{record.msg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tf_step_log
    set id = #{record.id,jdbcType=BIGINT},
    job_id = #{record.jobId,jdbcType=BIGINT},
    step_id = #{record.stepId,jdbcType=BIGINT},
    step_name = #{record.stepName,jdbcType=VARCHAR},
    created_at = #{record.createdAt,jdbcType=TIMESTAMP},
    updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
    job_name = #{record.jobName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tf_step_log
    <set>
      <if test="jobId != null">
        job_id = #{jobId,jdbcType=BIGINT},
      </if>
      <if test="stepId != null">
        step_id = #{stepId,jdbcType=BIGINT},
      </if>
      <if test="stepName != null">
        step_name = #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="jobName != null">
        job_name = #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tf_step_log
    set job_id = #{jobId,jdbcType=BIGINT},
    step_id = #{stepId,jdbcType=BIGINT},
    step_name = #{stepName,jdbcType=VARCHAR},
    created_at = #{createdAt,jdbcType=TIMESTAMP},
    updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    job_name = #{jobName,jdbcType=VARCHAR}
    msg = #{msg,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFStepLogDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tf_step_log
    set job_id = #{jobId,jdbcType=BIGINT},
    step_id = #{stepId,jdbcType=BIGINT},
    step_name = #{stepName,jdbcType=VARCHAR},
    created_at = #{createdAt,jdbcType=TIMESTAMP},
    updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    job_name = #{jobName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>