<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.auth.AuthOpDOMapper">
  <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="op_name" jdbcType="VARCHAR" property="opName" />
    <result column="op_type" jdbcType="VARCHAR" property="opType" />
    <result column="op_desc" jdbcType="VARCHAR" property="opDesc" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="pk" jdbcType="BIGINT" property="pk" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, op_name, op_type, op_desc, created_at, updated_at, is_deleted, env, pk
  </sql>
  <select id="selectByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auth_op
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from auth_op
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from auth_op
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from auth_op
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into auth_op (op_name, op_type, op_desc,
    created_at, updated_at, is_deleted,
    env, pk)
    values (#{opName,jdbcType=VARCHAR}, #{opType,jdbcType=VARCHAR}, #{opDesc,jdbcType=VARCHAR},
    #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT},
    #{env,jdbcType=VARCHAR}, #{pk,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into auth_op
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="opName != null">
        op_name,
      </if>
      <if test="opType != null">
        op_type,
      </if>
      <if test="opDesc != null">
        op_desc,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="env != null">
        env,
      </if>
      <if test="pk != null">
        pk,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="opName != null">
        #{opName,jdbcType=VARCHAR},
      </if>
      <if test="opType != null">
        #{opType,jdbcType=VARCHAR},
      </if>
      <if test="opDesc != null">
        #{opDesc,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="env != null">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="pk != null">
        #{pk,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from auth_op
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_op
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.opName != null">
        op_name = #{record.opName,jdbcType=VARCHAR},
      </if>
      <if test="record.opType != null">
        op_type = #{record.opType,jdbcType=VARCHAR},
      </if>
      <if test="record.opDesc != null">
        op_desc = #{record.opDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.env != null">
        env = #{record.env,jdbcType=VARCHAR},
      </if>
      <if test="record.pk != null">
        pk = #{record.pk,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_op
    set id = #{record.id,jdbcType=BIGINT},
    op_name = #{record.opName,jdbcType=VARCHAR},
    op_type = #{record.opType,jdbcType=VARCHAR},
    op_desc = #{record.opDesc,jdbcType=VARCHAR},
    created_at = #{record.createdAt,jdbcType=TIMESTAMP},
    updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
    is_deleted = #{record.isDeleted,jdbcType=BIT},
    env = #{record.env,jdbcType=VARCHAR},
    pk = #{record.pk,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_op
    <set>
      <if test="opName != null">
        op_name = #{opName,jdbcType=VARCHAR},
      </if>
      <if test="opType != null">
        op_type = #{opType,jdbcType=VARCHAR},
      </if>
      <if test="opDesc != null">
        op_desc = #{opDesc,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="env != null">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="pk != null">
        pk = #{pk,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthOpDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_op
    set op_name = #{opName,jdbcType=VARCHAR},
    op_type = #{opType,jdbcType=VARCHAR},
    op_desc = #{opDesc,jdbcType=VARCHAR},
    created_at = #{createdAt,jdbcType=TIMESTAMP},
    updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    is_deleted = #{isDeleted,jdbcType=BIT},
    env = #{env,jdbcType=VARCHAR},
    pk = #{pk,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>