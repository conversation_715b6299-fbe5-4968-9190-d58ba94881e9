<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.teamflow.TFOrderEntityDOMapper">
    <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="work_id" jdbcType="BIGINT" property="workId"/>
        <result column="business" jdbcType="VARCHAR" property="business"/>
        <result column="service" jdbcType="VARCHAR" property="service"/>
        <result column="cluster" jdbcType="VARCHAR" property="cluster"/>
        <result column="cluster_set" jdbcType="VARCHAR" property="clusterSet"/>
        <result column="logic_db" jdbcType="VARCHAR" property="logicDb"/>
        <result column="logic_table" jdbcType="VARCHAR" property="logicTable"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="env" jdbcType="VARCHAR" property="env"/>
        <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
        <result column="entity_id" jdbcType="VARCHAR" property="entityId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, work_id, business, service, cluster, cluster_set, logic_db, logic_table, instance_id,
        created_at, updated_at, is_deleted, env, operation_type, entity_id
    </sql>
    <select id="selectByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDOExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from tf_order_entity
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from tf_order_entity
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_order_entity
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDOExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from tf_order_entity
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDO"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_order_entity (work_id, business, service,
        cluster, cluster_set, logic_db,
        logic_table, instance_id, created_at,
        updated_at, is_deleted, env,
        operation_type, entity_id)
        values (#{workId,jdbcType=BIGINT}, #{business,jdbcType=VARCHAR}, #{service,jdbcType=VARCHAR},
        #{cluster,jdbcType=VARCHAR}, #{clusterSet,jdbcType=VARCHAR}, #{logicDb,jdbcType=VARCHAR},
        #{logicTable,jdbcType=VARCHAR}, #{instanceId,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP},
        #{updatedAt,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT}, #{env,jdbcType=VARCHAR},
        #{operationType,jdbcType=VARCHAR}, #{entityId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDO"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into tf_order_entity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workId != null">
                work_id,
            </if>
            <if test="business != null">
                business,
            </if>
            <if test="service != null">
                service,
            </if>
            <if test="cluster != null">
                cluster,
            </if>
            <if test="clusterSet != null">
                cluster_set,
            </if>
            <if test="logicDb != null">
                logic_db,
            </if>
            <if test="logicTable != null">
                logic_table,
            </if>
            <if test="instanceId != null">
                instance_id,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="env != null">
                env,
            </if>
            <if test="operationType != null">
                operation_type,
            </if>
            <if test="entityId != null">
                entity_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workId != null">
                #{workId,jdbcType=BIGINT},
            </if>
            <if test="business != null">
                #{business,jdbcType=VARCHAR},
            </if>
            <if test="service != null">
                #{service,jdbcType=VARCHAR},
            </if>
            <if test="cluster != null">
                #{cluster,jdbcType=VARCHAR},
            </if>
            <if test="clusterSet != null">
                #{clusterSet,jdbcType=VARCHAR},
            </if>
            <if test="logicDb != null">
                #{logicDb,jdbcType=VARCHAR},
            </if>
            <if test="logicTable != null">
                #{logicTable,jdbcType=VARCHAR},
            </if>
            <if test="instanceId != null">
                #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BIT},
            </if>
            <if test="env != null">
                #{env,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                #{operationType,jdbcType=VARCHAR},
            </if>
            <if test="entityId != null">
                #{entityId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDOExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from tf_order_entity
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order_entity
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.workId != null">
                work_id = #{record.workId,jdbcType=BIGINT},
            </if>
            <if test="record.business != null">
                business = #{record.business,jdbcType=VARCHAR},
            </if>
            <if test="record.service != null">
                service = #{record.service,jdbcType=VARCHAR},
            </if>
            <if test="record.cluster != null">
                cluster = #{record.cluster,jdbcType=VARCHAR},
            </if>
            <if test="record.clusterSet != null">
                cluster_set = #{record.clusterSet,jdbcType=VARCHAR},
            </if>
            <if test="record.logicDb != null">
                logic_db = #{record.logicDb,jdbcType=VARCHAR},
            </if>
            <if test="record.logicTable != null">
                logic_table = #{record.logicTable,jdbcType=VARCHAR},
            </if>
            <if test="record.instanceId != null">
                instance_id = #{record.instanceId,jdbcType=VARCHAR},
            </if>
            <if test="record.createdAt != null">
                created_at = #{record.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedAt != null">
                updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.isDeleted != null">
                is_deleted = #{record.isDeleted,jdbcType=BIT},
            </if>
            <if test="record.env != null">
                env = #{record.env,jdbcType=VARCHAR},
            </if>
            <if test="record.operationType != null">
                operation_type = #{record.operationType,jdbcType=VARCHAR},
            </if>
            <if test="record.entityId != null">
                entity_id = #{record.entityId,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order_entity
        set id = #{record.id,jdbcType=BIGINT},
        work_id = #{record.workId,jdbcType=BIGINT},
        business = #{record.business,jdbcType=VARCHAR},
        service = #{record.service,jdbcType=VARCHAR},
        cluster = #{record.cluster,jdbcType=VARCHAR},
        cluster_set = #{record.clusterSet,jdbcType=VARCHAR},
        logic_db = #{record.logicDb,jdbcType=VARCHAR},
        logic_table = #{record.logicTable,jdbcType=VARCHAR},
        instance_id = #{record.instanceId,jdbcType=VARCHAR},
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        is_deleted = #{record.isDeleted,jdbcType=BIT},
        env = #{record.env,jdbcType=VARCHAR},
        operation_type = #{record.operationType,jdbcType=VARCHAR},
        entity_id = #{record.entityId,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order_entity
        <set>
            <if test="workId != null">
                work_id = #{workId,jdbcType=BIGINT},
            </if>
            <if test="business != null">
                business = #{business,jdbcType=VARCHAR},
            </if>
            <if test="service != null">
                service = #{service,jdbcType=VARCHAR},
            </if>
            <if test="cluster != null">
                cluster = #{cluster,jdbcType=VARCHAR},
            </if>
            <if test="clusterSet != null">
                cluster_set = #{clusterSet,jdbcType=VARCHAR},
            </if>
            <if test="logicDb != null">
                logic_db = #{logicDb,jdbcType=VARCHAR},
            </if>
            <if test="logicTable != null">
                logic_table = #{logicTable,jdbcType=VARCHAR},
            </if>
            <if test="instanceId != null">
                instance_id = #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIT},
            </if>
            <if test="env != null">
                env = #{env,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=VARCHAR},
            </if>
            <if test="entityId != null">
                entity_id = #{entityId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update tf_order_entity
        set work_id = #{workId,jdbcType=BIGINT},
        business = #{business,jdbcType=VARCHAR},
        service = #{service,jdbcType=VARCHAR},
        cluster = #{cluster,jdbcType=VARCHAR},
        cluster_set = #{clusterSet,jdbcType=VARCHAR},
        logic_db = #{logicDb,jdbcType=VARCHAR},
        logic_table = #{logicTable,jdbcType=VARCHAR},
        instance_id = #{instanceId,jdbcType=VARCHAR},
        created_at = #{createdAt,jdbcType=TIMESTAMP},
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=BIT},
        env = #{env,jdbcType=VARCHAR},
        operation_type = #{operationType,jdbcType=VARCHAR},
        entity_id = #{entityId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <insert id="insertBatch" keyColumn="id" keyProperty="id"
            parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.teamflow.TFOrderEntityDO"
            useGeneratedKeys="true">
        insert into tf_order_entity (
        work_id,
        operation_type,
        entity_id,
        business,
        service,
        cluster,
        cluster_set,
        logic_db,
        logic_table,
        instance_id,
        created_at,
        updated_at,
        is_deleted,
        env)
        values
        <foreach collection="records" item="item" separator=",">
            (
            <choose> <when test="item.workId != null">  #{item.workId,jdbcType=BIGINT} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.operationType != null">  #{item.operationType,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.entityId != null">  #{item.entityId,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.business != null">  #{item.business,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.service != null">  #{item.service,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.cluster != null">  #{item.cluster,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.clusterSet != null">  #{item.clusterSet,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.logicDb != null">  #{item.logicDb,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.logicTable != null">  #{item.logicTable,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.instanceId != null">  #{item.instanceId,jdbcType=VARCHAR} </when> <otherwise> ""</otherwise> </choose>,
            <choose> <when test="item.createdAt != null">  #{item.createdAt,jdbcType=TIMESTAMP} </when> <otherwise> now()</otherwise> </choose>,
            <choose> <when test="item.updatedAt != null">  #{item.updatedAt,jdbcType=TIMESTAMP} </when> <otherwise> now()</otherwise> </choose>,
            <choose> <when test="item.isDeleted != null">  #{item.isDeleted,jdbcType=BIT} </when> <otherwise> 0</otherwise> </choose>,
            <choose> <when test="item.env != null">  #{item.env,jdbcType=VARCHAR}</when> <otherwise>""</otherwise> </choose>
            )
        </foreach>
    </insert>
</mapper>