<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinduoduo.mountain.repository.mysql.mountain.mapper.auth.AuthMngDOMapper">
  <resultMap id="BaseResultMap" type="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="business" jdbcType="VARCHAR" property="business" />
    <result column="service" jdbcType="VARCHAR" property="service" />
    <result column="logic_database" jdbcType="VARCHAR" property="logicDatabase" />
    <result column="logical_cluster" jdbcType="VARCHAR" property="logicalCluster" />
    <result column="cluster" jdbcType="VARCHAR" property="cluster" />
    <result column="physical_cluster" jdbcType="VARCHAR" property="physicalCluster" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="node" jdbcType="VARCHAR" property="node" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="duration_secs" jdbcType="BIGINT" property="durationSecs" />
    <result column="started_at" jdbcType="TIMESTAMP" property="startedAt" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="pk" jdbcType="BIGINT" property="pk" />
    <result column="access_level" jdbcType="VARCHAR" property="accessLevel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, username, business, service, logic_database, logical_cluster, cluster, physical_cluster, 
    operation_name, description, created_at, updated_at, is_deleted, node, env, duration_secs, 
    started_at, user_type, pk, access_level
  </sql>
  <select id="selectByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auth_mng
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from auth_mng
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from auth_mng
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from auth_mng
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into auth_mng (username, business, service, 
      logic_database, logical_cluster, cluster, 
      physical_cluster, operation_name, description, 
      created_at, updated_at, is_deleted, 
      node, env, duration_secs, 
      started_at, user_type, pk, 
      access_level)
    values (#{username,jdbcType=VARCHAR}, #{business,jdbcType=VARCHAR}, #{service,jdbcType=VARCHAR}, 
      #{logicDatabase,jdbcType=VARCHAR}, #{logicalCluster,jdbcType=VARCHAR}, #{cluster,jdbcType=VARCHAR}, 
      #{physicalCluster,jdbcType=VARCHAR}, #{operationName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT}, 
      #{node,jdbcType=VARCHAR}, #{env,jdbcType=VARCHAR}, #{durationSecs,jdbcType=BIGINT}, 
      #{startedAt,jdbcType=TIMESTAMP}, #{userType,jdbcType=VARCHAR}, #{pk,jdbcType=BIGINT}, 
      #{accessLevel,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into auth_mng
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="business != null">
        business,
      </if>
      <if test="service != null">
        service,
      </if>
      <if test="logicDatabase != null">
        logic_database,
      </if>
      <if test="logicalCluster != null">
        logical_cluster,
      </if>
      <if test="cluster != null">
        cluster,
      </if>
      <if test="physicalCluster != null">
        physical_cluster,
      </if>
      <if test="operationName != null">
        operation_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="node != null">
        node,
      </if>
      <if test="env != null">
        env,
      </if>
      <if test="durationSecs != null">
        duration_secs,
      </if>
      <if test="startedAt != null">
        started_at,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="pk != null">
        pk,
      </if>
      <if test="accessLevel != null">
        access_level,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="business != null">
        #{business,jdbcType=VARCHAR},
      </if>
      <if test="service != null">
        #{service,jdbcType=VARCHAR},
      </if>
      <if test="logicDatabase != null">
        #{logicDatabase,jdbcType=VARCHAR},
      </if>
      <if test="logicalCluster != null">
        #{logicalCluster,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="physicalCluster != null">
        #{physicalCluster,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="node != null">
        #{node,jdbcType=VARCHAR},
      </if>
      <if test="env != null">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="durationSecs != null">
        #{durationSecs,jdbcType=BIGINT},
      </if>
      <if test="startedAt != null">
        #{startedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="pk != null">
        #{pk,jdbcType=BIGINT},
      </if>
      <if test="accessLevel != null">
        #{accessLevel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from auth_mng
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_mng
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.business != null">
        business = #{record.business,jdbcType=VARCHAR},
      </if>
      <if test="record.service != null">
        service = #{record.service,jdbcType=VARCHAR},
      </if>
      <if test="record.logicDatabase != null">
        logic_database = #{record.logicDatabase,jdbcType=VARCHAR},
      </if>
      <if test="record.logicalCluster != null">
        logical_cluster = #{record.logicalCluster,jdbcType=VARCHAR},
      </if>
      <if test="record.cluster != null">
        cluster = #{record.cluster,jdbcType=VARCHAR},
      </if>
      <if test="record.physicalCluster != null">
        physical_cluster = #{record.physicalCluster,jdbcType=VARCHAR},
      </if>
      <if test="record.operationName != null">
        operation_name = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.node != null">
        node = #{record.node,jdbcType=VARCHAR},
      </if>
      <if test="record.env != null">
        env = #{record.env,jdbcType=VARCHAR},
      </if>
      <if test="record.durationSecs != null">
        duration_secs = #{record.durationSecs,jdbcType=BIGINT},
      </if>
      <if test="record.startedAt != null">
        started_at = #{record.startedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.pk != null">
        pk = #{record.pk,jdbcType=BIGINT},
      </if>
      <if test="record.accessLevel != null">
        access_level = #{record.accessLevel,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_mng
    set id = #{record.id,jdbcType=BIGINT},
      username = #{record.username,jdbcType=VARCHAR},
      business = #{record.business,jdbcType=VARCHAR},
      service = #{record.service,jdbcType=VARCHAR},
      logic_database = #{record.logicDatabase,jdbcType=VARCHAR},
      logical_cluster = #{record.logicalCluster,jdbcType=VARCHAR},
      cluster = #{record.cluster,jdbcType=VARCHAR},
      physical_cluster = #{record.physicalCluster,jdbcType=VARCHAR},
      operation_name = #{record.operationName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      node = #{record.node,jdbcType=VARCHAR},
      env = #{record.env,jdbcType=VARCHAR},
      duration_secs = #{record.durationSecs,jdbcType=BIGINT},
      started_at = #{record.startedAt,jdbcType=TIMESTAMP},
      user_type = #{record.userType,jdbcType=VARCHAR},
      pk = #{record.pk,jdbcType=BIGINT},
      access_level = #{record.accessLevel,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_mng
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="business != null">
        business = #{business,jdbcType=VARCHAR},
      </if>
      <if test="service != null">
        service = #{service,jdbcType=VARCHAR},
      </if>
      <if test="logicDatabase != null">
        logic_database = #{logicDatabase,jdbcType=VARCHAR},
      </if>
      <if test="logicalCluster != null">
        logical_cluster = #{logicalCluster,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        cluster = #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="physicalCluster != null">
        physical_cluster = #{physicalCluster,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        operation_name = #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="node != null">
        node = #{node,jdbcType=VARCHAR},
      </if>
      <if test="env != null">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="durationSecs != null">
        duration_secs = #{durationSecs,jdbcType=BIGINT},
      </if>
      <if test="startedAt != null">
        started_at = #{startedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="pk != null">
        pk = #{pk,jdbcType=BIGINT},
      </if>
      <if test="accessLevel != null">
        access_level = #{accessLevel,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pinduoduo.mountain.repository.mysql.mountain.entity.auth.AuthMngDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update auth_mng
    set username = #{username,jdbcType=VARCHAR},
      business = #{business,jdbcType=VARCHAR},
      service = #{service,jdbcType=VARCHAR},
      logic_database = #{logicDatabase,jdbcType=VARCHAR},
      logical_cluster = #{logicalCluster,jdbcType=VARCHAR},
      cluster = #{cluster,jdbcType=VARCHAR},
      physical_cluster = #{physicalCluster,jdbcType=VARCHAR},
      operation_name = #{operationName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT},
      node = #{node,jdbcType=VARCHAR},
      env = #{env,jdbcType=VARCHAR},
      duration_secs = #{durationSecs,jdbcType=BIGINT},
      started_at = #{startedAt,jdbcType=TIMESTAMP},
      user_type = #{userType,jdbcType=VARCHAR},
      pk = #{pk,jdbcType=BIGINT},
      access_level = #{accessLevel,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>