name: <PERSON><PERSON><PERSON><PERSON>
desc: I'm a simple flow, just an example
type: Job
contents:
  - name: simpleStep
    type: step
    definition: simpleStep
#    isAsync: false
    inputs:
      iParam1: "static params,to be implemented"
    outputs:
      oParam1: "static params,to be implemented"
    next: nextSimpleStep

  - name: nextSimpleStep
    type: step
    definition: nextSimpleStep
#    isAsync: false
    inputs:
      iParam1: "static params,to be implemented"
    outputs:
      oParam1: "static params,to be implemented"
    next: sleep

  - name: sleep
    type: step
    definition: sleepStep
    #    isAsync: false
    inputs:
      tf_step_sleep_seconds: 1
      iParam1: "static params,to be implemented"
    outputs:
      oParam1: "static params,to be implemented"
    next: end