name: mysqlCreateDB
desc: mysql create database order type
type: Job
contents:
  - name: genCreateShardDbPlan
    type: step
    definition: genCreateShardDbPlan
    inputs:

    outputs:
      instance_db_plan: $instance_db_plan
    next: createPhysicalDb

  - name: createPhysicalDb
    type: step
    definition: createPhysicalDb
    inputs:
      instance_db_plan: $instance_db_plan
    outputs:

    next: createLogicDbMetadata

  - name: createLogicDbMetadata
    type: step
    definition: createLogicDbMetadata
    inputs:
      instance_db_plan: $instance_db_plan
    outputs:
      logic_db_id: $logic_db_id
    next: createKeychain

  - name: createKeychain
    type: step
    definition: createKeychain
    inputs:
      logic_db_id: $logic_db_id
      instance_db_plan: $instance_db_plan
    outputs:

    next: end