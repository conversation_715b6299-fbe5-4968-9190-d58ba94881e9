name: mysqlUpgrade
desc: mysql upgrade order type
type: Job
contents:
  - name: mysqlUpgradePreCheck
    type: step
    definition: mysqlUpgradePreCheck
    inputs:
    outputs:
      preCheckResult: $preCheckResult
    next: createUpgradePostServerBody

  - name: createUpgradePostServerBody
    type: step
    definition: createUpgradePostServerBody
    inputs:
    outputs:
      requirementBody: $requirementBody
      totalInstanceIds: $totalInstanceIds
      planId: $planId
    next: createUpgradeCDBPanguWork

  - name: createUpgradeCDBPanguWork
    type: step
    definition: createUpgradeCDBPanguWork
    inputs:
      planId: $planId
      requirementBody: $requirementBody
      totalInstanceIds: $totalInstanceIds
    outputs:
      planId: $planId
    next: getUpgradePlanResult

  - name: getUpgradePlanResult
    type: step
    definition: getUpgradePlanResult
    inputs:
      planId: $planId
      totalInstanceIds: $totalInstanceIds
    outputs:
      jobResult: $jobResult
    next: syncUpgradeMetaData

  - name: syncUpgradeMetaData
    type: step
    definition: syncUpgradeMetaData
    inputs:
      jobResult: $jobResult
      requirementBody: $requirementBody
    outputs:
      showInfos: $showInfos
    next: end