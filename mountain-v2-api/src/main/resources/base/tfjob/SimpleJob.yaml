name: SimpleJob
desc: I'm a simple flow, just an example
type: Job
contents:
  - name: simpleStep
    type: step
    definition: simpleStep
#    isAsync: false
    inputs:
      ParamStr: static params,to be implemented
      ParamLong: 123
    outputs:
      oParam1: "static params,to be implemented"
      innerVar: $innerVarTest
      instanceId: $instance_id
    next: nextSimpleStep

  - name: nextSimpleStep
    type: step
    definition: nextSimpleStep
#    isAsync: false
    inputs:
      iParam1: "static params,to be implemented"
      iParma2: $oParam1
      innerVar: $innerVar
      instanceId: $instanceId
    outputs:
      oParam1: "static params,to be implemented"
    next: sleep

  - name: sleep
    type: step
    definition: sleepStep
    #    isAsync: false
    inputs:
      tf_step_sleep_seconds: 1
      iParam1: "static params,to be implemented"
    outputs:
      oParam1: "static params,to be implemented"
    next: end