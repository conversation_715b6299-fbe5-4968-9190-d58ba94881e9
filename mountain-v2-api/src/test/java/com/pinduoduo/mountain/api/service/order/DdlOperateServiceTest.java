package com.pinduoduo.mountain.api.service.order;

import com.pinduoduo.mountain.repository.mysql.mountain.entity.OrderDdlSub;
import org.junit.jupiter.api.*;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

class DdlOperateServiceTest {

    private DdlOperateService ddlOperateService;

    @BeforeEach
    void setUp() {
        ddlOperateService = new DdlOperateService(
                null, null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null, null, null, null,
                null, null);
    }

    @Test
    void testNoDuplicateData() {
        String ip = "**************";
        int port = 3306;
        String database = "htd_test";
        String table = "htd_test_uk_duplicate_data";
        List<String> columns = Arrays.asList("is_deleted", "varchar_256");

        OrderDdlSub orderDdlSub = OrderDdlSub.builder().workId(1234L).id(1234L).build();
        boolean result = ddlOperateService.columnHasDuplicateData(orderDdlSub, ip, port, database, table, columns);

        assertFalse(result);
    }
}
